{"discovery_summary": {"total_videos": 673, "classes_found": 3, "model_classes": ["doctor", "my_back_hurts", "my_mouth_is_dry", "phone", "pillow", "water", "where_does_it_hurt"]}, "discovered_videos": [{"video_path": "data/TEST SET/doctor 13.mp4", "relative_path": "TEST SET/doctor 13.mp4", "filename": "doctor 13.mp4", "directory": "data/TEST SET", "source_directory": "TEST SET", "matched_class": "doctor", "file_info": {"size_bytes": 1806396, "size_mb": 1.72, "exists": true}}, {"video_path": "data/TEST SET/doctor 20.mp4", "relative_path": "TEST SET/doctor 20.mp4", "filename": "doctor 20.mp4", "directory": "data/TEST SET", "source_directory": "TEST SET", "matched_class": "doctor", "file_info": {"size_bytes": 2656537, "size_mb": 2.53, "exists": true}}, {"video_path": "data/TEST SET/doctor 3.mp4", "relative_path": "TEST SET/doctor 3.mp4", "filename": "doctor 3.mp4", "directory": "data/TEST SET", "source_directory": "TEST SET", "matched_class": "doctor", "file_info": {"size_bytes": 3279054, "size_mb": 3.13, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 1.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 1.mp4", "filename": "doctor 1.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 479401, "size_mb": 0.46, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 10.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 10.mp4", "filename": "doctor 10.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2249326, "size_mb": 2.15, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 12.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 12.mp4", "filename": "doctor 12.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2924398, "size_mb": 2.79, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 14.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 14.mp4", "filename": "doctor 14.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2249342, "size_mb": 2.15, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 15.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 15.mp4", "filename": "doctor 15.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3181498, "size_mb": 3.03, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 16.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 16.mp4", "filename": "doctor 16.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 4195401, "size_mb": 4.0, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 17.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 17.mp4", "filename": "doctor 17.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3201751, "size_mb": 3.05, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 18.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 18.mp4", "filename": "doctor 18.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2344473, "size_mb": 2.24, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 19.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 19.mp4", "filename": "doctor 19.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1860334, "size_mb": 1.77, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 4.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 4.mp4", "filename": "doctor 4.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2358969, "size_mb": 2.25, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 5.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 5.mp4", "filename": "doctor 5.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3809261, "size_mb": 3.63, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 6.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 6.mp4", "filename": "doctor 6.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1654288, "size_mb": 1.58, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 7.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 7.mp4", "filename": "doctor 7.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2566809, "size_mb": 2.45, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 8.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 8.mp4", "filename": "doctor 8.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2325810, "size_mb": 2.22, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/doctor 9.mp4", "relative_path": "TRAINING SET 2.9.25/doctor 9.mp4", "filename": "doctor 9.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2475472, "size_mb": 2.36, "exists": true}}, {"video_path": "data/VAL SET/doctor 11.mp4", "relative_path": "VAL SET/doctor 11.mp4", "filename": "doctor 11.mp4", "directory": "data/VAL SET", "source_directory": "VAL SET", "matched_class": "doctor", "file_info": {"size_bytes": 2071593, "size_mb": 1.98, "exists": true}}, {"video_path": "data/VAL SET/doctor 2.mp4", "relative_path": "VAL SET/doctor 2.mp4", "filename": "doctor 2.mp4", "directory": "data/VAL SET", "source_directory": "VAL SET", "matched_class": "doctor", "file_info": {"size_bytes": 2987614, "size_mb": 2.85, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy 2.mp4", "filename": "doctor_female_18-39_caucasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1685967, "size_mb": 1.61, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy 3.mp4", "filename": "doctor_female_18-39_caucasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1685967, "size_mb": 1.61, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1 copy.mp4", "filename": "doctor_female_18-39_caucasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1685967, "size_mb": 1.61, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 1.mp4", "filename": "doctor_female_18-39_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1685967, "size_mb": 1.61, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy 2.mp4", "filename": "doctor_female_18-39_caucasian_video 2 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1526936, "size_mb": 1.46, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy 3.mp4", "filename": "doctor_female_18-39_caucasian_video 2 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1526936, "size_mb": 1.46, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2 copy.mp4", "filename": "doctor_female_18-39_caucasian_video 2 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1526936, "size_mb": 1.46, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 2.mp4", "filename": "doctor_female_18-39_caucasian_video 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1526936, "size_mb": 1.46, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy 2.mp4", "filename": "doctor_female_18-39_caucasian_video 3 (1) copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy 3.mp4", "filename": "doctor_female_18-39_caucasian_video 3 (1) copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1) copy.mp4", "filename": "doctor_female_18-39_caucasian_video 3 (1) copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1).mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 (1).mp4", "filename": "doctor_female_18-39_caucasian_video 3 (1).mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy 2.mp4", "filename": "doctor_female_18-39_caucasian_video 3 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy 3.mp4", "filename": "doctor_female_18-39_caucasian_video 3 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3 copy.mp4", "filename": "doctor_female_18-39_caucasian_video 3 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_female_18-39_caucasian_video 3.mp4", "filename": "doctor_female_18-39_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1424090, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 888875, "size_mb": 0.85, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 888875, "size_mb": 0.85, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 888875, "size_mb": 0.85, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 1.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 888875, "size_mb": 0.85, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 2.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 3.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 (1) copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1).mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 (1).mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 (1).mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 2.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 903013, "size_mb": 0.86, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 963423, "size_mb": 0.92, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 963423, "size_mb": 0.92, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 963423, "size_mb": 0.92, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_male_speaker 1_65plus_caucasian_video 3.mp4", "filename": "doctor_male_speaker 1_65plus_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 963423, "size_mb": 0.92, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy 2.mp4", "filename": "doctor_speaker1_female_65plus_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1035864, "size_mb": 0.99, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy 3.mp4", "filename": "doctor_speaker1_female_65plus_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1035864, "size_mb": 0.99, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1 copy.mp4", "filename": "doctor_speaker1_female_65plus_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1035864, "size_mb": 0.99, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_female_65plus_video 1.mp4", "filename": "doctor_speaker1_female_65plus_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1035864, "size_mb": 0.99, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy 2.mp4", "filename": "doctor_speaker1_male_65plus_caucasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1567292, "size_mb": 1.49, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy 3.mp4", "filename": "doctor_speaker1_male_65plus_caucasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1567292, "size_mb": 1.49, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1 copy.mp4", "filename": "doctor_speaker1_male_65plus_caucasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1567292, "size_mb": 1.49, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/doctor_speaker1_male_65plus_caucasian_video 1.mp4", "filename": "doctor_speaker1_male_65plus_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1567292, "size_mb": 1.49, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy 2.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 2 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2371775, "size_mb": 2.26, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy 3.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 2 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2371775, "size_mb": 2.26, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2 copy.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 2 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2371775, "size_mb": 2.26, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 2.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2371775, "size_mb": 2.26, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 2.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 3.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 (1) copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1).mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 (1).mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 (1).mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy 2.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy 3.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3 copy.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_female_18-39_caucasian_video 3.mp4", "filename": "my_mouth_is_dry_female_18-39_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 2439209, "size_mb": 2.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 2.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 3.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1) copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1).mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1).mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  (1).mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 2.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 3.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2  copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2 .mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2 .mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 2 .mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1396962, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 2.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1423514, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 3.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1423514, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy.mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3  copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1423514, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3 .mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3 .mp4", "filename": "my_mouth_is_dry_male_speaker 1_65plus_caucasian_video 3 .mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1423514, "size_mb": 1.36, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy 2.mp4", "filename": "my_mouth_is_dry_speaker1_female_65plus_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1384398, "size_mb": 1.32, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy 3.mp4", "filename": "my_mouth_is_dry_speaker1_female_65plus_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1384398, "size_mb": 1.32, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1 copy.mp4", "filename": "my_mouth_is_dry_speaker1_female_65plus_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1384398, "size_mb": 1.32, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker1_female_65plus_video 1.mp4", "filename": "my_mouth_is_dry_speaker1_female_65plus_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1384398, "size_mb": 1.32, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 2.mp4", "filename": "my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1998835, "size_mb": 1.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 3.mp4", "filename": "my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1998835, "size_mb": 1.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy.mp4", "filename": "my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1998835, "size_mb": 1.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1.mp4", "filename": "my_mouth_is_dry_speaker2_male_65plus_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "doctor", "file_info": {"size_bytes": 1998835, "size_mb": 1.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 2.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (1) 2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 955081, "size_mb": 0.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 3.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (1) 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 955081, "size_mb": 0.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1) 4.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (1) 4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 955081, "size_mb": 0.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (1).mp4", "filename": "my mouth is dry_female_18-39_asian_video  (1).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 955081, "size_mb": 0.91, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 2.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (2) 2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791533, "size_mb": 0.75, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 3.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (2) 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791533, "size_mb": 0.75, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2) 4.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (2) 4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791533, "size_mb": 0.75, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (2).mp4", "filename": "my mouth is dry_female_18-39_asian_video  (2).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791533, "size_mb": 0.75, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 2.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (3) 2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 3.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (3) 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3) 4.mp4", "filename": "my mouth is dry_female_18-39_asian_video  (3) 4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  (3).mp4", "filename": "my mouth is dry_female_18-39_asian_video  (3).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  2.mp4", "filename": "my mouth is dry_female_18-39_asian_video  2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  3.mp4", "filename": "my mouth is dry_female_18-39_asian_video  3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video  4.mp4", "filename": "my mouth is dry_female_18-39_asian_video  4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_asian_video .mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_asian_video .mp4", "filename": "my mouth is dry_female_18-39_asian_video .mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 791949, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_cauasian_video  (1).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_cauasian_video  (1).mp4", "filename": "my mouth is dry_female_18-39_cauasian_video  (1).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 733658, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_cauasian_video 1 (1).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_cauasian_video 1 (1).mp4", "filename": "my mouth is dry_female_18-39_cauasian_video 1 (1).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 746076, "size_mb": 0.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1) 2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1) 2.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  (1) 2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 733658, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1) 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1) 3.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  (1) 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 733658, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  (1).mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  (1).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 733658, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  2.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 651928, "size_mb": 0.62, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  3.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 651928, "size_mb": 0.62, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video  4.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video  4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 651928, "size_mb": 0.62, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video .mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video .mp4", "filename": "my mouth is dry_female_18-39_caucasian_video .mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 651928, "size_mb": 0.62, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1) 2.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1) 2.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 1 (1) 2.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 746076, "size_mb": 0.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1) 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1) 3.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 1 (1) 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 746076, "size_mb": 0.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1).mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 1 (1).mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 1 (1).mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 746076, "size_mb": 0.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 10.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 10.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 10.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 796391, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 4.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 714070, "size_mb": 0.68, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 6.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 6.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 6.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 979623, "size_mb": 0.93, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 7.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 7.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 7.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 796845, "size_mb": 0.76, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 8.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 8.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 8.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 979623, "size_mb": 0.93, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 9.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_female_18-39_caucasian_video 9.mp4", "filename": "my mouth is dry_female_18-39_caucasian_video 9.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 830915, "size_mb": 0.79, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 1.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 10.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 10.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 10.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 2 .mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 2 .mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 2 .mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1074861, "size_mb": 1.03, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 3.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 4.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 4.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 4.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 5.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 5.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 5.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1074861, "size_mb": 1.03, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 6.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 6.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 6.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1074861, "size_mb": 1.03, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 7.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 7.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 7.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1074861, "size_mb": 1.03, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 8.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 8.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 8.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 9.mp4", "relative_path": "extra videos 22.9.25/my mouth is dry_male_65plus_caucasian_video 9.mp4", "filename": "my mouth is dry_male_65plus_caucasian_video 9.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 845186, "size_mb": 0.81, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250715T190633_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250715T190633_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250715T190633_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13456, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T040655_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T040655_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250723T040655_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15497, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T040729_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T040729_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250723T040729_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12599, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T062930_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250723T062930_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250723T062930_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12437, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020039_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020039_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250724T020039_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13632, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020052_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020052_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250724T020052_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13285, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020102_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250724T020102_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250724T020102_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12876, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051856_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051856_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T051856_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16480, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051928_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051928_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T051928_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 14960, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051955_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T051955_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T051955_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 14142, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064504_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064504_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T064504_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11883, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064518_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064518_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T064518_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11885, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064535_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250731T064535_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250731T064535_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11961, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250802T100024_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250802T100024_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250802T100024_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15111, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250824T025344_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250824T025344_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250824T025344_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16349, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T052540_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T052540_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250827T052540_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16957, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T052630_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T052630_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250827T052630_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15521, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T060227_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/doctor__useruser01__18to39__male__not_specified__20250827T060227_topmid_96x64_processed.mp4", "filename": "doctor__useruser01__18to39__male__not_specified__20250827T060227_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16001, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T134852_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T134852_topmid_96x64_processed.mp4", "filename": "my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T134852_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19621, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140557_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140557_topmid_96x64_processed.mp4", "filename": "my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140557_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19978, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140614_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140614_topmid_96x64_processed.mp4", "filename": "my_mouth_is_dry__useruser01__18to39__male__not_specified__20250819T140614_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 20873, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250820T091523_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250820T091523_topmid_96x64_processed.mp4", "filename": "my_mouth_is_dry__useruser01__18to39__male__not_specified__20250820T091523_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 17248, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250827T060851_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/my_mouth_is_dry__useruser01__18to39__male__not_specified__20250827T060851_topmid_96x64_processed.mp4", "filename": "my_mouth_is_dry__useruser01__18to39__male__not_specified__20250827T060851_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "doctor", "file_info": {"size_bytes": 18158, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 1.MOV", "filename": "doctor_speaker1._video 1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 5530598, "size_mb": 5.27, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 2.MOV", "filename": "doctor_speaker1._video 2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 5976527, "size_mb": 5.7, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 3.MOV", "filename": "doctor_speaker1._video 3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 6004342, "size_mb": 5.73, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 4.MOV", "filename": "doctor_speaker1._video 4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 5932502, "size_mb": 5.66, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/doctor_speaker1._video 5.MOV", "filename": "doctor_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 5041744, "size_mb": 4.81, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_1.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_1.mp4", "filename": "doctor_additional_1.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16957, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_10.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_10.mp4", "filename": "doctor_additional_10.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15521, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_11.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_11.mp4", "filename": "doctor_additional_11.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11885, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_12.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_12.mp4", "filename": "doctor_additional_12.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15497, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_13.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_13.mp4", "filename": "doctor_additional_13.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13456, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_14.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_14.mp4", "filename": "doctor_additional_14.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12876, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_15.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_15.mp4", "filename": "doctor_additional_15.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11883, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_16.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_16.mp4", "filename": "doctor_additional_16.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 14960, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_17.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_17.mp4", "filename": "doctor_additional_17.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16349, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_18.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_18.mp4", "filename": "doctor_additional_18.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12599, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_2.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_2.mp4", "filename": "doctor_additional_2.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 14142, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_3.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_3.mp4", "filename": "doctor_additional_3.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16001, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_4.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_4.mp4", "filename": "doctor_additional_4.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13285, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_5.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_5.mp4", "filename": "doctor_additional_5.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 11961, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_6.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_6.mp4", "filename": "doctor_additional_6.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 16480, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_7.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_7.mp4", "filename": "doctor_additional_7.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 12437, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_8.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_8.mp4", "filename": "doctor_additional_8.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 15111, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_9.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_additional_9.mp4", "filename": "doctor_additional_9.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 13632, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 1.MOV", "filename": "doctor_speaker1._video 1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 5530598, "size_mb": 5.27, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 2.MOV", "filename": "doctor_speaker1._video 2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 5976527, "size_mb": 5.7, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 3.MOV", "filename": "doctor_speaker1._video 3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6004342, "size_mb": 5.73, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 4.MOV", "filename": "doctor_speaker1._video 4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 5932502, "size_mb": 5.66, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/doctor_speaker1._video 5.MOV", "filename": "doctor_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 5041744, "size_mb": 4.81, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_1.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_1.mp4", "filename": "my_mouth_is_dry_additional_1.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19978, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_2.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_2.mp4", "filename": "my_mouth_is_dry_additional_2.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19621, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_3.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_3.mp4", "filename": "my_mouth_is_dry_additional_3.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 18158, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_4.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_4.mp4", "filename": "my_mouth_is_dry_additional_4.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 17248, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_5.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_additional_5.mp4", "filename": "my_mouth_is_dry_additional_5.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 20873, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_1.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_1.mp4", "filename": "my_mouth_is_dry_duplicate_1.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 7295589, "size_mb": 6.96, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_10.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_10.mp4", "filename": "my_mouth_is_dry_duplicate_10.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 18158, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_2.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_2.mp4", "filename": "my_mouth_is_dry_duplicate_2.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 7634572, "size_mb": 7.28, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_3.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_3.mp4", "filename": "my_mouth_is_dry_duplicate_3.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6832677, "size_mb": 6.52, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_4.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_4.mp4", "filename": "my_mouth_is_dry_duplicate_4.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6225931, "size_mb": 5.94, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_5.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_5.mp4", "filename": "my_mouth_is_dry_duplicate_5.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6579088, "size_mb": 6.27, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_6.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_6.mp4", "filename": "my_mouth_is_dry_duplicate_6.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 17248, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_7.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_7.mp4", "filename": "my_mouth_is_dry_duplicate_7.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 20873, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_8.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_8.mp4", "filename": "my_mouth_is_dry_duplicate_8.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19978, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_9.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_duplicate_9.mp4", "filename": "my_mouth_is_dry_duplicate_9.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 19621, "size_mb": 0.02, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 1.MOV", "filename": "my_mouth_is_dry_speaker1._video 1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6579088, "size_mb": 6.27, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 2.MOV", "filename": "my_mouth_is_dry_speaker1._video 2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6832677, "size_mb": 6.52, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 3.MOV", "filename": "my_mouth_is_dry_speaker1._video 3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 6225931, "size_mb": 5.94, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 4.MOV", "filename": "my_mouth_is_dry_speaker1._video 4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 7295589, "size_mb": 6.96, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/my_mouth_is_dry_speaker1._video 5.MOV", "filename": "my_mouth_is_dry_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "doctor", "file_info": {"size_bytes": 7634572, "size_mb": 7.28, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 1.MOV", "filename": "my_mouth_is_dry_speaker1._video 1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 6579088, "size_mb": 6.27, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 2.MOV", "filename": "my_mouth_is_dry_speaker1._video 2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 6832677, "size_mb": 6.52, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 3.MOV", "filename": "my_mouth_is_dry_speaker1._video 3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 6225931, "size_mb": 5.94, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 4.MOV", "filename": "my_mouth_is_dry_speaker1._video 4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 7295589, "size_mb": 6.96, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/my_mouth_is_dry_speaker1._video 5.MOV", "filename": "my_mouth_is_dry_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 7634572, "size_mb": 7.28, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_01_doctor_test_set.mp4", "relative_path": "model_evaluation_processed/processed_test_01_doctor_test_set.mp4", "filename": "processed_test_01_doctor_test_set.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "doctor", "file_info": {"size_bytes": 5606, "size_mb": 0.01, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_06_doctor_training_set.mp4", "relative_path": "model_evaluation_processed/processed_test_06_doctor_training_set.mp4", "filename": "processed_test_06_doctor_training_set.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "doctor", "file_info": {"size_bytes": 17459, "size_mb": 0.02, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_08_my_mouth_is_dry_extra_videos.mp4", "relative_path": "model_evaluation_processed/processed_test_08_my_mouth_is_dry_extra_videos.mp4", "filename": "processed_test_08_my_mouth_is_dry_extra_videos.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "doctor", "file_info": {"size_bytes": 13401, "size_mb": 0.01, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_01_doctor_test_set.mp4", "relative_path": "model_evaluation_test_videos/test_01_doctor_test_set.mp4", "filename": "test_01_doctor_test_set.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "doctor", "file_info": {"size_bytes": 3279054, "size_mb": 3.13, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_06_doctor_training_set.mp4", "relative_path": "model_evaluation_test_videos/test_06_doctor_training_set.mp4", "filename": "test_06_doctor_training_set.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "doctor", "file_info": {"size_bytes": 479401, "size_mb": 0.46, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_08_my_mouth_is_dry_extra_videos.mp4", "relative_path": "model_evaluation_test_videos/test_08_my_mouth_is_dry_extra_videos.mp4", "filename": "test_08_my_mouth_is_dry_extra_videos.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "doctor", "file_info": {"size_bytes": 955081, "size_mb": 0.91, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 1.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 1.mov", "filename": "doctor_speaker 1_video 1.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5534528, "size_mb": 5.28, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 1.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 1.mp4", "filename": "doctor_speaker 1_video 1.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 628574, "size_mb": 0.6, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 10.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 10.mov", "filename": "doctor_speaker 1_video 10.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5891026, "size_mb": 5.62, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 10.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 10.mp4", "filename": "doctor_speaker 1_video 10.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 700486, "size_mb": 0.67, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 11.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 11.mov", "filename": "doctor_speaker 1_video 11.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5534528, "size_mb": 5.28, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 11.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 11.mp4", "filename": "doctor_speaker 1_video 11.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 628574, "size_mb": 0.6, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 12.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 12.mov", "filename": "doctor_speaker 1_video 12.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4801646, "size_mb": 4.58, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 12.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 12.mp4", "filename": "doctor_speaker 1_video 12.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 583682, "size_mb": 0.56, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 13.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 13.mov", "filename": "doctor_speaker 1_video 13.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4563291, "size_mb": 4.35, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 13.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 13.mp4", "filename": "doctor_speaker 1_video 13.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 554288, "size_mb": 0.53, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 14.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 14.mov", "filename": "doctor_speaker 1_video 14.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5862830, "size_mb": 5.59, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 14.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 14.mp4", "filename": "doctor_speaker 1_video 14.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 702906, "size_mb": 0.67, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 15.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 15.mov", "filename": "doctor_speaker 1_video 15.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4466012, "size_mb": 4.26, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 15.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 15.mp4", "filename": "doctor_speaker 1_video 15.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 549282, "size_mb": 0.52, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 16.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 16.mov", "filename": "doctor_speaker 1_video 16.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4619319, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 16.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 16.mp4", "filename": "doctor_speaker 1_video 16.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 592151, "size_mb": 0.56, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 17.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 17.mov", "filename": "doctor_speaker 1_video 17.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4396160, "size_mb": 4.19, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 17.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 17.mp4", "filename": "doctor_speaker 1_video 17.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 534172, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 18.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 18.mov", "filename": "doctor_speaker 1_video 18.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4416392, "size_mb": 4.21, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 18.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 18.mp4", "filename": "doctor_speaker 1_video 18.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 532670, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 19.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 19.mov", "filename": "doctor_speaker 1_video 19.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4119930, "size_mb": 3.93, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 19.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 19.mp4", "filename": "doctor_speaker 1_video 19.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 516242, "size_mb": 0.49, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 2.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 2.mov", "filename": "doctor_speaker 1_video 2.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4801646, "size_mb": 4.58, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 2.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 2.mp4", "filename": "doctor_speaker 1_video 2.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 583682, "size_mb": 0.56, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 20.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 20.mov", "filename": "doctor_speaker 1_video 20.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5891026, "size_mb": 5.62, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 20.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 20.mp4", "filename": "doctor_speaker 1_video 20.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 700486, "size_mb": 0.67, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 3.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 3.mov", "filename": "doctor_speaker 1_video 3.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4563291, "size_mb": 4.35, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 3.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 3.mp4", "filename": "doctor_speaker 1_video 3.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 554288, "size_mb": 0.53, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 4.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 4.mov", "filename": "doctor_speaker 1_video 4.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5862830, "size_mb": 5.59, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 4.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 4.mp4", "filename": "doctor_speaker 1_video 4.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 702906, "size_mb": 0.67, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 5.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 5.mov", "filename": "doctor_speaker 1_video 5.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4466012, "size_mb": 4.26, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 5.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 5.mp4", "filename": "doctor_speaker 1_video 5.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 549282, "size_mb": 0.52, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 6.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 6.mov", "filename": "doctor_speaker 1_video 6.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4619319, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 6.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 6.mp4", "filename": "doctor_speaker 1_video 6.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 592151, "size_mb": 0.56, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 7.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 7.mov", "filename": "doctor_speaker 1_video 7.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4396160, "size_mb": 4.19, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 7.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 7.mp4", "filename": "doctor_speaker 1_video 7.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 534172, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 8.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 8.mov", "filename": "doctor_speaker 1_video 8.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4416392, "size_mb": 4.21, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 8.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 8.mp4", "filename": "doctor_speaker 1_video 8.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 532670, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 9.mov", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 9.mov", "filename": "doctor_speaker 1_video 9.mov", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4119930, "size_mb": 3.93, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/doctor/doctor_speaker 1_video 9.mp4", "relative_path": "speaker 1 calibration Videos/doctor/doctor_speaker 1_video 9.mp4", "filename": "doctor_speaker 1_video 9.mp4", "directory": "data/speaker 1 calibration Videos/doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 516242, "size_mb": 0.49, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mov", "filename": "my_mouth_is_dry_speaker 1_video 1.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4865092, "size_mb": 4.64, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mp4", "filename": "my_mouth_is_dry_speaker 1_video 1.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 694823, "size_mb": 0.66, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mov", "filename": "my_mouth_is_dry_speaker 1_video 10.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4891561, "size_mb": 4.66, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mp4", "filename": "my_mouth_is_dry_speaker 1_video 10.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 685078, "size_mb": 0.65, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mov", "filename": "my_mouth_is_dry_speaker 1_video 11.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4865092, "size_mb": 4.64, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mp4", "filename": "my_mouth_is_dry_speaker 1_video 11.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 694823, "size_mb": 0.66, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mov", "filename": "my_mouth_is_dry_speaker 1_video 12.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 6004286, "size_mb": 5.73, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mp4", "filename": "my_mouth_is_dry_speaker 1_video 12.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 792661, "size_mb": 0.76, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mov", "filename": "my_mouth_is_dry_speaker 1_video 13.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5238692, "size_mb": 5.0, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mp4", "filename": "my_mouth_is_dry_speaker 1_video 13.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 749947, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mov", "filename": "my_mouth_is_dry_speaker 1_video 14.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5302154, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mp4", "filename": "my_mouth_is_dry_speaker 1_video 14.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 751651, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mov", "filename": "my_mouth_is_dry_speaker 1_video 15.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5547117, "size_mb": 5.29, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mp4", "filename": "my_mouth_is_dry_speaker 1_video 15.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 755560, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mov", "filename": "my_mouth_is_dry_speaker 1_video 16.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5951098, "size_mb": 5.68, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mp4", "filename": "my_mouth_is_dry_speaker 1_video 16.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 751037, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mov", "filename": "my_mouth_is_dry_speaker 1_video 17.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5158388, "size_mb": 4.92, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mp4", "filename": "my_mouth_is_dry_speaker 1_video 17.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 659453, "size_mb": 0.63, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mov", "filename": "my_mouth_is_dry_speaker 1_video 18.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5051089, "size_mb": 4.82, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mp4", "filename": "my_mouth_is_dry_speaker 1_video 18.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 679801, "size_mb": 0.65, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mov", "filename": "my_mouth_is_dry_speaker 1_video 19.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5268850, "size_mb": 5.02, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mp4", "filename": "my_mouth_is_dry_speaker 1_video 19.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 729350, "size_mb": 0.7, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mov", "filename": "my_mouth_is_dry_speaker 1_video 2.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 6004286, "size_mb": 5.73, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mp4", "filename": "my_mouth_is_dry_speaker 1_video 2.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 792661, "size_mb": 0.76, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mov", "filename": "my_mouth_is_dry_speaker 1_video 20.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4891561, "size_mb": 4.66, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mp4", "filename": "my_mouth_is_dry_speaker 1_video 20.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 685078, "size_mb": 0.65, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mov", "filename": "my_mouth_is_dry_speaker 1_video 3.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5238692, "size_mb": 5.0, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mp4", "filename": "my_mouth_is_dry_speaker 1_video 3.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 749947, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mov", "filename": "my_mouth_is_dry_speaker 1_video 4.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5302154, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mp4", "filename": "my_mouth_is_dry_speaker 1_video 4.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 751651, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mov", "filename": "my_mouth_is_dry_speaker 1_video 5.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5547117, "size_mb": 5.29, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mp4", "filename": "my_mouth_is_dry_speaker 1_video 5.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 755560, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mov", "filename": "my_mouth_is_dry_speaker 1_video 6.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5951098, "size_mb": 5.68, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mp4", "filename": "my_mouth_is_dry_speaker 1_video 6.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 751037, "size_mb": 0.72, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mov", "filename": "my_mouth_is_dry_speaker 1_video 7.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5158388, "size_mb": 4.92, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mp4", "filename": "my_mouth_is_dry_speaker 1_video 7.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 659453, "size_mb": 0.63, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mov", "filename": "my_mouth_is_dry_speaker 1_video 8.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5051089, "size_mb": 4.82, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mp4", "filename": "my_mouth_is_dry_speaker 1_video 8.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 679801, "size_mb": 0.65, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mov", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mov", "filename": "my_mouth_is_dry_speaker 1_video 9.mov", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5268850, "size_mb": 5.02, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mp4", "relative_path": "speaker 1 calibration Videos/my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mp4", "filename": "my_mouth_is_dry_speaker 1_video 9.mp4", "directory": "data/speaker 1 calibration Videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 729350, "size_mb": 0.7, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_001.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_001.mp4", "filename": "doctor_speaker2_video_001.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2672270, "size_mb": 2.55, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_002.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_002.mp4", "filename": "doctor_speaker2_video_002.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2127958, "size_mb": 2.03, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_003.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_003.mp4", "filename": "doctor_speaker2_video_003.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2415373, "size_mb": 2.3, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_004.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_004.mp4", "filename": "doctor_speaker2_video_004.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2612076, "size_mb": 2.49, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_005.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_005.mp4", "filename": "doctor_speaker2_video_005.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3125689, "size_mb": 2.98, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_006.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_006.mp4", "filename": "doctor_speaker2_video_006.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3176570, "size_mb": 3.03, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_007.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_007.mp4", "filename": "doctor_speaker2_video_007.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3658368, "size_mb": 3.49, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_008.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_008.mp4", "filename": "doctor_speaker2_video_008.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2513901, "size_mb": 2.4, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_009.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_009.mp4", "filename": "doctor_speaker2_video_009.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3738746, "size_mb": 3.57, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_010.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_010.mp4", "filename": "doctor_speaker2_video_010.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3002078, "size_mb": 2.86, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_011.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_011.mp4", "filename": "doctor_speaker2_video_011.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3461775, "size_mb": 3.3, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_012.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_012.mp4", "filename": "doctor_speaker2_video_012.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2772889, "size_mb": 2.64, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_013.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_013.mp4", "filename": "doctor_speaker2_video_013.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2443781, "size_mb": 2.33, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_014.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_014.mp4", "filename": "doctor_speaker2_video_014.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2383323, "size_mb": 2.27, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_015.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_015.mp4", "filename": "doctor_speaker2_video_015.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4087427, "size_mb": 3.9, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_016.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_016.mp4", "filename": "doctor_speaker2_video_016.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2731122, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_017.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_017.mp4", "filename": "doctor_speaker2_video_017.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3792974, "size_mb": 3.62, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_018.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_018.mp4", "filename": "doctor_speaker2_video_018.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3388137, "size_mb": 3.23, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_019.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_019.mp4", "filename": "doctor_speaker2_video_019.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2721200, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_020.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_020.mp4", "filename": "doctor_speaker2_video_020.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4693562, "size_mb": 4.48, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_021.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Doctor/doctor_speaker2_video_021.mp4", "filename": "doctor_speaker2_video_021.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3625388, "size_mb": 3.46, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_001.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_001.mp4", "filename": "my_mouth_is_dry_speaker2_video_001.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4631757, "size_mb": 4.42, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_002.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_002.mp4", "filename": "my_mouth_is_dry_speaker2_video_002.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4620182, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_003.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_003.mp4", "filename": "my_mouth_is_dry_speaker2_video_003.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4642920, "size_mb": 4.43, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_004.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_004.mp4", "filename": "my_mouth_is_dry_speaker2_video_004.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4000762, "size_mb": 3.82, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_005.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_005.mp4", "filename": "my_mouth_is_dry_speaker2_video_005.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4181331, "size_mb": 3.99, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_006.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_006.mp4", "filename": "my_mouth_is_dry_speaker2_video_006.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4152728, "size_mb": 3.96, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_007.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_007.mp4", "filename": "my_mouth_is_dry_speaker2_video_007.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3787660, "size_mb": 3.61, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_008.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_008.mp4", "filename": "my_mouth_is_dry_speaker2_video_008.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3893179, "size_mb": 3.71, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_009.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_009.mp4", "filename": "my_mouth_is_dry_speaker2_video_009.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3593788, "size_mb": 3.43, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_010.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_010.mp4", "filename": "my_mouth_is_dry_speaker2_video_010.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3593151, "size_mb": 3.43, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_011.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_011.mp4", "filename": "my_mouth_is_dry_speaker2_video_011.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4267290, "size_mb": 4.07, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_012.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_012.mp4", "filename": "my_mouth_is_dry_speaker2_video_012.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3680430, "size_mb": 3.51, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_013.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_013.mp4", "filename": "my_mouth_is_dry_speaker2_video_013.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4009050, "size_mb": 3.82, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_014.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_014.mp4", "filename": "my_mouth_is_dry_speaker2_video_014.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3882250, "size_mb": 3.7, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_015.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_015.mp4", "filename": "my_mouth_is_dry_speaker2_video_015.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3551609, "size_mb": 3.39, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_016.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_016.mp4", "filename": "my_mouth_is_dry_speaker2_video_016.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3450532, "size_mb": 3.29, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_017.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_017.mp4", "filename": "my_mouth_is_dry_speaker2_video_017.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4274660, "size_mb": 4.08, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_018.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_018.mp4", "filename": "my_mouth_is_dry_speaker2_video_018.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3531620, "size_mb": 3.37, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_019.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry/my_mouth_is_dry_speaker2_video_019.mp4", "filename": "my_mouth_is_dry_speaker2_video_019.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4108628, "size_mb": 3.92, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 1.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 1.mov", "filename": "doctor_speaker 1_video 1.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5534528, "size_mb": 5.28, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 10.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 10.mov", "filename": "doctor_speaker 1_video 10.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5891026, "size_mb": 5.62, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 11.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 11.mov", "filename": "doctor_speaker 1_video 11.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5534528, "size_mb": 5.28, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 12.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 12.mov", "filename": "doctor_speaker 1_video 12.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4801646, "size_mb": 4.58, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 13.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 13.mov", "filename": "doctor_speaker 1_video 13.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4563291, "size_mb": 4.35, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 14.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 14.mov", "filename": "doctor_speaker 1_video 14.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5862830, "size_mb": 5.59, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 15.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 15.mov", "filename": "doctor_speaker 1_video 15.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4466012, "size_mb": 4.26, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 16.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 16.mov", "filename": "doctor_speaker 1_video 16.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4619319, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 17.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 17.mov", "filename": "doctor_speaker 1_video 17.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4396160, "size_mb": 4.19, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 18.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 18.mov", "filename": "doctor_speaker 1_video 18.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4416392, "size_mb": 4.21, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 19.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 19.mov", "filename": "doctor_speaker 1_video 19.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4119930, "size_mb": 3.93, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 2.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 2.mov", "filename": "doctor_speaker 1_video 2.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4801646, "size_mb": 4.58, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 20.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 20.mov", "filename": "doctor_speaker 1_video 20.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5891026, "size_mb": 5.62, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 3.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 3.mov", "filename": "doctor_speaker 1_video 3.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4563291, "size_mb": 4.35, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 4.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 4.mov", "filename": "doctor_speaker 1_video 4.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 5862830, "size_mb": 5.59, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 5.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 5.mov", "filename": "doctor_speaker 1_video 5.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4466012, "size_mb": 4.26, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 6.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 6.mov", "filename": "doctor_speaker 1_video 6.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4619319, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 7.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 7.mov", "filename": "doctor_speaker 1_video 7.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4396160, "size_mb": 4.19, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 8.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 8.mov", "filename": "doctor_speaker 1_video 8.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4416392, "size_mb": 4.21, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /doctor/doctor_speaker 1_video 9.mov", "relative_path": "speaker sets/speaker 1 /doctor/doctor_speaker 1_video 9.mov", "filename": "doctor_speaker 1_video 9.mov", "directory": "data/speaker sets/speaker 1 /doctor", "source_directory": "doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4119930, "size_mb": 3.93, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 1.mov", "filename": "my_mouth_is_dry_speaker 1_video 1.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4865092, "size_mb": 4.64, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 10.mov", "filename": "my_mouth_is_dry_speaker 1_video 10.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4891561, "size_mb": 4.66, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 11.mov", "filename": "my_mouth_is_dry_speaker 1_video 11.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4865092, "size_mb": 4.64, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 12.mov", "filename": "my_mouth_is_dry_speaker 1_video 12.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 6004286, "size_mb": 5.73, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 13.mov", "filename": "my_mouth_is_dry_speaker 1_video 13.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5238692, "size_mb": 5.0, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 14.mov", "filename": "my_mouth_is_dry_speaker 1_video 14.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5302154, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 15.mov", "filename": "my_mouth_is_dry_speaker 1_video 15.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5547117, "size_mb": 5.29, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 16.mov", "filename": "my_mouth_is_dry_speaker 1_video 16.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5951098, "size_mb": 5.68, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 17.mov", "filename": "my_mouth_is_dry_speaker 1_video 17.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5158388, "size_mb": 4.92, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 18.mov", "filename": "my_mouth_is_dry_speaker 1_video 18.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5051089, "size_mb": 4.82, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 19.mov", "filename": "my_mouth_is_dry_speaker 1_video 19.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5268850, "size_mb": 5.02, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 2.mov", "filename": "my_mouth_is_dry_speaker 1_video 2.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 6004286, "size_mb": 5.73, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 20.mov", "filename": "my_mouth_is_dry_speaker 1_video 20.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4891561, "size_mb": 4.66, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 3.mov", "filename": "my_mouth_is_dry_speaker 1_video 3.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5238692, "size_mb": 5.0, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 4.mov", "filename": "my_mouth_is_dry_speaker 1_video 4.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5302154, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 5.mov", "filename": "my_mouth_is_dry_speaker 1_video 5.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5547117, "size_mb": 5.29, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 6.mov", "filename": "my_mouth_is_dry_speaker 1_video 6.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5951098, "size_mb": 5.68, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 7.mov", "filename": "my_mouth_is_dry_speaker 1_video 7.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5158388, "size_mb": 4.92, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 8.mov", "filename": "my_mouth_is_dry_speaker 1_video 8.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5051089, "size_mb": 4.82, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mov", "relative_path": "speaker sets/speaker 1 /my mouth is dry/my_mouth_is_dry_speaker 1_video 9.mov", "filename": "my_mouth_is_dry_speaker 1_video 9.mov", "directory": "data/speaker sets/speaker 1 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 5268850, "size_mb": 5.02, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_001.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_001.mp4", "filename": "doctor_speaker2_video_001.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2672270, "size_mb": 2.55, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_002.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_002.mp4", "filename": "doctor_speaker2_video_002.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2127958, "size_mb": 2.03, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_003.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_003.mp4", "filename": "doctor_speaker2_video_003.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2415373, "size_mb": 2.3, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_004.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_004.mp4", "filename": "doctor_speaker2_video_004.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2612076, "size_mb": 2.49, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_005.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_005.mp4", "filename": "doctor_speaker2_video_005.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3125689, "size_mb": 2.98, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_006.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_006.mp4", "filename": "doctor_speaker2_video_006.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3176570, "size_mb": 3.03, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_007.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_007.mp4", "filename": "doctor_speaker2_video_007.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3658368, "size_mb": 3.49, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_008.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_008.mp4", "filename": "doctor_speaker2_video_008.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2513901, "size_mb": 2.4, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_009.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_009.mp4", "filename": "doctor_speaker2_video_009.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3738746, "size_mb": 3.57, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_010.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_010.mp4", "filename": "doctor_speaker2_video_010.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3002078, "size_mb": 2.86, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_011.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_011.mp4", "filename": "doctor_speaker2_video_011.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3461775, "size_mb": 3.3, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_012.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_012.mp4", "filename": "doctor_speaker2_video_012.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2772889, "size_mb": 2.64, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_013.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_013.mp4", "filename": "doctor_speaker2_video_013.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2443781, "size_mb": 2.33, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_014.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_014.mp4", "filename": "doctor_speaker2_video_014.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2383323, "size_mb": 2.27, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_015.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_015.mp4", "filename": "doctor_speaker2_video_015.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4087427, "size_mb": 3.9, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_016.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_016.mp4", "filename": "doctor_speaker2_video_016.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2731122, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_017.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_017.mp4", "filename": "doctor_speaker2_video_017.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3792974, "size_mb": 3.62, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_018.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_018.mp4", "filename": "doctor_speaker2_video_018.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3388137, "size_mb": 3.23, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_019.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_019.mp4", "filename": "doctor_speaker2_video_019.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 2721200, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_020.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_020.mp4", "filename": "doctor_speaker2_video_020.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 4693562, "size_mb": 4.48, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Doctor/doctor_speaker2_video_021.mp4", "relative_path": "speaker sets/speaker 2 /Doctor/doctor_speaker2_video_021.mp4", "filename": "doctor_speaker2_video_021.mp4", "directory": "data/speaker sets/speaker 2 /Doctor", "source_directory": "Doctor", "matched_class": "doctor", "file_info": {"size_bytes": 3625388, "size_mb": 3.46, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_001.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_001.mp4", "filename": "my_mouth_is_dry_speaker2_video_001.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4631757, "size_mb": 4.42, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_002.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_002.mp4", "filename": "my_mouth_is_dry_speaker2_video_002.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4620182, "size_mb": 4.41, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_003.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_003.mp4", "filename": "my_mouth_is_dry_speaker2_video_003.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4642920, "size_mb": 4.43, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_004.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_004.mp4", "filename": "my_mouth_is_dry_speaker2_video_004.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4000762, "size_mb": 3.82, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_005.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_005.mp4", "filename": "my_mouth_is_dry_speaker2_video_005.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4181331, "size_mb": 3.99, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_006.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_006.mp4", "filename": "my_mouth_is_dry_speaker2_video_006.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4152728, "size_mb": 3.96, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_007.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_007.mp4", "filename": "my_mouth_is_dry_speaker2_video_007.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3787660, "size_mb": 3.61, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_008.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_008.mp4", "filename": "my_mouth_is_dry_speaker2_video_008.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3893179, "size_mb": 3.71, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_009.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_009.mp4", "filename": "my_mouth_is_dry_speaker2_video_009.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3593788, "size_mb": 3.43, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_010.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_010.mp4", "filename": "my_mouth_is_dry_speaker2_video_010.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3593151, "size_mb": 3.43, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_011.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_011.mp4", "filename": "my_mouth_is_dry_speaker2_video_011.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4267290, "size_mb": 4.07, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_012.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_012.mp4", "filename": "my_mouth_is_dry_speaker2_video_012.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3680430, "size_mb": 3.51, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_013.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_013.mp4", "filename": "my_mouth_is_dry_speaker2_video_013.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4009050, "size_mb": 3.82, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_014.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_014.mp4", "filename": "my_mouth_is_dry_speaker2_video_014.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3882250, "size_mb": 3.7, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_015.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_015.mp4", "filename": "my_mouth_is_dry_speaker2_video_015.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3551609, "size_mb": 3.39, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_016.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_016.mp4", "filename": "my_mouth_is_dry_speaker2_video_016.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3450532, "size_mb": 3.29, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_017.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_017.mp4", "filename": "my_mouth_is_dry_speaker2_video_017.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4274660, "size_mb": 4.08, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_018.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_018.mp4", "filename": "my_mouth_is_dry_speaker2_video_018.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 3531620, "size_mb": 3.37, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_019.mp4", "relative_path": "speaker sets/speaker 2 /my mouth is dry/my_mouth_is_dry_speaker2_video_019.mp4", "filename": "my_mouth_is_dry_speaker2_video_019.mp4", "directory": "data/speaker sets/speaker 2 /my mouth is dry", "source_directory": "my mouth is dry", "matched_class": "doctor", "file_info": {"size_bytes": 4108628, "size_mb": 3.92, "exists": true}}, {"video_path": "data/the_best_videos_so_far/doctor 10_processed.mp4", "relative_path": "the_best_videos_so_far/doctor 10_processed.mp4", "filename": "doctor 10_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "doctor", "file_info": {"size_bytes": 444600, "size_mb": 0.42, "exists": true}}, {"video_path": "data/the_best_videos_so_far/doctor 15_processed.mp4", "relative_path": "the_best_videos_so_far/doctor 15_processed.mp4", "filename": "doctor 15_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "doctor", "file_info": {"size_bytes": 452141, "size_mb": 0.43, "exists": true}}, {"video_path": "data/the_best_videos_so_far/doctor 4_processed.mp4", "relative_path": "the_best_videos_so_far/doctor 4_processed.mp4", "filename": "doctor 4_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "doctor", "file_info": {"size_bytes": 455384, "size_mb": 0.43, "exists": true}}, {"video_path": "data/the_best_videos_so_far/doctor 8_processed.mp4", "relative_path": "the_best_videos_so_far/doctor 8_processed.mp4", "filename": "doctor 8_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "doctor", "file_info": {"size_bytes": 470155, "size_mb": 0.45, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 1.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 1.mp4", "filename": "doctor 1.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 479401, "size_mb": 0.46, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 10.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 10.mp4", "filename": "doctor 10.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2249326, "size_mb": 2.15, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 12.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 12.mp4", "filename": "doctor 12.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2924398, "size_mb": 2.79, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 14.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 14.mp4", "filename": "doctor 14.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2249342, "size_mb": 2.15, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 15.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 15.mp4", "filename": "doctor 15.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3181498, "size_mb": 3.03, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 16.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 16.mp4", "filename": "doctor 16.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 4195401, "size_mb": 4.0, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 17.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 17.mp4", "filename": "doctor 17.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3201751, "size_mb": 3.05, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 18.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 18.mp4", "filename": "doctor 18.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2344473, "size_mb": 2.24, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 19.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 19.mp4", "filename": "doctor 19.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1860334, "size_mb": 1.77, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 4.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 4.mp4", "filename": "doctor 4.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2358969, "size_mb": 2.25, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 5.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 5.mp4", "filename": "doctor 5.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 3809261, "size_mb": 3.63, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 6.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 6.mp4", "filename": "doctor 6.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 1654288, "size_mb": 1.58, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 7.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 7.mp4", "filename": "doctor 7.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2566809, "size_mb": 2.45, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 8.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 8.mp4", "filename": "doctor 8.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2325810, "size_mb": 2.22, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/doctor 9.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/doctor 9.mp4", "filename": "doctor 9.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "doctor", "file_info": {"size_bytes": 2475472, "size_mb": 2.36, "exists": true}}, {"video_path": "data/TEST SET/phone 9.mp4", "relative_path": "TEST SET/phone 9.mp4", "filename": "phone 9.mp4", "directory": "data/TEST SET", "source_directory": "TEST SET", "matched_class": "phone", "file_info": {"size_bytes": 2471526, "size_mb": 2.36, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 1.mp4", "relative_path": "TRAINING SET 2.9.25/phone 1.mp4", "filename": "phone 1.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2003428, "size_mb": 1.91, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 10.mp4", "relative_path": "TRAINING SET 2.9.25/phone 10.mp4", "filename": "phone 10.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2200678, "size_mb": 2.1, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 11.mp4", "relative_path": "TRAINING SET 2.9.25/phone 11.mp4", "filename": "phone 11.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 1994364, "size_mb": 1.9, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 12.mp4", "relative_path": "TRAINING SET 2.9.25/phone 12.mp4", "filename": "phone 12.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2707326, "size_mb": 2.58, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 13.mp4", "relative_path": "TRAINING SET 2.9.25/phone 13.mp4", "filename": "phone 13.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 3489900, "size_mb": 3.33, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 14.mp4", "relative_path": "TRAINING SET 2.9.25/phone 14.mp4", "filename": "phone 14.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2672812, "size_mb": 2.55, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 16.mp4", "relative_path": "TRAINING SET 2.9.25/phone 16.mp4", "filename": "phone 16.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2218051, "size_mb": 2.12, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 17.mp4", "relative_path": "TRAINING SET 2.9.25/phone 17.mp4", "filename": "phone 17.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 3306627, "size_mb": 3.15, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 18.mp4", "relative_path": "TRAINING SET 2.9.25/phone 18.mp4", "filename": "phone 18.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 4563508, "size_mb": 4.35, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 2.mp4", "relative_path": "TRAINING SET 2.9.25/phone 2.mp4", "filename": "phone 2.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2781536, "size_mb": 2.65, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 20.mp4", "relative_path": "TRAINING SET 2.9.25/phone 20.mp4", "filename": "phone 20.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 552726, "size_mb": 0.53, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 3.mp4", "relative_path": "TRAINING SET 2.9.25/phone 3.mp4", "filename": "phone 3.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2683744, "size_mb": 2.56, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 4.mp4", "relative_path": "TRAINING SET 2.9.25/phone 4.mp4", "filename": "phone 4.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2042758, "size_mb": 1.95, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 5.mp4", "relative_path": "TRAINING SET 2.9.25/phone 5.mp4", "filename": "phone 5.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2255019, "size_mb": 2.15, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 6.mp4", "relative_path": "TRAINING SET 2.9.25/phone 6.mp4", "filename": "phone 6.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2441226, "size_mb": 2.33, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/phone 7.mp4", "relative_path": "TRAINING SET 2.9.25/phone 7.mp4", "filename": "phone 7.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2690715, "size_mb": 2.57, "exists": true}}, {"video_path": "data/VAL SET/phone 15.mp4", "relative_path": "VAL SET/phone 15.mp4", "filename": "phone 15.mp4", "directory": "data/VAL SET", "source_directory": "VAL SET", "matched_class": "phone", "file_info": {"size_bytes": 2909605, "size_mb": 2.77, "exists": true}}, {"video_path": "data/VAL SET/phone 8.mp4", "relative_path": "VAL SET/phone 8.mp4", "filename": "phone 8.mp4", "directory": "data/VAL SET", "source_directory": "VAL SET", "matched_class": "phone", "file_info": {"size_bytes": 2313451, "size_mb": 2.21, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_02_phone_test_set.mp4", "relative_path": "model_evaluation_processed/processed_test_02_phone_test_set.mp4", "filename": "processed_test_02_phone_test_set.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "phone", "file_info": {"size_bytes": 4417, "size_mb": 0.0, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_07_phone_training_set.mp4", "relative_path": "model_evaluation_processed/processed_test_07_phone_training_set.mp4", "filename": "processed_test_07_phone_training_set.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "phone", "file_info": {"size_bytes": 6426, "size_mb": 0.01, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_02_phone_test_set.mp4", "relative_path": "model_evaluation_test_videos/test_02_phone_test_set.mp4", "filename": "test_02_phone_test_set.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "phone", "file_info": {"size_bytes": 2471526, "size_mb": 2.36, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_07_phone_training_set.mp4", "relative_path": "model_evaluation_test_videos/test_07_phone_training_set.mp4", "filename": "test_07_phone_training_set.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "phone", "file_info": {"size_bytes": 2255019, "size_mb": 2.15, "exists": true}}, {"video_path": "data/the_best_videos_so_far/phone 12_processed.mp4", "relative_path": "the_best_videos_so_far/phone 12_processed.mp4", "filename": "phone 12_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "phone", "file_info": {"size_bytes": 478441, "size_mb": 0.46, "exists": true}}, {"video_path": "data/the_best_videos_so_far/phone 17_processed.mp4", "relative_path": "the_best_videos_so_far/phone 17_processed.mp4", "filename": "phone 17_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "phone", "file_info": {"size_bytes": 534009, "size_mb": 0.51, "exists": true}}, {"video_path": "data/the_best_videos_so_far/phone 18_processed.mp4", "relative_path": "the_best_videos_so_far/phone 18_processed.mp4", "filename": "phone 18_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "phone", "file_info": {"size_bytes": 487083, "size_mb": 0.46, "exists": true}}, {"video_path": "data/the_best_videos_so_far/phone 5_processed.mp4", "relative_path": "the_best_videos_so_far/phone 5_processed.mp4", "filename": "phone 5_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "phone", "file_info": {"size_bytes": 484021, "size_mb": 0.46, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 1.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 1.mp4", "filename": "phone 1.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2003428, "size_mb": 1.91, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 10.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 10.mp4", "filename": "phone 10.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2200678, "size_mb": 2.1, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 11.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 11.mp4", "filename": "phone 11.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 1994364, "size_mb": 1.9, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 12.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 12.mp4", "filename": "phone 12.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2707326, "size_mb": 2.58, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 13.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 13.mp4", "filename": "phone 13.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 3489900, "size_mb": 3.33, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 14.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 14.mp4", "filename": "phone 14.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2672812, "size_mb": 2.55, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 16.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 16.mp4", "filename": "phone 16.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2218051, "size_mb": 2.12, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 17.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 17.mp4", "filename": "phone 17.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 3306627, "size_mb": 3.15, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 18.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 18.mp4", "filename": "phone 18.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 4563508, "size_mb": 4.35, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 2.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 2.mp4", "filename": "phone 2.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2781536, "size_mb": 2.65, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 20.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 20.mp4", "filename": "phone 20.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 552726, "size_mb": 0.53, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 3.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 3.mp4", "filename": "phone 3.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2683744, "size_mb": 2.56, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 4.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 4.mp4", "filename": "phone 4.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2042758, "size_mb": 1.95, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 5.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 5.mp4", "filename": "phone 5.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2255019, "size_mb": 2.15, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 6.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 6.mp4", "filename": "phone 6.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2441226, "size_mb": 2.33, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/phone 7.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/phone 7.mp4", "filename": "phone 7.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "phone", "file_info": {"size_bytes": 2690715, "size_mb": 2.57, "exists": true}}, {"video_path": "data/TEST SET/pillow 11.mp4", "relative_path": "TEST SET/pillow 11.mp4", "filename": "pillow 11.mp4", "directory": "data/TEST SET", "source_directory": "TEST SET", "matched_class": "pillow", "file_info": {"size_bytes": 600758, "size_mb": 0.57, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 12.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 12.mp4", "filename": "pillow 12.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 695778, "size_mb": 0.66, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 13.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 13.mp4", "filename": "pillow 13.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 610769, "size_mb": 0.58, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 18.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 18.mp4", "filename": "pillow 18.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 634301, "size_mb": 0.6, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 19.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 19.mp4", "filename": "pillow 19.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 744245, "size_mb": 0.71, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 2.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 2.mp4", "filename": "pillow 2.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 797609, "size_mb": 0.76, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 20.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 20.mp4", "filename": "pillow 20.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 858319, "size_mb": 0.82, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 7.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 7.mp4", "filename": "pillow 7.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 516299, "size_mb": 0.49, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 8.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 8.mp4", "filename": "pillow 8.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 595095, "size_mb": 0.57, "exists": true}}, {"video_path": "data/TRAINING SET 2.9.25/pillow 9.mp4", "relative_path": "TRAINING SET 2.9.25/pillow 9.mp4", "filename": "pillow 9.mp4", "directory": "data/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 491720, "size_mb": 0.47, "exists": true}}, {"video_path": "data/VAL SET/pillow 10.mp4", "relative_path": "VAL SET/pillow 10.mp4", "filename": "pillow 10.mp4", "directory": "data/VAL SET", "source_directory": "VAL SET", "matched_class": "pillow", "file_info": {"size_bytes": 495438, "size_mb": 0.47, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy 2.mp4", "filename": "pillow_female_18-39_caucasian_video 2 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1797094, "size_mb": 1.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy 3.mp4", "filename": "pillow_female_18-39_caucasian_video 2 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1797094, "size_mb": 1.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2 copy.mp4", "filename": "pillow_female_18-39_caucasian_video 2 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1797094, "size_mb": 1.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 2.mp4", "filename": "pillow_female_18-39_caucasian_video 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1797094, "size_mb": 1.71, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy 2.mp4", "filename": "pillow_female_18-39_caucasian_video 3 (1) copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy 3.mp4", "filename": "pillow_female_18-39_caucasian_video 3 (1) copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1) copy.mp4", "filename": "pillow_female_18-39_caucasian_video 3 (1) copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1).mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 (1).mp4", "filename": "pillow_female_18-39_caucasian_video 3 (1).mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy 2.mp4", "filename": "pillow_female_18-39_caucasian_video 3 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy 3.mp4", "filename": "pillow_female_18-39_caucasian_video 3 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3 copy.mp4", "filename": "pillow_female_18-39_caucasian_video 3 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_female_18-39_caucasian_video 3.mp4", "filename": "pillow_female_18-39_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1392979, "size_mb": 1.33, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 766871, "size_mb": 0.73, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 766871, "size_mb": 0.73, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 766871, "size_mb": 0.73, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 1.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 766871, "size_mb": 0.73, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 2 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 834096, "size_mb": 0.8, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 2 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 834096, "size_mb": 0.8, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 2 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 834096, "size_mb": 0.8, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 2.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 834096, "size_mb": 0.8, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 3 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 730766, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 3 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 730766, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 3 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 730766, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_male_speaker 1_65plus_caucasian_video 3.mp4", "filename": "pillow_male_speaker 1_65plus_caucasian_video 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 730766, "size_mb": 0.7, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy 2.mp4", "filename": "pillow_speaker1_female_65plus_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 804629, "size_mb": 0.77, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy 3.mp4", "filename": "pillow_speaker1_female_65plus_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 804629, "size_mb": 0.77, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1 copy.mp4", "filename": "pillow_speaker1_female_65plus_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 804629, "size_mb": 0.77, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_female_65plus_video 1.mp4", "filename": "pillow_speaker1_female_65plus_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 804629, "size_mb": 0.77, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy 2.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy 2.mp4", "filename": "pillow_speaker1_male_65plus_cauasian_video 1 copy 2.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1653807, "size_mb": 1.58, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy 3.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy 3.mp4", "filename": "pillow_speaker1_male_65plus_cauasian_video 1 copy 3.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1653807, "size_mb": 1.58, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1 copy.mp4", "filename": "pillow_speaker1_male_65plus_cauasian_video 1 copy.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1653807, "size_mb": 1.58, "exists": true}}, {"video_path": "data/extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1.mp4", "relative_path": "extra videos 22.9.25/extra videos 11pm/pillow_speaker1_male_65plus_cauasian_video 1.mp4", "filename": "pillow_speaker1_male_65plus_cauasian_video 1.mp4", "directory": "data/extra videos 22.9.25/extra videos 11pm", "source_directory": "extra videos 11pm", "matched_class": "pillow", "file_info": {"size_bytes": 1653807, "size_mb": 1.58, "exists": true}}, {"video_path": "data/extra videos 22.9.25/pillow_female_65plus_causasian_video 9.mp4", "relative_path": "extra videos 22.9.25/pillow_female_65plus_causasian_video 9.mp4", "filename": "pillow_female_65plus_causasian_video 9.mp4", "directory": "data/extra videos 22.9.25", "source_directory": "extra videos 22.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 496159, "size_mb": 0.47, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061141_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061141_topmid_96x64_processed.mp4", "filename": "pillow__useruser01__18to39__male__not_specified__20250827T061141_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "pillow", "file_info": {"size_bytes": 14596, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061153_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061153_topmid_96x64_processed.mp4", "filename": "pillow__useruser01__18to39__male__not_specified__20250827T061153_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15283, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061803_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061803_topmid_96x64_processed.mp4", "filename": "pillow__useruser01__18to39__male__not_specified__20250827T061803_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15544, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061814_topmid_96x64_processed.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/additional videos for calibration/pillow__useruser01__18to39__male__not_specified__20250827T061814_topmid_96x64_processed.mp4", "filename": "pillow__useruser01__18to39__male__not_specified__20250827T061814_topmid_96x64_processed.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/additional videos for calibration", "source_directory": "additional videos for calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15071, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_1.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_1.mp4", "filename": "pillow_additional_1.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15283, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_2.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_2.mp4", "filename": "pillow_additional_2.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15544, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_3.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_3.mp4", "filename": "pillow_additional_3.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 14596, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_4.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_additional_4.mp4", "filename": "pillow_additional_4.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15071, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_1.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_1.mp4", "filename": "pillow_duplicate_1.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15071, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_10.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_10.mp4", "filename": "pillow_duplicate_10.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15071, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_11.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_11.mp4", "filename": "pillow_duplicate_11.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15283, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_2.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_2.mp4", "filename": "pillow_duplicate_2.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15283, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_3.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_3.mp4", "filename": "pillow_duplicate_3.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 15544, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_4.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_4.mp4", "filename": "pillow_duplicate_4.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 14596, "size_mb": 0.01, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_5.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_5.mp4", "filename": "pillow_duplicate_5.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5532557, "size_mb": 5.28, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_6.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_6.mp4", "filename": "pillow_duplicate_6.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5549350, "size_mb": 5.29, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_7.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_7.mp4", "filename": "pillow_duplicate_7.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5725920, "size_mb": 5.46, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_8.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_8.mp4", "filename": "pillow_duplicate_8.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 6441082, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_9.mp4", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_duplicate_9.mp4", "filename": "pillow_duplicate_9.mp4", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 6436839, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video 5.MOV", "filename": "pillow_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 6436839, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_1.MOV", "filename": "pillow_speaker1._video_1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5549350, "size_mb": 5.29, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_2.MOV", "filename": "pillow_speaker1._video_2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 6441082, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_3.MOV", "filename": "pillow_speaker1._video_3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5725920, "size_mb": 5.46, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/expanded_calibration/pillow_speaker1._video_4.MOV", "filename": "pillow_speaker1._video_4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25/expanded_calibration", "source_directory": "expanded_calibration", "matched_class": "pillow", "file_info": {"size_bytes": 5532557, "size_mb": 5.28, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video 5.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video 5.MOV", "filename": "pillow_speaker1._video 5.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 6436839, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_1.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_1.MOV", "filename": "pillow_speaker1._video_1.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 5549350, "size_mb": 5.29, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_2.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_2.MOV", "filename": "pillow_speaker1._video_2.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 6441082, "size_mb": 6.14, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_3.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_3.MOV", "filename": "pillow_speaker1._video_3.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 5725920, "size_mb": 5.46, "exists": true}}, {"video_path": "data/final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_4.MOV", "relative_path": "final_corrected_test/data/calibration 23.9.25/pillow_speaker1._video_4.MOV", "filename": "pillow_speaker1._video_4.MOV", "directory": "data/final_corrected_test/data/calibration 23.9.25", "source_directory": "calibration 23.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 5532557, "size_mb": 5.28, "exists": true}}, {"video_path": "data/model_evaluation_processed/processed_test_03_pillow_test_set.mp4", "relative_path": "model_evaluation_processed/processed_test_03_pillow_test_set.mp4", "filename": "processed_test_03_pillow_test_set.mp4", "directory": "data/model_evaluation_processed", "source_directory": "model_evaluation_processed", "matched_class": "pillow", "file_info": {"size_bytes": 6075, "size_mb": 0.01, "exists": true}}, {"video_path": "data/model_evaluation_test_videos/test_03_pillow_test_set.mp4", "relative_path": "model_evaluation_test_videos/test_03_pillow_test_set.mp4", "filename": "test_03_pillow_test_set.mp4", "directory": "data/model_evaluation_test_videos", "source_directory": "model_evaluation_test_videos", "matched_class": "pillow", "file_info": {"size_bytes": 600758, "size_mb": 0.57, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 1.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 1.mov", "filename": "pillow_speaker 1_video 1.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 6086521, "size_mb": 5.8, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 1.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 1.mp4", "filename": "pillow_speaker 1_video 1.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 857797, "size_mb": 0.82, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 10.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 10.mov", "filename": "pillow_speaker 1_video 10.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5674666, "size_mb": 5.41, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 10.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 10.mp4", "filename": "pillow_speaker 1_video 10.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 778732, "size_mb": 0.74, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 11.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 11.mov", "filename": "pillow_speaker 1_video 11.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 6086521, "size_mb": 5.8, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 11.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 11.mp4", "filename": "pillow_speaker 1_video 11.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 857797, "size_mb": 0.82, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 12.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 12.mov", "filename": "pillow_speaker 1_video 12.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5209739, "size_mb": 4.97, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 12.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 12.mp4", "filename": "pillow_speaker 1_video 12.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 749098, "size_mb": 0.71, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 13.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 13.mov", "filename": "pillow_speaker 1_video 13.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5315963, "size_mb": 5.07, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 13.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 13.mp4", "filename": "pillow_speaker 1_video 13.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 736409, "size_mb": 0.7, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 14.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 14.mov", "filename": "pillow_speaker 1_video 14.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5307142, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 14.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 14.mp4", "filename": "pillow_speaker 1_video 14.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 714354, "size_mb": 0.68, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 15.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 15.mov", "filename": "pillow_speaker 1_video 15.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5332041, "size_mb": 5.09, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 15.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 15.mp4", "filename": "pillow_speaker 1_video 15.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 791794, "size_mb": 0.76, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 16.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 16.mov", "filename": "pillow_speaker 1_video 16.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 7559866, "size_mb": 7.21, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 16.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 16.mp4", "filename": "pillow_speaker 1_video 16.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 971146, "size_mb": 0.93, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 17.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 17.mov", "filename": "pillow_speaker 1_video 17.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4159206, "size_mb": 3.97, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 17.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 17.mp4", "filename": "pillow_speaker 1_video 17.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 539870, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 18.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 18.mov", "filename": "pillow_speaker 1_video 18.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4380369, "size_mb": 4.18, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 18.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 18.mp4", "filename": "pillow_speaker 1_video 18.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 565196, "size_mb": 0.54, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 19.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 19.mov", "filename": "pillow_speaker 1_video 19.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4314999, "size_mb": 4.12, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 19.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 19.mp4", "filename": "pillow_speaker 1_video 19.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 557870, "size_mb": 0.53, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 2.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 2.mov", "filename": "pillow_speaker 1_video 2.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5209739, "size_mb": 4.97, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 2.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 2.mp4", "filename": "pillow_speaker 1_video 2.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 749098, "size_mb": 0.71, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 20.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 20.mov", "filename": "pillow_speaker 1_video 20.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5674666, "size_mb": 5.41, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 20.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 20.mp4", "filename": "pillow_speaker 1_video 20.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 778732, "size_mb": 0.74, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 3.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 3.mov", "filename": "pillow_speaker 1_video 3.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5315963, "size_mb": 5.07, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 3.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 3.mp4", "filename": "pillow_speaker 1_video 3.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 736409, "size_mb": 0.7, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 4.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 4.mov", "filename": "pillow_speaker 1_video 4.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5307142, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 4.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 4.mp4", "filename": "pillow_speaker 1_video 4.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 714354, "size_mb": 0.68, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 5.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 5.mov", "filename": "pillow_speaker 1_video 5.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5332041, "size_mb": 5.09, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 5.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 5.mp4", "filename": "pillow_speaker 1_video 5.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 791794, "size_mb": 0.76, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 6.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 6.mov", "filename": "pillow_speaker 1_video 6.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 7559866, "size_mb": 7.21, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 6.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 6.mp4", "filename": "pillow_speaker 1_video 6.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 971146, "size_mb": 0.93, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 7.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 7.mov", "filename": "pillow_speaker 1_video 7.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4159206, "size_mb": 3.97, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 7.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 7.mp4", "filename": "pillow_speaker 1_video 7.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 539870, "size_mb": 0.51, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 8.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 8.mov", "filename": "pillow_speaker 1_video 8.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4380369, "size_mb": 4.18, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 8.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 8.mp4", "filename": "pillow_speaker 1_video 8.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 565196, "size_mb": 0.54, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 9.mov", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 9.mov", "filename": "pillow_speaker 1_video 9.mov", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4314999, "size_mb": 4.12, "exists": true}}, {"video_path": "data/speaker 1 calibration Videos/pillow/pillow_speaker 1_video 9.mp4", "relative_path": "speaker 1 calibration Videos/pillow/pillow_speaker 1_video 9.mp4", "filename": "pillow_speaker 1_video 9.mp4", "directory": "data/speaker 1 calibration Videos/pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 557870, "size_mb": 0.53, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_001.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_001.mp4", "filename": "pillow_speaker2_video_001.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2776660, "size_mb": 2.65, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_002.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_002.mp4", "filename": "pillow_speaker2_video_002.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2520842, "size_mb": 2.4, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_003.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_003.mp4", "filename": "pillow_speaker2_video_003.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2360295, "size_mb": 2.25, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_004.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_004.mp4", "filename": "pillow_speaker2_video_004.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 1997263, "size_mb": 1.9, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_005.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_005.mp4", "filename": "pillow_speaker2_video_005.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2572330, "size_mb": 2.45, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_006.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_006.mp4", "filename": "pillow_speaker2_video_006.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2180067, "size_mb": 2.08, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_007.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_007.mp4", "filename": "pillow_speaker2_video_007.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2225953, "size_mb": 2.12, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_008.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_008.mp4", "filename": "pillow_speaker2_video_008.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2726298, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_009.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_009.mp4", "filename": "pillow_speaker2_video_009.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2804689, "size_mb": 2.67, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_010.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_010.mp4", "filename": "pillow_speaker2_video_010.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2639677, "size_mb": 2.52, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_011.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_011.mp4", "filename": "pillow_speaker2_video_011.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2348620, "size_mb": 2.24, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_012.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_012.mp4", "filename": "pillow_speaker2_video_012.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2550947, "size_mb": 2.43, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_013.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_013.mp4", "filename": "pillow_speaker2_video_013.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2699016, "size_mb": 2.57, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_014.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_014.mp4", "filename": "pillow_speaker2_video_014.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2901045, "size_mb": 2.77, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_015.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_015.mp4", "filename": "pillow_speaker2_video_015.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2541804, "size_mb": 2.42, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_016.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_016.mp4", "filename": "pillow_speaker2_video_016.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 3117803, "size_mb": 2.97, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_017.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_017.mp4", "filename": "pillow_speaker2_video_017.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2838234, "size_mb": 2.71, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_018.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_018.mp4", "filename": "pillow_speaker2_video_018.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 3528791, "size_mb": 3.37, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_019.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_019.mp4", "filename": "pillow_speaker2_video_019.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2800807, "size_mb": 2.67, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_020.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_020.mp4", "filename": "pillow_speaker2_video_020.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2723667, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_021.mp4", "relative_path": "speaker 2 calibration videos/speaker 2 calibration videos/Pillow/pillow_speaker2_video_021.mp4", "filename": "pillow_speaker2_video_021.mp4", "directory": "data/speaker 2 calibration videos/speaker 2 calibration videos/Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2748818, "size_mb": 2.62, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 1.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 1.mov", "filename": "pillow_speaker 1_video 1.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 6086521, "size_mb": 5.8, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 10.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 10.mov", "filename": "pillow_speaker 1_video 10.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5674666, "size_mb": 5.41, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 11.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 11.mov", "filename": "pillow_speaker 1_video 11.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 6086521, "size_mb": 5.8, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 12.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 12.mov", "filename": "pillow_speaker 1_video 12.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5209739, "size_mb": 4.97, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 13.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 13.mov", "filename": "pillow_speaker 1_video 13.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5315963, "size_mb": 5.07, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 14.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 14.mov", "filename": "pillow_speaker 1_video 14.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5307142, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 15.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 15.mov", "filename": "pillow_speaker 1_video 15.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5332041, "size_mb": 5.09, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 16.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 16.mov", "filename": "pillow_speaker 1_video 16.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 7559866, "size_mb": 7.21, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 17.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 17.mov", "filename": "pillow_speaker 1_video 17.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4159206, "size_mb": 3.97, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 18.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 18.mov", "filename": "pillow_speaker 1_video 18.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4380369, "size_mb": 4.18, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 19.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 19.mov", "filename": "pillow_speaker 1_video 19.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4314999, "size_mb": 4.12, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 2.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 2.mov", "filename": "pillow_speaker 1_video 2.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5209739, "size_mb": 4.97, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 20.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 20.mov", "filename": "pillow_speaker 1_video 20.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5674666, "size_mb": 5.41, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 3.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 3.mov", "filename": "pillow_speaker 1_video 3.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5315963, "size_mb": 5.07, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 4.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 4.mov", "filename": "pillow_speaker 1_video 4.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5307142, "size_mb": 5.06, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 5.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 5.mov", "filename": "pillow_speaker 1_video 5.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 5332041, "size_mb": 5.09, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 6.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 6.mov", "filename": "pillow_speaker 1_video 6.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 7559866, "size_mb": 7.21, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 7.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 7.mov", "filename": "pillow_speaker 1_video 7.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4159206, "size_mb": 3.97, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 8.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 8.mov", "filename": "pillow_speaker 1_video 8.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4380369, "size_mb": 4.18, "exists": true}}, {"video_path": "data/speaker sets/speaker 1 /pillow/pillow_speaker 1_video 9.mov", "relative_path": "speaker sets/speaker 1 /pillow/pillow_speaker 1_video 9.mov", "filename": "pillow_speaker 1_video 9.mov", "directory": "data/speaker sets/speaker 1 /pillow", "source_directory": "pillow", "matched_class": "pillow", "file_info": {"size_bytes": 4314999, "size_mb": 4.12, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_001.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_001.mp4", "filename": "pillow_speaker2_video_001.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2776660, "size_mb": 2.65, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_002.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_002.mp4", "filename": "pillow_speaker2_video_002.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2520842, "size_mb": 2.4, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_003.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_003.mp4", "filename": "pillow_speaker2_video_003.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2360295, "size_mb": 2.25, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_004.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_004.mp4", "filename": "pillow_speaker2_video_004.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 1997263, "size_mb": 1.9, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_005.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_005.mp4", "filename": "pillow_speaker2_video_005.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2572330, "size_mb": 2.45, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_006.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_006.mp4", "filename": "pillow_speaker2_video_006.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2180067, "size_mb": 2.08, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_007.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_007.mp4", "filename": "pillow_speaker2_video_007.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2225953, "size_mb": 2.12, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_008.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_008.mp4", "filename": "pillow_speaker2_video_008.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2726298, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_009.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_009.mp4", "filename": "pillow_speaker2_video_009.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2804689, "size_mb": 2.67, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_010.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_010.mp4", "filename": "pillow_speaker2_video_010.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2639677, "size_mb": 2.52, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_011.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_011.mp4", "filename": "pillow_speaker2_video_011.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2348620, "size_mb": 2.24, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_012.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_012.mp4", "filename": "pillow_speaker2_video_012.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2550947, "size_mb": 2.43, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_013.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_013.mp4", "filename": "pillow_speaker2_video_013.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2699016, "size_mb": 2.57, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_014.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_014.mp4", "filename": "pillow_speaker2_video_014.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2901045, "size_mb": 2.77, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_015.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_015.mp4", "filename": "pillow_speaker2_video_015.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2541804, "size_mb": 2.42, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_016.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_016.mp4", "filename": "pillow_speaker2_video_016.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 3117803, "size_mb": 2.97, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_017.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_017.mp4", "filename": "pillow_speaker2_video_017.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2838234, "size_mb": 2.71, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_018.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_018.mp4", "filename": "pillow_speaker2_video_018.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 3528791, "size_mb": 3.37, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_019.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_019.mp4", "filename": "pillow_speaker2_video_019.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2800807, "size_mb": 2.67, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_020.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_020.mp4", "filename": "pillow_speaker2_video_020.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2723667, "size_mb": 2.6, "exists": true}}, {"video_path": "data/speaker sets/speaker 2 /Pillow/pillow_speaker2_video_021.mp4", "relative_path": "speaker sets/speaker 2 /Pillow/pillow_speaker2_video_021.mp4", "filename": "pillow_speaker2_video_021.mp4", "directory": "data/speaker sets/speaker 2 /Pillow", "source_directory": "Pillow", "matched_class": "pillow", "file_info": {"size_bytes": 2748818, "size_mb": 2.62, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 10_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 10_processed.mp4", "filename": "pillow 10_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 465183, "size_mb": 0.44, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 12_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 12_processed.mp4", "filename": "pillow 12_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 528676, "size_mb": 0.5, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 14_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 14_processed.mp4", "filename": "pillow 14_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 456373, "size_mb": 0.44, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 16_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 16_processed.mp4", "filename": "pillow 16_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 457051, "size_mb": 0.44, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 18_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 18_processed.mp4", "filename": "pillow 18_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 502011, "size_mb": 0.48, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 2_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 2_processed.mp4", "filename": "pillow 2_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 474321, "size_mb": 0.45, "exists": true}}, {"video_path": "data/the_best_videos_so_far/pillow 6_processed.mp4", "relative_path": "the_best_videos_so_far/pillow 6_processed.mp4", "filename": "pillow 6_processed.mp4", "directory": "data/the_best_videos_so_far", "source_directory": "the_best_videos_so_far", "matched_class": "pillow", "file_info": {"size_bytes": 520786, "size_mb": 0.5, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 12.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 12.mp4", "filename": "pillow 12.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 695778, "size_mb": 0.66, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 13.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 13.mp4", "filename": "pillow 13.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 610769, "size_mb": 0.58, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 18.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 18.mp4", "filename": "pillow 18.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 634301, "size_mb": 0.6, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 19.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 19.mp4", "filename": "pillow 19.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 744245, "size_mb": 0.71, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 2.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 2.mp4", "filename": "pillow 2.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 797609, "size_mb": 0.76, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 20.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 20.mp4", "filename": "pillow 20.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 858319, "size_mb": 0.82, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 7.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 7.mp4", "filename": "pillow 7.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 516299, "size_mb": 0.49, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 8.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 8.mp4", "filename": "pillow 8.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 595095, "size_mb": 0.57, "exists": true}}, {"video_path": "data/training 16.9.25/TRAINING SET 2.9.25/pillow 9.mp4", "relative_path": "training 16.9.25/TRAINING SET 2.9.25/pillow 9.mp4", "filename": "pillow 9.mp4", "directory": "data/training 16.9.25/TRAINING SET 2.9.25", "source_directory": "TRAINING SET 2.9.25", "matched_class": "pillow", "file_info": {"size_bytes": 491720, "size_mb": 0.47, "exists": true}}]}