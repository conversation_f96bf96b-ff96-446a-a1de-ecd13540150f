{"version": 3, "names": ["_assertThisInitialized", "self", "ReferenceError"], "sources": ["../../src/helpers/assertThisInitialized.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _assertThisInitialized<T>(self: T | undefined): T {\n  if (self === void 0) {\n    throw new ReferenceError(\n      \"this hasn't been initialised - super() hasn't been called\",\n    );\n  }\n  return self;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,sBAAsBA,CAAIC,IAAmB,EAAK;EACxE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CACtB,2DACF,CAAC;EACH;EACA,OAAOD,IAAI;AACb", "ignoreList": []}