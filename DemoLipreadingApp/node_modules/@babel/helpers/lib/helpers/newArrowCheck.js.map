{"version": 3, "names": ["_newArrowCheck", "innerThis", "boundThis", "TypeError"], "sources": ["../../src/helpers/newArrowCheck.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _newArrowCheck(innerThis, boundThis) {\n  if (innerThis !== boundThis) {\n    throw new TypeError(\"Cannot instantiate an arrow function\");\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAE;EAC3D,IAAID,SAAS,KAAKC,SAAS,EAAE;IAC3B,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;EAC7D;AACF", "ignoreList": []}