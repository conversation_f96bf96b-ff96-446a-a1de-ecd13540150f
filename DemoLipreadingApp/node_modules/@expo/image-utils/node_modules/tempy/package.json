{"name": "tempy", "version": "0.3.0", "description": "Get a random temporary file or directory path", "license": "MIT", "repository": "sindresorhus/tempy", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["temp", "temporary", "path", "file", "directory", "folder", "tempfile", "tempdir", "tmpdir", "tmpfile", "random", "unique", "uniq"], "dependencies": {"temp-dir": "^1.0.0", "type-fest": "^0.3.1", "unique-string": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}