{"name": "crypto-random-string", "version": "1.0.0", "description": "Generate a cryptographically strong random string", "license": "MIT", "repository": "sindresorhus/crypto-random-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["random", "string", "str", "rand", "text", "id", "identifier", "slug", "salt", "crypto", "strong", "secure", "hex"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}