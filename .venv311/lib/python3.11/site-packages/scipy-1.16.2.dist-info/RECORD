scipy-1.16.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.16.2.dist-info/LICENSE.txt,sha256=YKfQXXKe1cdjhSoyShy0Ij1mXUrT9LigwTCuXGDJPkk,46841
scipy-1.16.2.dist-info/METADATA,sha256=Mrv4zoxN12DcRzTin6mcTbpobUTs7F_oFZzSwko-uNA,62006
scipy-1.16.2.dist-info/RECORD,,
scipy-1.16.2.dist-info/WHEEL,sha256=KEx1caQrrMdaBVaCnLu1XvMcjVqPBe_79rJKYIEpL0o,122
scipy/.dylibs/libgcc_s.1.1.dylib,sha256=JHN67a-WVKVWNxRNTWp2gpqc4PEKO9dAo2s1lwvocto,183040
scipy/.dylibs/libgfortran.5.dylib,sha256=y3vaFqrRx7dXHd7X6v1Ml-GYHU550FZO6bBhVDkQfww,1901520
scipy/.dylibs/libquadmath.0.dylib,sha256=htURDqFWC4NqZkxxkXZcSEgU9bfZnYsqeT2QIRqlxP8,363936
scipy/__config__.py,sha256=7kmUZ3O498O2DYuR27eOlBpyiQQ2KWDFc6eHOxhXm5A,4903
scipy/__init__.py,sha256=pyQSpcYkQoduBzoB2hkQyEtyFNE4I4C3Goc7RwQg3Wg,4063
scipy/__pycache__/__config__.cpython-311.pyc,,
scipy/__pycache__/__init__.cpython-311.pyc,,
scipy/__pycache__/_distributor_init.cpython-311.pyc,,
scipy/__pycache__/conftest.cpython-311.pyc,,
scipy/__pycache__/version.cpython-311.pyc,,
scipy/_cyutility.cpython-311-darwin.so,sha256=zsGI2DLFyt0LpLMA8YGkTqExBJne3gDDNM0uBzPHzIM,173040
scipy/_distributor_init.py,sha256=zJThN3Fvof09h24804pNDPd2iN-lCHV3yPlZylSefgQ,611
scipy/_lib/__init__.py,sha256=CXrH_YBpZ-HImHHrqXIhQt_vevp4P5NXClp7hnFMVLM,353
scipy/_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api_compat_vendor.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api_no_0d.cpython-311.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-311.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-311.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-311.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-311.pyc,,
scipy/_lib/__pycache__/_elementwise_iterative_method.cpython-311.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-311.pyc,,
scipy/_lib/__pycache__/_sparse.cpython-311.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-311.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-311.pyc,,
scipy/_lib/__pycache__/_util.cpython-311.pyc,,
scipy/_lib/__pycache__/decorator.cpython-311.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-311.pyc,,
scipy/_lib/__pycache__/doccer.cpython-311.pyc,,
scipy/_lib/__pycache__/uarray.cpython-311.pyc,,
scipy/_lib/_array_api.py,sha256=oG1O6R_3DoFSaA2ZevUGoSaNtDXJMLVijiZ-KZn2LHQ,34594
scipy/_lib/_array_api_compat_vendor.py,sha256=H8MxZuHSs4TtWXgfEUs0_y0BQy57j6rX330DVashpZ4,393
scipy/_lib/_array_api_no_0d.py,sha256=zVB7D070dZ9Rc-7mXvlkqpv75TgcvCy_7PL0q6yZsbg,4453
scipy/_lib/_bunch.py,sha256=KV-kCN6lXFOp7HSiiVGSmDSwzKUITtuy8WWnO_rWLRo,8305
scipy/_lib/_ccallback.py,sha256=N9CO7kJYzk6IWQR5LHf_YA1-Oq48R38UIhJFIlJ2Qyc,7087
scipy/_lib/_ccallback_c.cpython-311-darwin.so,sha256=MMU3bg-EALnFgbQRqqfFmZ_T0OhJdNxlh0wHWXDLdLM,116944
scipy/_lib/_disjoint_set.py,sha256=o_EUHZwnnI1m8nitEf8bSkF7TWZ65RSiklBN4daFruA,6160
scipy/_lib/_docscrape.py,sha256=Jm2QPPIqNiAgKqoR5fX0P-bsBofYAtJhHbX2h1A63j0,23807
scipy/_lib/_elementwise_iterative_method.py,sha256=fC1ou8u6XHCpMnTRKsqwyP5_zW0gDu0TzGqw-nqWEAU,15023
scipy/_lib/_fpumode.cpython-311-darwin.so,sha256=UXTPmzOUZsnfbpKQfIfY_IrUFP5iFj2jDaX3qy5r8bs,50208
scipy/_lib/_gcutils.py,sha256=hajQd-HUw9ckK7QeBaqXVRpmnxPgyXO3QqqniEh7tRk,2669
scipy/_lib/_pep440.py,sha256=vo3nxbfjtMfGq1ektYzHIzRbj8W-NHOMp5WBRjPlDTg,14005
scipy/_lib/_sparse.py,sha256=Ifbhhdja4dpNNpsaO0VcjgRPcw2hqqHXOBF9eXRGsbE,875
scipy/_lib/_test_ccallback.cpython-311-darwin.so,sha256=svzY_5XIy_Isqyrka37tRP9Xh0efAzxHyK61KNijhaU,52968
scipy/_lib/_test_deprecation_call.cpython-311-darwin.so,sha256=wAED-vpTFWbPUU5eoBXMxECR4v3rVpuwEW-tkaEDRbY,74720
scipy/_lib/_test_deprecation_def.cpython-311-darwin.so,sha256=Hh9o9w6qIULx68_JSEYqevfyLloZJEhAjJpfjbHkjsU,55816
scipy/_lib/_testutils.py,sha256=qIz1sYnGCQfIDumEHDYDoQdMTHXlY40-DqVfI9hg7qk,12279
scipy/_lib/_threadsafety.py,sha256=ttPEh64SKLjhQGZIYSm_9d5bW4cjAXoRZCA_a5-nK9M,1453
scipy/_lib/_tmpdirs.py,sha256=z3IYpzACnWdN_BMjOvqYbkTvYyUbfbQvfehq7idENSo,2374
scipy/_lib/_uarray/LICENSE,sha256=yAw5tfzga6SJfhTgsKiLVEWDNNlR6xNhQC_60s-4Y7Q,1514
scipy/_lib/_uarray/__init__.py,sha256=Rww7wLA7FH6Yong7oMgl_sHPpjcRslRaTjh61W_xVg4,4493
scipy/_lib/_uarray/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-311.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=LZnSLJ2UK209jrMtocOMoc5grlNoob3tbb1HbW0XlAQ,20531
scipy/_lib/_uarray/_uarray.cpython-311-darwin.so,sha256=95OqMhZzCbfSnQZpaOp43Y4zVrRG-txSJj8gS38MzYI,120464
scipy/_lib/_util.py,sha256=Suos9e-8Jsp1y4nSJWsDX3tmwlkZCMlQmzbX6YxBYGM,49008
scipy/_lib/array_api_compat/__init__.py,sha256=zk6TZdJLBzT7Td3TKbCkYA1KIxKOsa-CKqDn0JCUq2I,992
scipy/_lib/array_api_compat/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/__pycache__/_internal.cpython-311.pyc,,
scipy/_lib/array_api_compat/_internal.py,sha256=pfbMacXgxBaLmhueWE54mtXrbBdxyLd2Gc7dHrxYtGk,1412
scipy/_lib/array_api_compat/common/__init__.py,sha256=4IcMWP5rARLYe2_pgXDWEuj2YpM0c1G6Pb5pkbQ0QS8,38
scipy/_lib/array_api_compat/common/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_helpers.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/_aliases.py,sha256=xvZcAGCBbbujmjh76EvaYDzgPfQhaHK8QH--CQI906U,19644
scipy/_lib/array_api_compat/common/_fft.py,sha256=ckCR2uHtz0iaOkcuvqVunhz1khIdxQNKuVU0x1bfrq8,4669
scipy/_lib/array_api_compat/common/_helpers.py,sha256=zIz2QmS4LEI-aT05xMzXTgZ6Y6aULKbxlZxWa_R-lb4,31586
scipy/_lib/array_api_compat/common/_linalg.py,sha256=Wdf0FzzxJNEiGhOOsQKg8PnMusM3fVeN5CA4RBItF_Y,6856
scipy/_lib/array_api_compat/common/_typing.py,sha256=Z5N8fYR_54UorD4IXFdOOigqYRDp6mNa-iA7703PKf4,4358
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=8KfEs6ULcXuZ4AUKBD_7L3XZfW8TOQayZPerR_YLeSI,390
scipy/_lib/array_api_compat/cupy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=OgOoVRk-TI9t0hCsI82VLkebkZRdN7aXjamWMRw0yYQ,4842
scipy/_lib/array_api_compat/cupy/_info.py,sha256=g3DwO5ps4bSlFU2pc_f4XTaLrkCYuSDlCw0Ql2wuqM8,10125
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=dkA_sAAgU1Zb1PNopuOsywbLeFK-rLWAY4V4Vj3-x0I,628
scipy/_lib/array_api_compat/cupy/fft.py,sha256=xCAC42CNAwAyVW7uCREsSoAV23R3rL2dqrT7w877zuE,842
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=nKOM-_wcOHzHhEeV9KBzcMVNlviJK4nP1nFBUtvnjTM,1444
scipy/_lib/array_api_compat/dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/array_api_compat/dask/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__init__.py,sha256=OkadrcCZUdp3KsB5q2fhTyAACW12gDXxW_A4ANGcAqY,320
scipy/_lib/array_api_compat/dask/array/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/_aliases.py,sha256=ZmoAVGbsj04gcfE7R0V6N_7AXCZrhYSFXXfzJfJ5O4Y,10668
scipy/_lib/array_api_compat/dask/array/_info.py,sha256=rpfvNrS4ZaZMEcaomlRFxx7Dqb_tohhDFvI6qYoaivI,12618
scipy/_lib/array_api_compat/dask/array/fft.py,sha256=OZxTcLBCXKgVpbMo7Oqn9NH_7_9ZUHQdB6iP8WSYVfY,589
scipy/_lib/array_api_compat/dask/array/linalg.py,sha256=AtkHftJ3hufuuSlZhRxR0RH9IureEet387rpn1h38XU,2451
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=7SOguTm7-yJgJPnFTlbk_4bPTltsgKLbkO59ZmoCODg,853
scipy/_lib/array_api_compat/numpy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=SKaCfzc2eY1eAu3Yzm3JVuR3uUqL7PoXf6GyYyXpcw4,5715
scipy/_lib/array_api_compat/numpy/_info.py,sha256=8KNJ09jKFfMH20wff67GJVPyoZ-e8-OUHF88THx-1Cs,10782
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=O03YoguInLXMcL5Q0JKHxRXSREgE0DCusVAZKAv-l10,626
scipy/_lib/array_api_compat/numpy/fft.py,sha256=7oxAzAnFwsAH0J43eXFKRkJ_GKCVEC-7G_lz56pVBz8,779
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=ORu4MhuN6F5EXOy-lYHxfMHkRpVRx2VEC29rRwB8Bws,4039
scipy/_lib/array_api_compat/torch/__init__.py,sha256=o351abwQmNWcX00GBnGYHrpfM8pFiieFWRaf0NI-KFg,549
scipy/_lib/array_api_compat/torch/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=w_exCqFcAuB3TXtiqk_NpSHJ8D3ZawulLdjFxTvujQc,30261
scipy/_lib/array_api_compat/torch/_info.py,sha256=-H2xD9z9SMf3GjIOW0jeRTUOvh4s8E9p9u_4LqawRZM,11889
scipy/_lib/array_api_compat/torch/_typing.py,sha256=-uCkuTie1g9hb4vwPLK9eEnir9Zp67wAhrfaI_o-35E,108
scipy/_lib/array_api_compat/torch/fft.py,sha256=9YO23YEbQr49gq_DrfJ7V0G41G7WlJC6rJAeqqOP7dw,1738
scipy/_lib/array_api_compat/torch/linalg.py,sha256=acbcg80CjamMQ0JDAkrWL7FkyEW5MfmGVzQsrRT00jM,4799
scipy/_lib/array_api_extra/__init__.py,sha256=hDYzsy3XZ6AWPDGdqMsubxTun_z-VTG_DNN1PIHvjlQ,665
scipy/_lib/array_api_extra/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_extra/__pycache__/_delegation.cpython-311.pyc,,
scipy/_lib/array_api_extra/__pycache__/testing.cpython-311.pyc,,
scipy/_lib/array_api_extra/_delegation.py,sha256=LCYXvd7a8dvQMxGt6Gd0tANEmPGG-g_AfTKkDW3bKtU,6111
scipy/_lib/array_api_extra/_lib/__init__.py,sha256=Mht4YV9Rpkzg5kORPlgjOOXk4y7bvdngowS6KGSHqNE,36
scipy/_lib/array_api_extra/_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_at.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_backends.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_funcs.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_lazy.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_testing.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/_at.py,sha256=AgmWvHzhGnCY09BGTIMDBj6PiN0JFWdxl89Lpm-leC0,15362
scipy/_lib/array_api_extra/_lib/_backends.py,sha256=ZgwBySjeewhZQrUrAhp9ahgEYKdXAffQ4qm7QDwNZOM,1468
scipy/_lib/array_api_extra/_lib/_funcs.py,sha256=NEXw26WeyygBOoMzDSljVcto1IDN2C9y5QwTW76mP6k,29771
scipy/_lib/array_api_extra/_lib/_lazy.py,sha256=UrtSnariXoJO-9Efhci8grpqw5lQ9wyD5uQ9U6V5ZTU,13934
scipy/_lib/array_api_extra/_lib/_testing.py,sha256=kzNdLylEFOJJSKPw9dUBn-kHy5TDa_5DqpbAEOxA0Lg,9170
scipy/_lib/array_api_extra/_lib/_utils/__init__.py,sha256=8ICffM2MprXpWZd8ia0-5ZTnKtDfeZD0gExLveDrXZs,49
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_compat.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_helpers.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/_compat.py,sha256=4A5a_S4qo88_H8LKmQal2e_1PVQAY1eInKDZsCgPNYU,1812
scipy/_lib/array_api_extra/_lib/_utils/_compat.pyi,sha256=AsYR_QCjWx7cd8VWDu5_PzbiYGSEJZKeEmgB6JRNUdE,1750
scipy/_lib/array_api_extra/_lib/_utils/_helpers.py,sha256=LrMpnfqTfjBwPav_pZYT3OBpPLAeoGkxAhhZunvTm0Y,17509
scipy/_lib/array_api_extra/_lib/_utils/_typing.py,sha256=0NSYWpdXH58Dx9T7HsNPM0NW2iPlTZS6xOrR-5tFOYc,228
scipy/_lib/array_api_extra/_lib/_utils/_typing.pyi,sha256=-XcCOYxOoKjgPeo3w9Pqg1KyYm6JTKm6aO_jD22CGoU,4725
scipy/_lib/array_api_extra/testing.py,sha256=PQVnuC3WuEVYryNU8CsGfpLW4VBIPJVh0nuix1pSr-o,12908
scipy/_lib/cobyqa/__init__.py,sha256=9Gj-EtpYGRmh0-ADiX0t0psItcvMgzIMwFDzlvOzcE8,578
scipy/_lib/cobyqa/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/framework.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/main.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/models.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/problem.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/settings.cpython-311.pyc,,
scipy/_lib/cobyqa/framework.py,sha256=lIeKCkDLxHbMmSTiMcyasvVe77jVvh_YTOYX0HnK4Qk,38900
scipy/_lib/cobyqa/main.py,sha256=wz0M2iqFfzeTaZUq_j1TkF_9V_SJ1t73A-0fdH0eSs4,57527
scipy/_lib/cobyqa/models.py,sha256=cAM8_np_xFSRwKsjaMRZu9Dc9xQOQPAZVWxsvR_7qjE,50656
scipy/_lib/cobyqa/problem.py,sha256=SiPgmiFTxiW5yJ_FVf37Z9GQGo6Gx_fJ3RXMzhsrn40,40203
scipy/_lib/cobyqa/settings.py,sha256=ogfiShxuPHsMfW16OGSwB9-mIPRiuWZSGXBOCO2HDvw,3826
scipy/_lib/cobyqa/subsolvers/__init__.py,sha256=VmFBpi-_tNa8yzNmu_fufewmPTnCU6ycNCGcN34UBcc,341
scipy/_lib/cobyqa/subsolvers/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/geometry.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/optim.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/geometry.py,sha256=dgS-C0QBUhkzPhHULFIRbnbFOIEB005GyPYE-i-cuFY,14173
scipy/_lib/cobyqa/subsolvers/optim.py,sha256=hIseVqrPyI3ezICGNXkCtKlpqvAO2W6ZQe0n7sxfkss,45512
scipy/_lib/cobyqa/utils/__init__.py,sha256=sw6g402vXaXwX7rMhxrNl5PD5OBs89l5f3XNcYApRHs,359
scipy/_lib/cobyqa/utils/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/exceptions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/math.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/versions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/exceptions.py,sha256=N1JdmUxHnME95wEZHyeeF_M6GXPEqH5t3qzuXig49YE,483
scipy/_lib/cobyqa/utils/math.py,sha256=beT-Tib41TJWZecjnKhSfu4foOLLaHlWj5CcyRhdSl4,1611
scipy/_lib/cobyqa/utils/versions.py,sha256=eBOlEGAKFCfjFqVprdali3M1G7l0k_kxb7ku-Lz2bU0,1465
scipy/_lib/decorator.py,sha256=_g2s5lG2XHGWPG6csfC3ZhN_JNWP6gH7qU8HkHI6Zrk,15014
scipy/_lib/deprecation.py,sha256=2xwTeh_7Uc71zmnJW264zxjvh0LUWQqZsH6s95dQDyo,9840
scipy/_lib/doccer.py,sha256=W1HgV3SpTFpZJ0oyiN9Yn4vuX5pwxx27anH-sGNv19Y,10721
scipy/_lib/messagestream.cpython-311-darwin.so,sha256=0Iz2eaf9PwAFbayLMoUK80XI-R_VT0czItK7RaPgZpQ,98512
scipy/_lib/pyprima/__init__.py,sha256=YakFMfftDrq3fBu2-_O3FWjeu41511VEPDTtzJ0IKX0,8461
scipy/_lib/pyprima/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/pyprima/cobyla/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/cobyla.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/cobylb.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/geometry.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/initialize.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/trustregion.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/update.cpython-311.pyc,,
scipy/_lib/pyprima/cobyla/cobyla.py,sha256=YiuAhtZ5u13uDciL7sc0qxVVpH5t5IKJ2R0X7qTFMwo,22146
scipy/_lib/pyprima/cobyla/cobylb.py,sha256=T9ZRYBYZdAYvRLJ9sjAYyQb3vdcRxMmj3AukIidw9L0,40764
scipy/_lib/pyprima/cobyla/geometry.py,sha256=84zrSvKNCr-WnXqFrnnb-nE11yVAI_CME79nhVc4ZvM,10645
scipy/_lib/pyprima/cobyla/initialize.py,sha256=ft98aJ03PvUUvIXluk-4IUZLbDQPF3ZLdFUhfpRh5Zg,9409
scipy/_lib/pyprima/cobyla/trustregion.py,sha256=6UNiTytfEyxW1-qHpqBGzcegRyrnx0A82XwLS5b9N_U,25200
scipy/_lib/pyprima/cobyla/update.py,sha256=PwDRKMY_VAhBKVSOkMUCOSMhGezpfiafIansW1vqI4I,13882
scipy/_lib/pyprima/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/pyprima/common/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/_bounds.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/_linear_constraints.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/_nonlinear_constraints.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/_project.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/checkbreak.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/consts.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/evaluate.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/history.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/infos.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/message.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/powalg.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/preproc.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/present.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/ratio.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/redrho.cpython-311.pyc,,
scipy/_lib/pyprima/common/__pycache__/selectx.cpython-311.pyc,,
scipy/_lib/pyprima/common/_bounds.py,sha256=ZzvGVDJigjFkxb4Qkct89SRta7VPS7C_XFm0qKSy090,1636
scipy/_lib/pyprima/common/_linear_constraints.py,sha256=SZHsoLLWFVtJ1TPPamd0CPXmKmVlnrgT-qCyYZo2CkQ,2162
scipy/_lib/pyprima/common/_nonlinear_constraints.py,sha256=0OGZwFpa8JqtTm4rZKb2LL8oxx1Sld4ANwfH-00Qy4A,2150
scipy/_lib/pyprima/common/_project.py,sha256=oX1l_vGwYod1knQ08t_j2MZ1hBOT_szT621GXfk67nE,7863
scipy/_lib/pyprima/common/checkbreak.py,sha256=kDbwy_CG-0ZUlrfrQ_SKwlxyPjqZHH1-z0HTvXJq7G0,3451
scipy/_lib/pyprima/common/consts.py,sha256=gw-JKckLhBHPXZLLp1qMrVsqNP9PJ-kcgtBvfCajk-g,1310
scipy/_lib/pyprima/common/evaluate.py,sha256=aKVctlTqacdI_n329ipEH-ZTpQNwAX-01UavdsXlUeY,3150
scipy/_lib/pyprima/common/history.py,sha256=c6vWe7XJ5JMzRm7TzaPfYhmHlaZGoUDZzEvLdfZNbAA,1361
scipy/_lib/pyprima/common/infos.py,sha256=fcIk6zFWQrtUhtvOHYcYUzZCDmgEAY39fjRshjOb9M4,704
scipy/_lib/pyprima/common/linalg.py,sha256=QwbXquUpao34qwFoQreJ7ocxacCs5pO_7p9Gn9TbB78,14280
scipy/_lib/pyprima/common/message.py,sha256=D1UwykQhQ16hekF4-wxC1Xw1Qs9gsm_0hXG-yf3VK20,9663
scipy/_lib/pyprima/common/powalg.py,sha256=BWD8urM3sF_T0e47iHZCKvjIZgcDxsDrLHMtDMcQoUE,6785
scipy/_lib/pyprima/common/preproc.py,sha256=Ge0SyRxy3f47uf1anX8WmFjfkOdw82czkMgC8FlDiJs,13820
scipy/_lib/pyprima/common/present.py,sha256=TvFOVG53WvXwjsTqz2tfZzgmV0Uv7dlX0DQ1e2IO92M,146
scipy/_lib/pyprima/common/ratio.py,sha256=ePcZVLwu4NB29FgVx-qYr3Yvgf_9Wx16Lyz9qbjJDyM,1823
scipy/_lib/pyprima/common/redrho.py,sha256=ES_D2yucGa_uMGcvlSL8-z4ZxPv2BKoK8mCMw1DZqvE,1258
scipy/_lib/pyprima/common/selectx.py,sha256=8IYf6BB-OnOOnuxn8_9YsrllaLFLtIKQY5NqTHRc61Q,14120
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_config.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_doccer.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-311.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=Uadt4yXwuLDMCSbf4cpMszR_5NOeVQC1E_v4NZAeJR4,3729
scipy/_lib/tests/test__pep440.py,sha256=u9hPoolK4AoIIS-Rq74Du5SJu5og2RxMwgaAvGgWvRo,2277
scipy/_lib/tests/test__testutils.py,sha256=P4WDJpUgy19wD9tknQSjIivuQvZF7YUBGSBWlur2QRA,800
scipy/_lib/tests/test__threadsafety.py,sha256=qSfCF5OG_5lbnSl-grmDN_QCU4QLe-fS3sqnwL04pf8,1322
scipy/_lib/tests/test__util.py,sha256=g9C1pPrJCgh0fE16yULUlVLdGhZDn2ph9HVYatqsscM,23922
scipy/_lib/tests/test_array_api.py,sha256=a33zHgh03F8yUqbIuxI2zct9ppFo3mP-B0oBYjtWDMw,13383
scipy/_lib/tests/test_bunch.py,sha256=USL2wl6PT4gAuuNk5CipnNrG45GZPZ8y1B7BqtIDqKM,6389
scipy/_lib/tests/test_ccallback.py,sha256=wQ0Bwewx7KWJWspmdTYTPjRgm8A3A5mZ1ssLIig5wfE,6040
scipy/_lib/tests/test_config.py,sha256=ekM39jzkDFcuk3ahIMn-j4JUz3kZeSDxxB_2WRRxULM,1275
scipy/_lib/tests/test_deprecation.py,sha256=pIia1qGES_ABOfbqLSSlXzmLmeBjpziyvh9J2mUUcMA,390
scipy/_lib/tests/test_doccer.py,sha256=2HGlzqu7dgJ7collFy6SunjKc4lKMFo4TZIUQCHlVoU,4053
scipy/_lib/tests/test_import_cycles.py,sha256=K4LfxIHzFRIj4XGGmpRhYj4Kij8GXYxKGbIX8WfjUWQ,586
scipy/_lib/tests/test_public_api.py,sha256=tsHTY1YqVRxjZXYucDE_gpRfwUlKWaG6q0CIt1UxNXw,18800
scipy/_lib/tests/test_scipy_version.py,sha256=kVoxuBUidCHsVpvybRPoVJzkv2hUixRwuDAEAqPgpaA,918
scipy/_lib/tests/test_tmpdirs.py,sha256=DiSY_ReQtD9Ou01pJ49MVY1aT6L62W2Odbbr-zEm3zI,1337
scipy/_lib/tests/test_warnings.py,sha256=ZQ_4o16m2b--0v8erteoUd2pA134GzMRZhTV9vfuhqI,4949
scipy/_lib/uarray.py,sha256=4X0D3FBQR6HOYcwMftjH-38Kt1nkrS-eD4c5lWL5DGo,815
scipy/cluster/__init__.py,sha256=pgzWiWR5smQ3rwud2dhnLn6dpkD5lju_moElQp_zhoE,880
scipy/cluster/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-311.pyc,,
scipy/cluster/__pycache__/vq.cpython-311.pyc,,
scipy/cluster/_hierarchy.cpython-311-darwin.so,sha256=Jo-caQofTE4HVKbHil9BpeWTuNA_szVLNdN6K0To8UI,259424
scipy/cluster/_optimal_leaf_ordering.cpython-311-darwin.so,sha256=TG915iHu9mL-LaWleFwRTC8_XrkMeZU6d7eXCxevYHc,170400
scipy/cluster/_vq.cpython-311-darwin.so,sha256=t0SiGqa-LQQLKFkzFQaKQi-V_IPyfeV7ep-DG3qrGrE,130392
scipy/cluster/hierarchy.py,sha256=j9LKlUFqn1Nv8rAxRZYZbf9zW42YPA8xtEvojqmrKr4,156811
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-311.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=7syUYdIaDVr7hgvMliX0CW4386utjBJn1DOgX0USXls,6850
scipy/cluster/tests/test_disjoint_set.py,sha256=EuHGBE3ZVEMnWFbCn8tjI-_6CWrNXfpnv5bUBa9qhWI,5525
scipy/cluster/tests/test_hierarchy.py,sha256=j5B3V67-0Ws4U5M9MuGqMbBK7IZXc1lubntMVlm-YTA,50282
scipy/cluster/tests/test_vq.py,sha256=JMavr2NlWjz-EUpus9B2AXhSNFAnl1mhrqHrkMUBqr0,18212
scipy/cluster/vq.py,sha256=HKFZDBazq35WOj8_1Ofg875dG2mHioCDmQAB82sR6GY,30899
scipy/conftest.py,sha256=KrV2IrSaXIkaHQNKomD0TuyJ2S5FLylvOxaS6RzkECw,27502
scipy/constants/__init__.py,sha256=1Iqylk8TvAxegNKIcFIUVXwiH5ItKpdKtCcVPhEBvPQ,14839
scipy/constants/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/__pycache__/_codata.cpython-311.pyc,,
scipy/constants/__pycache__/_constants.cpython-311.pyc,,
scipy/constants/__pycache__/codata.cpython-311.pyc,,
scipy/constants/__pycache__/constants.cpython-311.pyc,,
scipy/constants/_codata.py,sha256=fIhZGWMCGLGSwO3rnNmDEisAN1rGLwkNbSlwdZDpowQ,202354
scipy/constants/_constants.py,sha256=1BiP8rT7BdaAwCpLbpXQzrCVCcIJPmmc4oh1fg5WHOo,10571
scipy/constants/codata.py,sha256=ThmW8ohzndi-4-WtyVXxSrW40MnLIz1XoqRcm2RgSHw,614
scipy/constants/constants.py,sha256=w7sGxSidD2Q9Ged0Sn1pnL-qqD1ssEP1A8sZWeLWBeI,2250
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-311.pyc,,
scipy/constants/tests/test_codata.py,sha256=gxakBdJjXuXFPPgJvJAj50s4IXCJH3whaeXZjEcso-g,2854
scipy/constants/tests/test_constants.py,sha256=Gz5YfzcqFnZsHuuZDZ4QOcegWL-2IGIZwZ9cNp-ubTM,4247
scipy/datasets/__init__.py,sha256=X_9AbefPK1_pg-eG7g3nn--JhoHeDsrEFbJfbI5Hyak,2802
scipy/datasets/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-311.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-311.pyc,,
scipy/datasets/__pycache__/_registry.cpython-311.pyc,,
scipy/datasets/__pycache__/_utils.cpython-311.pyc,,
scipy/datasets/_download_all.py,sha256=08-F2A9IaCzAYHy5yzsiHkEK_9zDA4ISDmgy2OanZbY,2095
scipy/datasets/_fetchers.py,sha256=xlLSxIhtysobPe3kYFL717t31qRspTwoqe31iGtevvA,6941
scipy/datasets/_registry.py,sha256=br0KfyalEbh5yrQLznQ_QvBtmN4rMsm0UxOjnsJp4OQ,1072
scipy/datasets/_utils.py,sha256=o-3PNQwKjZS8Rx8p3VrwHFkDKXRD5cETvV_m9vaQRv8,2966
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/datasets/tests/test_data.py,sha256=6DJtyMDmwi_ghOrDuryVakZQExFq-MIKiuJi_Cr7kdM,4213
scipy/differentiate/__init__.py,sha256=nZ3imDWtf1QzImE-xsrYHE4kuOa8tEuc99Hl0zAFqzI,621
scipy/differentiate/__pycache__/__init__.cpython-311.pyc,,
scipy/differentiate/__pycache__/_differentiate.cpython-311.pyc,,
scipy/differentiate/_differentiate.py,sha256=vB0JAJgv486JpJAoqUOSN7sOebAC8w5mE-mRy6c4BKI,50815
scipy/differentiate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/differentiate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/differentiate/tests/__pycache__/test_differentiate.cpython-311.pyc,,
scipy/differentiate/tests/test_differentiate.py,sha256=VCfZq-SyAm5LP9CI1cplRQRptRgCOWpoYU2UjbhLU1o,28153
scipy/fft/__init__.py,sha256=0cjHIwyHnjoz1XUUe3OB70vrQR0-pFp8Uv34-U-FGRg,3632
scipy/fft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/__pycache__/_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_basic.cpython-311.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_helper.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-311.pyc,,
scipy/fft/_backend.py,sha256=5rBxK8GQtCMnuPHc-lNQdpH4uFFZ9_5vBukkDv6jRRA,6544
scipy/fft/_basic.py,sha256=W5Wv4_JQJNsCiKRKZcfLtdxKDvFOd27Sls-hM1Wc_ao,63546
scipy/fft/_basic_backend.py,sha256=Qms-BE7DCJYNSq9Vd5utnKiwVTqRIUzLYYEiMyTdpfE,7447
scipy/fft/_debug_backends.py,sha256=RlvyunZNqaDDsI3-I6QH6GSBz_faT6EN4OONWsvMtR8,598
scipy/fft/_fftlog.py,sha256=JeLVCAgfB99brT2Ez9tzdapmhWrTfYCUYEi2KTvPzIQ,7864
scipy/fft/_fftlog_backend.py,sha256=UgoePwhoMoLxvQ5soSUZkVWvWWTP7y1xWVAD9BlrdJY,5304
scipy/fft/_helper.py,sha256=JVsmPX1tMXJywsVf_SEIdy64qs5feZcJ2pQXXPJbIzE,11261
scipy/fft/_pocketfft/LICENSE.md,sha256=wlSytf0wrjyJ02ugYXMFY7l2D8oE8bdGobLDFX2ix4k,1498
scipy/fft/_pocketfft/__init__.py,sha256=dROVDi9kRvkbSdynd3L09tp9_exzQ4QqG3xnNx78JeU,207
scipy/fft/_pocketfft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=4HR-eRDb6j4YR4sqKnTikFmG0tnUIXxa0uImnB6_JVs,8138
scipy/fft/_pocketfft/helper.py,sha256=Iifq1BGj7IQIHPhBNTaaP2_Rry_7TbINjx1rkntCKjY,6837
scipy/fft/_pocketfft/pypocketfft.cpython-311-darwin.so,sha256=JK6xIs_a7w2WkKT0snDQFP5JcJvSOr5FbJmUhvqdJfY,920320
scipy/fft/_pocketfft/realtransforms.py,sha256=4TmqAkCDQK3gs1ddxXY4rOrVfvQqO8NyVtOzziUGw6E,3344
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=jsqSZLy8m3mJsFT39EonX7a0iKMjDzApyZTvowcKAuw,35633
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=YKGdhXrDJ-xcTjRSCzW2xm1IXBtn35NQe38bebkiVAQ,16863
scipy/fft/_realtransforms.py,sha256=roow-1oLm3nQNOm6Xgpl7N9bB3wo9NGvFISTQNaV8g4,25780
scipy/fft/_realtransforms_backend.py,sha256=u4y4nBGCxpTLVqxK1J7xV6tcpeC3-8iiSEXLOcRM9wI,2389
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/tests/mock_backend.py,sha256=p17Hfg6xuoF6Ldxwe1PZ-79Lf_r9FyJUR00N4TokM8k,2685
scipy/fft/tests/test_backend.py,sha256=DFJ6OKV6gRw4p9OuVfy1ENTeLJCbYS2GuppwpJnwQGQ,4285
scipy/fft/tests/test_basic.py,sha256=0v7XObukDi4-LQlxrhG784cbFHt9K_6ZIjFyEMwEmSQ,20415
scipy/fft/tests/test_fftlog.py,sha256=S1Lz3N0L_5Gmb8Ny8zXTuatJrmXUacuE58A1I0BK25M,7643
scipy/fft/tests/test_helper.py,sha256=Sh9E59ey3v0vGqu94VMYH8iX7Ck2XqgcC83sKHQm5gQ,19522
scipy/fft/tests/test_multithreading.py,sha256=JMSXQocScFghpsy47zov03R5MbEY0Z3ROGt6GxFeWzo,2150
scipy/fft/tests/test_real_transforms.py,sha256=S_EXMcrOByjTmzBXH2jXCFpQQwE-NJgSGbG_njoS1i0,9166
scipy/fftpack/__init__.py,sha256=rLCBFC5Dx5ij_wmL7ChiGmScYlgu0mhaWtrJaz_rBt0,3155
scipy/fftpack/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fftpack/__pycache__/basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fftpack/_basic.py,sha256=Sk_gfswmWKb3za6wrU_mIrRVBl69qjzAu9ltznbDCKs,13098
scipy/fftpack/_helper.py,sha256=8r6Hh2FA5qTzYyn8y4jfaG41FXMfqQyK6SN8x1dIbaE,3348
scipy/fftpack/_pseudo_diffs.py,sha256=T39Owz8EgL4oqmViBT0ggen9DXOtNHWRxh-n6I7pLyw,15936
scipy/fftpack/_realtransforms.py,sha256=2k91B3tSnFm6gKsQn-hRGx4J238CKvqwvQevKgDMuaQ,19222
scipy/fftpack/basic.py,sha256=i2CMMS__L3UtFFqe57E0cs7AZ4U6VO-Ted1KhU7_wNc,577
scipy/fftpack/convolve.cpython-311-darwin.so,sha256=1L1p-vCKdjKG7W6jLNaQ_KtMb-cXINbbFdx2bjpgwDE,131936
scipy/fftpack/helper.py,sha256=M7jTN4gQIRWpkArQR13bI7WN6WcW-AabxKgrOHRvfeQ,580
scipy/fftpack/pseudo_diffs.py,sha256=h0vkjsSqAThy7OdTkYWVxQqZ3rILohg7MXJqf5CGMTE,658
scipy/fftpack/realtransforms.py,sha256=9-mR-VV3W14oTaD6pB5-RIDV3vkTBQmGCcxfbA8GYH0,595
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=xq92TCdPbZbh6DStQ2dTc958GYSe453goeijDJoDGrQ,30472
scipy/fftpack/tests/test_helper.py,sha256=8JaPSJOwsk5XXOf1zFahJ_ktUTfNGSk2-k3R6e420XI,1675
scipy/fftpack/tests/test_import.py,sha256=dzyXQHtsdW2WL5ruVp_-MsqSQd_n-tuyq22okrzXlGw,1156
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=ZJU6AkkH6jKjebu_-Ant-dT6tUGwo1Jx9c5kou1floU,13733
scipy/fftpack/tests/test_real_transforms.py,sha256=KxSsIxsMzdeif5Jej3MuERF3lC8Cyv_UZnJhLPuKpIU,24460
scipy/integrate/__init__.py,sha256=CmPLfkF66jXhHsKyQPOsvFEc9nxicRYwl6WDAa7cfJk,4373
scipy/integrate/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-311.pyc,,
scipy/integrate/__pycache__/_cubature.cpython-311.pyc,,
scipy/integrate/__pycache__/_lebedev.cpython-311.pyc,,
scipy/integrate/__pycache__/_ode.cpython-311.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-311.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-311.pyc,,
scipy/integrate/__pycache__/dop.cpython-311.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/__pycache__/odepack.cpython-311.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-311.pyc,,
scipy/integrate/__pycache__/vode.cpython-311.pyc,,
scipy/integrate/_bvp.py,sha256=Ot2q657UUVaYYlFHe8bWFR_mrRgkienJG6YO3EB2__o,41213
scipy/integrate/_cubature.py,sha256=AaGqpXw3IpqGTyxNIH13ID0_9A_oqdvVUBq7fKSR1eU,25705
scipy/integrate/_dop.cpython-311-darwin.so,sha256=MJtMFCS8s3HqnyBMO_zfV6m7JPUzWVlpXQ3cRA1FX1Y,142144
scipy/integrate/_ivp/__init__.py,sha256=gKFR_pPjr8fRLgAGY5sOzYKGUFu2nGX8x1RrXT-GZZc,256
scipy/integrate/_ivp/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-311.pyc,,
scipy/integrate/_ivp/base.py,sha256=Mlef_dgmn0wzjFxZA3oBbtHrQgrfdZw_8k1mLYNZP4A,10295
scipy/integrate/_ivp/bdf.py,sha256=tTN2OiFRjGlIT-PkrCLi-mBfUmcAZ8NEprFSjwR_K5U,17501
scipy/integrate/_ivp/common.py,sha256=GVKTcx-QO7WPr2ejNAi94aEdMv03zFVOr24Q1w2rZ2I,15745
scipy/integrate/_ivp/dop853_coefficients.py,sha256=OrYvW0Hu6X7sOh37FU58gNkgC77KVpYclewv_ARGMAE,7237
scipy/integrate/_ivp/ivp.py,sha256=DGmLGk4TbhkGhBiJvnbeNScZzLdnm-6nJoWt83hrz-s,31743
scipy/integrate/_ivp/lsoda.py,sha256=t5t2jZBgBPt0G20TOI4SVXuGFAZYAhfDlJZhfCzeeDo,9927
scipy/integrate/_ivp/radau.py,sha256=0KpFk0Me857geCXbbvAyTkqbrO8OI_2kLTdzGLpqYlY,19676
scipy/integrate/_ivp/rk.py,sha256=-l1jAJF_T5SeaZsRb1muFHFZ1cYUfVXZQNydMwOJEFY,22800
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-311.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=A0hw3AqENXeTFp1Rcb_4ayEsLYLuMVFz9s7UrglatLQ,42823
scipy/integrate/_ivp/tests/test_rk.py,sha256=K9UxZghBzSL2BzmgLndPJcWOWV4Nr530TGKWakpsoeM,1326
scipy/integrate/_lebedev.py,sha256=Tj3I_tnQ3_mfARK_scDsd9aM5dLe9To-GeaCda5OMKw,262024
scipy/integrate/_lsoda.cpython-311-darwin.so,sha256=kFigX87EzIBfcDku-yNcv_gadXX6_WdqjQ4Zs1xM4Go,126928
scipy/integrate/_ode.py,sha256=_4luseJWv01Zj_Avdk6xtNprZ_HoyXm1PPNiGsQmq4c,48622
scipy/integrate/_odepack.cpython-311-darwin.so,sha256=OO_w8aCIAkd3JQ6fmQJlmR-6Ag-tiatjUC0mmqwsqlc,122496
scipy/integrate/_odepack_py.py,sha256=DhHLB7rx0p6TrQQzQQlwzqcb8oMuFRDra0nIFryb0M8,11231
scipy/integrate/_quad_vec.py,sha256=Sk-HNGPWTfw8fALxl7vLaDSxEOWeqb6LlJJOILlBHsc,21582
scipy/integrate/_quadpack.cpython-311-darwin.so,sha256=VSEakCFNwmTSu1Jo6OvC0EDFIlfl2YaRtjhI2n4O4Ss,104176
scipy/integrate/_quadpack_py.py,sha256=H4ftrZKqWLvH4St-FYf56D_fvCcxl2vkGpkBK9DQxDg,53704
scipy/integrate/_quadrature.py,sha256=mvcABe0R7Tffe1NIgDXJsFS0cxhr2ca2oMYyv9h40ao,47879
scipy/integrate/_rules/__init__.py,sha256=JNlDLTPYR-FVDeWbm9BHOot47OA8tvOj22g2iJlEsBg,328
scipy/integrate/_rules/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_base.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_kronrod.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_legendre.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_genz_malik.cpython-311.pyc,,
scipy/integrate/_rules/_base.py,sha256=iwb872yqwq2Y9LbCTujUKI1CvwZurApCeJAlOR4wrnE,17927
scipy/integrate/_rules/_gauss_kronrod.py,sha256=ULpHMJRd0J99IFwNufur9BYG8EQhxlGj-OdCBgnE8yk,8473
scipy/integrate/_rules/_gauss_legendre.py,sha256=KJSMmztXRqTvpmkB-ky-WSVIqAMg_GcWoewTcRxJ1Cw,1733
scipy/integrate/_rules/_genz_malik.py,sha256=104fosqAnmCI992oY-Z9V_QiuG2ruWLmGS2U_EdshEw,7308
scipy/integrate/_tanhsinh.py,sha256=ec5BwtdswWRLDrX6__hNfL3OwUBn2DKj9tbP_sAyuAI,61339
scipy/integrate/_test_multivariate.cpython-311-darwin.so,sha256=wguSEbYZ9TE842_rC2s6NRkTL01tox-RQBfoUu6X5Q8,50664
scipy/integrate/_test_odeint_banded.cpython-311-darwin.so,sha256=IpphdUNOWHqSPe-59wWRMeqPiTcMjyaJJZxM1Wsf6w0,126512
scipy/integrate/_vode.cpython-311-darwin.so,sha256=Wv5oFvlV3O77E55xmCFeXvRgbtEX7YyVNHcZ54oqvYg,195264
scipy/integrate/dop.py,sha256=Kx5Ed_Te81X09bvGmBUq3-_kQNdTIsOdO7ykjEpEG9c,422
scipy/integrate/lsoda.py,sha256=hUg4-tJcW3MjhLjLBsD88kzP7qGp_zLGw1AH2ZClHmw,436
scipy/integrate/odepack.py,sha256=G5KiKninKFyYgF756_LtDGB68BGk7IwPidUOywFpLQo,545
scipy/integrate/quadpack.py,sha256=vQNE5jQ-dFpH26er1i8LJSkylFVbeSgVGLwSRQawfYg,604
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_cubature.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_tanhsinh.cpython-311.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=Rzhy0XsKdy5MSfo9wOjJjgueNkYa3Rujujxn9D8-zkw,6338
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=a6QODlYQLpm9m43K3Ocz320cFQrP0P3_nlMB44txMGk,9109
scipy/integrate/tests/test_bvp.py,sha256=tNSp-4YyIQNyLVykDU77i0-4zzkY0sEwVVaT2uoOvz4,20223
scipy/integrate/tests/test_cubature.py,sha256=dsavI9md0pxBbFmJECaLk4SH--2ECTlTYshHWCe6sYc,37061
scipy/integrate/tests/test_integrate.py,sha256=KiyXeJ7ThQUpL8_XQKfOTZ8i_LBVwgC7ykzF6Yg574I,24611
scipy/integrate/tests/test_odeint_jac.py,sha256=enXGyQQ4m-9kMPDaWvipIt3buYZ5jNjaxITP8GoS86s,1816
scipy/integrate/tests/test_quadpack.py,sha256=8EM7IsCLJxswnWAd8S5xyvWX9dWjudycdvDDq1ci7v4,28066
scipy/integrate/tests/test_quadrature.py,sha256=FGjWORDvDwPJaN0AJospoOAQHIF_m66Yiqs9LyQFQsI,28110
scipy/integrate/tests/test_tanhsinh.py,sha256=kGntXLF3wLfBMYA_PUtgczEpKZwqQ5eJWM7MTouczms,44884
scipy/integrate/vode.py,sha256=DPRqm2oBQx6KKi5tl9dDVpXEdAO--W0WpRQEyLeQpf4,424
scipy/interpolate/__init__.py,sha256=i0mcQWXv0KbAn44vld8w-9NrRy-z_w0xf5_kQnBTtK8,4073
scipy/interpolate/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/__pycache__/_bary_rational.cpython-311.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-311.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_repro.cpython-311.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-311.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-311.pyc,,
scipy/interpolate/__pycache__/dfitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/interpnd.cpython-311.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-311.pyc,,
scipy/interpolate/_bary_rational.py,sha256=X7qtKb8vxJfXHQ0QXnuwJvWKcgppY98cGiwB5VrHDwI,27972
scipy/interpolate/_bsplines.py,sha256=Ef0Q2BqFck84UidLUNJhCYPCEI3vXa1Ln9rzUAgTl1Q,84775
scipy/interpolate/_cubic.py,sha256=EYu03F-l4oie0sgibENi-KhYJv0clr7ELcneDLJtc3k,38395
scipy/interpolate/_dfitpack.cpython-311-darwin.so,sha256=1BioH3HvyUZxSqO2vO30hOa4QVc0Z8FKIJPTBZaR8gg,312368
scipy/interpolate/_dierckx.cpython-311-darwin.so,sha256=3CUosZWYeVMUjJ4yuIZQ_tEhYpvAH2UCzwuIRSEsUqU,91120
scipy/interpolate/_fitpack.cpython-311-darwin.so,sha256=iiBE4DYNMVMkYgYYkuKgmy6nrnC6fVIVqPRG2ljatc4,121056
scipy/interpolate/_fitpack2.py,sha256=2a-qgUvVsixP8x0T8aBGZD0eEFCzNCsgxUau92ZOy14,89921
scipy/interpolate/_fitpack_impl.py,sha256=iKPcMTFmZweOwMWzQdlG83Vv1khAOvETza3lO3z2jC8,28510
scipy/interpolate/_fitpack_py.py,sha256=sCzWA-X8ulb0bn-YcaBq9Zo1fpHD0nAoKmURIMbqGek,32157
scipy/interpolate/_fitpack_repro.py,sha256=RB7_I76197ICXGjs8vKmGj-EzotRrcfNPtNhTO_mjhw,36981
scipy/interpolate/_interpnd.cpython-311-darwin.so,sha256=3siROi3qBQ4Fc8_c38c4wzYwUD6TFmK0XDrHujP_Hrk,243680
scipy/interpolate/_interpolate.py,sha256=TDf2XjoWURScQ80orCECwlxlR8Ya3WnhB_z2mGHXmaA,80439
scipy/interpolate/_ndbspline.py,sha256=HiT3tLo3zoHdnWMN33hb-NAV0mMYd6prytC9Tz1EnKY,14597
scipy/interpolate/_ndgriddata.py,sha256=aSJ5uzoA_Sqznb-NtJpOlxe6Y9NewMg9PGccSXbT42w,12068
scipy/interpolate/_pade.py,sha256=OBorKWc3vCSGlsWrajoF1_7WeNd9QtdbX0wOHLdRI2A,1827
scipy/interpolate/_polyint.py,sha256=K0o8FcReHeZi2oIUTngYZDVugfm-iVWTZ5c-FdvzuDw,38957
scipy/interpolate/_ppoly.cpython-311-darwin.so,sha256=5_zOYEIlTPiAAVj7CvJDXMnZxlsdiioREIj5mkQUmSw,271760
scipy/interpolate/_rbf.py,sha256=qGujX6VsA5xH5E0wILEvn2S4AVkqQisXZBDmqZsYhhQ,11681
scipy/interpolate/_rbfinterp.py,sha256=OekCXOlPAI4TvSSbAHcJPiJjAICp4bZQVzJFC0Tj5co,19722
scipy/interpolate/_rbfinterp_pythran.cpython-311-darwin.so,sha256=lUEet1KeZ_NFHkwe0wkIJWxa2WK6zynTGjXvJqoeHag,303624
scipy/interpolate/_rgi.py,sha256=YC8WOY-TenR5W9BNn1am32CS9DVeO3-OAwcKE5Nluyk,30774
scipy/interpolate/_rgi_cython.cpython-311-darwin.so,sha256=7IV-tci_lxG2vbDxNCZFpsVa90Ah_8fkl72OkUhBj3o,135616
scipy/interpolate/dfitpack.py,sha256=lRSKk1GcuWHJCyBXjRlg5lupLzugQHrR47tWL0vYvzc,594
scipy/interpolate/fitpack.py,sha256=aCH6A3dRouuXW47tK5lEdd2pJa39LCkewY-1zTlI8Hc,702
scipy/interpolate/fitpack2.py,sha256=P15_3gM5eZQYb_-K3c70xKdeIGM81u5WAkVhY8ei4N0,817
scipy/interpolate/interpnd.py,sha256=3AQP8UZsQD0lNLmEJzKflA5k22eGuiVUlUjedHgvFZE,704
scipy/interpolate/interpolate.py,sha256=Aiu_dJ_oxq-Y1VXns5N5u5K1Wng2hzCgRgRiDhTAiVI,754
scipy/interpolate/ndgriddata.py,sha256=VbvvoDPdWmrk8871y5olPS9StX0S_B27j_oGMAyj8QQ,636
scipy/interpolate/polyint.py,sha256=ek1EtbIbLLwehb8XDSKeNvIdjTfDQoQ9CSu4TbY8Vbo,672
scipy/interpolate/rbf.py,sha256=6oBxdpsKY8bH36nQnRNiLB9C1bNri8b2PHz9IsUIr-w,519
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_bary_rational.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-311.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bary_rational.py,sha256=GU9goarqzTWEpyFsGbvz4RH5xWFkZR_jFdnmE3AHuoI,15448
scipy/interpolate/tests/test_bsplines.py,sha256=raPe2TjQih9OBRu_hnPrpFCujwvHfU-2SMcrhHTLriI,131879
scipy/interpolate/tests/test_fitpack.py,sha256=cFJmwsWhdysO-BEpZ5pMHo6sXSGO1TYWWg_12omcvvk,16589
scipy/interpolate/tests/test_fitpack2.py,sha256=AqDBy1CbtRVUNrFjPtq1FH1HgKkCTQIBqlCfEFnPQBM,61289
scipy/interpolate/tests/test_gil.py,sha256=BPC_Ig9lRg28mVHIqdSqWnwBKLukTXFkbrdqUYuskq4,1831
scipy/interpolate/tests/test_interpnd.py,sha256=tLWoXApHQW800JfRk-hn5KyH1ViOvuZXtTGrtTvtKLQ,15525
scipy/interpolate/tests/test_interpolate.py,sha256=YrBZtU7FrQnjkpDsazDdieuuijONIGvDCGQtpfMLT70,99203
scipy/interpolate/tests/test_ndgriddata.py,sha256=b_AMpiIj3mlslZXHMnwOqDdI6ORXnO4McbpjGh51dL0,11025
scipy/interpolate/tests/test_pade.py,sha256=5gmdgTBoJGsY-d813it9JP5Uh8Wc88dz3vPQ2pRZdNk,3868
scipy/interpolate/tests/test_polyint.py,sha256=wUZqVdoSRbXm_n7rfcLQ3C_dGCkPxEG-MdpjmBPR7vQ,37296
scipy/interpolate/tests/test_rbf.py,sha256=eoFUrp861RWX4SDbe6VJfDd9_vh9a-f6xwoOrfn7JtA,7021
scipy/interpolate/tests/test_rbfinterp.py,sha256=Sk_e-H18y97dZ1dgCjMxr9bywAUseLBbou7PwlWQ16k,19094
scipy/interpolate/tests/test_rgi.py,sha256=-SbdMuFMYgbqoRA6iQJrqEq5-WBTxgJpB8EQOx46NQs,46278
scipy/io/__init__.py,sha256=XegFIpTjKz9NXsHPLcvnYXT-mzUrMqPJUD7a8dhUK_0,2735
scipy/io/__pycache__/__init__.cpython-311.pyc,,
scipy/io/__pycache__/_fortran.cpython-311.pyc,,
scipy/io/__pycache__/_idl.cpython-311.pyc,,
scipy/io/__pycache__/_mmio.cpython-311.pyc,,
scipy/io/__pycache__/_netcdf.cpython-311.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-311.pyc,,
scipy/io/__pycache__/idl.cpython-311.pyc,,
scipy/io/__pycache__/mmio.cpython-311.pyc,,
scipy/io/__pycache__/netcdf.cpython-311.pyc,,
scipy/io/__pycache__/wavfile.cpython-311.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=pCuwuPJgkxc3DmXf9N1LR8BRPW1h80Zg8628MUJJqiM,17247
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cpython-311-darwin.so,sha256=K96tSnSiiTzUEQWR2KWLgU5Ox37O0IAzVezebUM76yM,2100688
scipy/io/_fortran.py,sha256=pgbB0LbOKEfPk07y-9IQXUyT7Kx_wHP0AyGPLtC53yM,10893
scipy/io/_harwell_boeing/__init__.py,sha256=90qYbBzDEoTMG8ouVLGnTU2GMsY4BYOOtwJdoKT3Zz8,164
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=qvHmXonHRYMYTc-sV7TxRwxdrt4WRZk4rgawnwcleQ0,9003
scipy/io/_harwell_boeing/hb.py,sha256=qOZJxT-bhlpCjADyv3GGu7rBIoHs2w3y5L6-OOHq6Qw,19404
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=hPH4AmfUmyBrDU3C_Rx3j7yaGEjefQJOai4rfxMHuV0,2383
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=jYbRWktqO5bgXDh8i9O_u_KDTpYQcMx_blw7Pn66Nd0,2516
scipy/io/_idl.py,sha256=sgL0II6EcAe6Mtp_SdqmAIvvMGq9UFsWeH7M8RTGvIY,27000
scipy/io/_mmio.py,sha256=Pk9Qmf4r-g7-ZQE9cCsu9_BaqiQJDRcnYlJL840WeQo,32094
scipy/io/_netcdf.py,sha256=Bz6ywNgaX1hYWDpa2tCjAqvKi17k0DpNfc4VCuchIqA,39634
scipy/io/_test_fortran.cpython-311-darwin.so,sha256=MjSpf1HqubhBeem11QvOCqW8qXtjPdIe6aI4iHJO4Qk,91040
scipy/io/arff/__init__.py,sha256=czaV8hvY6JnmEn2qyU3_fzcy_P55aXVT09OzGnhJT9I,805
scipy/io/arff/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-311.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-311.pyc,,
scipy/io/arff/_arffread.py,sha256=uOomT89u1pVrDdGKujArTE_e6Xz3Cw2f2ACPTPS6DlY,25752
scipy/io/arff/arffread.py,sha256=KW6mASZrW2J1wmC3GYucy1EO7y-rg5MgcGDMyMTpfw4,575
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-311.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=fTS6VWSX6dwoM16mYoo30dvLoJChriDcLenHAy0ZkVM,7486
scipy/io/arff/tests/data/missing.arff,sha256=ga__Te95i1Yf-yu2kmYDBVTz0xpSTemz7jS74_OfI4I,120
scipy/io/arff/tests/data/nodata.arff,sha256=DBXdnIe28vrbf4C-ar7ZgeFIa0kGD4pDBJ4YP-z4QHQ,229
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=01mPSc-_OpcjXFy3EoIzKdHCmzWSag4oK1Ek2tUc6_U,286
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=bcMOl-E0I5uTT27E7bDTbW2mYOp9jS8Yrj0NfFjQdKU,292
scipy/io/arff/tests/data/test1.arff,sha256=nUFDXUbV3sIkur55rL4qvvBdqUTbzSRrTiIPwmtmG8I,191
scipy/io/arff/tests/data/test10.arff,sha256=va7cXiWX_AnHf-_yz25ychD8hOgf7-sEMJITGwQla30,199009
scipy/io/arff/tests/data/test11.arff,sha256=G-cbOUUxuc3859vVkRDNjcLRSnUu8-T-Y8n0dSpvweo,241
scipy/io/arff/tests/data/test2.arff,sha256=COGWCYV9peOGLqlYWhqG4ANT2UqlAtoVehbJLW6fxHw,300
scipy/io/arff/tests/data/test3.arff,sha256=jUTWGaZbzoeGBneCmKu6V6RwsRPp9_0sJaSCdBg6tyI,72
scipy/io/arff/tests/data/test4.arff,sha256=mtyuSFKUeiRR2o3mNlwvDCxWq4DsHEBHj_8IthNzp-M,238
scipy/io/arff/tests/data/test5.arff,sha256=2Q_prOBCfM_ggsGRavlOaJ_qnWPFf2akFXJFz0NtTIE,365
scipy/io/arff/tests/data/test6.arff,sha256=V8FNv-WUdurutFXKTOq8DADtNDrzfW65gyOlv-lquOU,195
scipy/io/arff/tests/data/test7.arff,sha256=rxsqdev8WeqC_nKJNwetjVYXA1-qCzWmaHlMvSaVRGk,559
scipy/io/arff/tests/data/test8.arff,sha256=c34srlkU8hkXYpdKXVozEutiPryR8bf_5qEmiGQBoG4,429
scipy/io/arff/tests/data/test9.arff,sha256=ZuXQQzprgmTXxENW7we3wBJTpByBlpakrvRgG8n7fUk,311
scipy/io/arff/tests/test_arffread.py,sha256=bWB6uAqr6Iadm3fWhhlo6M2fM0WTKrLTC1BmXYKidJ4,13094
scipy/io/harwell_boeing.py,sha256=BzISbfgVnrO3vYx-mP2xkLqh9r3oq64NNPbEY03P6v0,538
scipy/io/idl.py,sha256=A1QV5h6xBa1cTIejjsc1NfjG0MqMbxqFqXicC2OLNrM,504
scipy/io/matlab/__init__.py,sha256=PMqq8WBOEOPH_v5IVpuxrBTqXOFBJVuegOCml76pr1s,2247
scipy/io/matlab/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-311.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=AUMjfdIARtCGqyMgDDJBGa_EncP5ioYrEzyZqXOLRxU,1983
scipy/io/matlab/_mio.py,sha256=HON1CysjlVZms7kymAQO-m5q-34BJ9tI225clKkgZbM,13810
scipy/io/matlab/_mio4.py,sha256=W9FaF7ryhbT10TEgHcuovZkm7w2zIU3tDtnb5gIlYlQ,20993
scipy/io/matlab/_mio5.py,sha256=hBvVcnAUZX2hLZzQDfSnlUQVWtQlD53ur8w7VkpT9Qs,33989
scipy/io/matlab/_mio5_params.py,sha256=skRcKG70vOlVMSb1TO67LB5312zuOUSrcOK7mOCcUss,8201
scipy/io/matlab/_mio5_utils.cpython-311-darwin.so,sha256=GsMHQiTDNooQ4QFggsijS-ZNtQutoA3_HznaHM7I6lE,213504
scipy/io/matlab/_mio_utils.cpython-311-darwin.so,sha256=DJLlcjQsLGYTfOksIpDIVm50LXhV_VxgOGhb4KZYYiE,95776
scipy/io/matlab/_miobase.py,sha256=AmMxD5puIqxYYv8MCSdF2wUDqwOTSoyo9I6hh51myaQ,13102
scipy/io/matlab/_streams.cpython-311-darwin.so,sha256=oA6iaUZeufj1Xc6k9foVW82UjByu95kvdDbsLImRQRc,136592
scipy/io/matlab/byteordercodes.py,sha256=fHZVESDgIeYzGYtRlknPQ2nUqscQQ_4FhQc_ClkjBvQ,528
scipy/io/matlab/mio.py,sha256=2b0WwgQ0rBkoJ4X0hgPl889PpR7Q0i7ibSLtTQVuTto,539
scipy/io/matlab/mio4.py,sha256=hkhpBa4p0euf2rUjJviBWJ4TJs1wkUads3mX1fgDYMc,508
scipy/io/matlab/mio5.py,sha256=jEFeEEkXWOhziPreDt0SqfAtOo9JMauxoODAbbXHmoQ,638
scipy/io/matlab/mio5_params.py,sha256=2RWROlfc8RmXmcXGyM-be107Tm55ibc_U7DztJ2b4fc,593
scipy/io/matlab/mio5_utils.py,sha256=DYiQfx5BkyDVnK4nZ3xPa-5tbpZE7WRx4SIdBmPVfSI,520
scipy/io/matlab/mio_utils.py,sha256=VZPx03BNFbrQjB1CNbDCvvXUuP0_VoNRFV1R0YoB2iw,518
scipy/io/matlab/miobase.py,sha256=3qQoq8Y7ZQpHIufUCzg6RAeaLqU3qTAozmuYbaOd7BI,565
scipy/io/matlab/streams.py,sha256=0Aww9GRGGnRmiAMBAzIAXsFGySu5YCUNG-cHP1omYjI,513
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-311.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/debigged_m4.mat,sha256=8QbD-LzoYbKSfOYPRRw-oelDJscwufYp5cqLfZ1hB0c,1024
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=FCHBAxeQZlhvTXw-AO-ukwTWvpN7NzmncBEDJ1P4de4,938
scipy/io/matlab/tests/test_mio.py,sha256=d39L1re_SLGXLoN1arqS8THjZmWUjZNWUi-ft0UwvQU,47168
scipy/io/matlab/tests/test_mio5_utils.py,sha256=eacgGg0TaQXOkG7iaeYovtWyjPgYCY50mHPoPjnHMTI,5389
scipy/io/matlab/tests/test_mio_funcs.py,sha256=2BgaB9bSwy2M-4gUBrgKsD290UxjLmyuiI3gW4SbVyo,1390
scipy/io/matlab/tests/test_mio_utils.py,sha256=GX85RuLqr2HxS5_f7ZgrxbhswJy2GPQQoQbiQYg0s14,1594
scipy/io/matlab/tests/test_miobase.py,sha256=CGefrU6m_GpOwaKr_Q93Z5zKp5nuv791kjxcNNP8iiE,1460
scipy/io/matlab/tests/test_pathological.py,sha256=-Efeq2x2yAaLK28EKpai1vh4HsZTCteF_hY_vEGWndA,1055
scipy/io/matlab/tests/test_streams.py,sha256=jgwUF4PyXfKtw24A5VsfTJNCeLd9os8mKeMMf-nLS28,7715
scipy/io/mmio.py,sha256=Dc5HqR8BXOD0wir63VTVczuZcLjSxEjbSbeZd4y27po,526
scipy/io/netcdf.py,sha256=RKhmlybZwbFNKA4US6xLX6O2IUDCmdkToosPt4bAUX0,533
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-311.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-1234Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav,sha256=GSJpCuezlvHbhP3Cr4jNWmz4zG46XZ6jci2fWtiMN0k,17756
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav,sha256=iSGyqouX53NaEB33tzKXa11NRIY97GG40_pqWF_k5LQ,126
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=0cUeyIczUhtaRMFPTqHwH1U_Rm1djCaD1vDbi-6DRBo,8609
scipy/io/tests/test_idl.py,sha256=2QpZGBWoSCwH5jchc9wvot2L03p0qqeqzjqux5KP-bM,20569
scipy/io/tests/test_mmio.py,sha256=Di_-QKmcX3EoTc-j2a7PwrNFcpZSYkfHStvArw_gj0Y,29250
scipy/io/tests/test_netcdf.py,sha256=0OR5kfTlx9SonwZPT9P8gRz7p0HEZy_6Jwr7PkfXrpY,19459
scipy/io/tests/test_paths.py,sha256=3f12UO-N11JJjkw8jBgVAhz5KVrkokJbHrnvfklDhNA,3190
scipy/io/tests/test_wavfile.py,sha256=oiNewKAmFkCvX0wlH-jCBpJ8HJWt-tVMdA6byrfYm7w,18412
scipy/io/wavfile.py,sha256=Lp2pYttpWPhHvO41mRR-kUFD4NRn8JXN_oBG67WHV2E,30465
scipy/linalg/__init__.pxd,sha256=0MlO-o_Kr8gg--_ipXEHFGtB8pZdHX8VX4wLYe_UzPg,53
scipy/linalg/__init__.py,sha256=UOFZX4GCusrQjcaPB6NNNerhsVDe707BvlfE7XB8KzU,7517
scipy/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/__pycache__/_basic.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-311.pyc,,
scipy/linalg/__pycache__/_misc.cpython-311.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-311.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-311.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-311.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-311.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-311.pyc,,
scipy/linalg/__pycache__/basic.cpython-311.pyc,,
scipy/linalg/__pycache__/blas.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-311.pyc,,
scipy/linalg/__pycache__/lapack.cpython-311.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/misc.cpython-311.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-311.pyc,,
scipy/linalg/_basic.py,sha256=zxHeSh1_k3HGyQl8bbW1Q4zAgg_QPlsCczYFXxSEZtY,77637
scipy/linalg/_blas_subroutines.h,sha256=B2Y9A7ejwt3PHSLLS5mX2VhF6jwP3XgKMmt53O3cowg,18174
scipy/linalg/_cythonized_array_utils.cpython-311-darwin.so,sha256=m6q9dmSQdsliTVfCgYNGaEiFTtcGgesyiww7H2B1Io0,384832
scipy/linalg/_cythonized_array_utils.pxd,sha256=OlWTbJt3gmdrfRFyx_Vz7GTmDTjr8dids5HA4TfC6R0,890
scipy/linalg/_cythonized_array_utils.pyi,sha256=HZWXvJdpXGcydTEjkaL_kXIcxpcMqBBfFz7ZhscsRNo,340
scipy/linalg/_decomp.py,sha256=zN2sxfJn3TTa9Y5pQa4FPRsL2s0CFutJ7IuCy0iuz4w,62432
scipy/linalg/_decomp_cholesky.py,sha256=eby_d0hZymPNsouiFEFeQ1Am8e3WFQAzKsXePmqhkjs,14192
scipy/linalg/_decomp_cossin.py,sha256=JBkbZQPgvi5WiZm5h6ZB7cgsr-XV2vjib6p6sZgtah4,9625
scipy/linalg/_decomp_interpolative.cpython-311-darwin.so,sha256=V_U3LDSR11TcVmqXDSZC8ckE8-81NdCsRxI3QeRW92Y,574824
scipy/linalg/_decomp_ldl.py,sha256=d5w6AAyDwxNwvV8EZOs144ZGUdF8II77NphA_f2k8Nc,12612
scipy/linalg/_decomp_lu.py,sha256=1nqF2fw1Erf_04narGazcptmY_58lE38KO2r99l-sbY,13256
scipy/linalg/_decomp_lu_cython.cpython-311-darwin.so,sha256=6Kf0OXX0iGJ2Bo5jxptym5DrYYR1KzNsQl2e1WbCOas,130360
scipy/linalg/_decomp_lu_cython.pyi,sha256=EASCkhrbJcBHo4zMYCUl1qRJDvPrvCqxd1TfqMWEd_U,291
scipy/linalg/_decomp_polar.py,sha256=yrlXNtJv3pLWvvgnLkrWTXBOeRXNGKNFS-t71BqIyK0,3654
scipy/linalg/_decomp_qr.py,sha256=xD7J3UqwN3a9gyF9l_Za9t0ErP_1kmEA8gFiTK8Mofk,15506
scipy/linalg/_decomp_qz.py,sha256=ajCygb5S4kLsNbtcKcKDIBEj8UPr-LPW5WEt1mdA3JA,16455
scipy/linalg/_decomp_schur.py,sha256=XjrQQGCiLoLu5xSXkTyB8eYXAxJFzakbDn5WsmpF9hA,12173
scipy/linalg/_decomp_svd.py,sha256=gd6OX3KlZfXuMUOxtEUMjpIKOOByAdOXEP0Jp8OHAo8,17139
scipy/linalg/_decomp_update.cpython-311-darwin.so,sha256=noNxP2OEYBHtF6c-ikS2-5fqY0yBaydakD4YdNU_KvU,272072
scipy/linalg/_expm_frechet.py,sha256=_RvX6m1ntUhLlbeTUrbvv48a80UW4kCM4gFBBedxx58,12442
scipy/linalg/_fblas.cpython-311-darwin.so,sha256=MysErqt5RUIEqsPc8BlKp0vkWBtTQC22HDkwmBNX5sg,553648
scipy/linalg/_flapack.cpython-311-darwin.so,sha256=YTHkIS1KzHs97t0Be9Equ8ecSvXapRr2QwBYMl03Mvo,1871104
scipy/linalg/_lapack_subroutines.h,sha256=oTfgwlGeOWm1A9EL1ybTxS5cURQbHXnI3wQMuUr3fH0,239326
scipy/linalg/_linalg_pythran.cpython-311-darwin.so,sha256=FQtO4HGZuZfjxTibzS7ouSXXNgjE4g6KD7VY3iTKsO4,145944
scipy/linalg/_matfuncs.py,sha256=kYjDgbyfefqeop_C18cC2B9kObbLOpv51_bpndAJqGI,31788
scipy/linalg/_matfuncs_expm.cpython-311-darwin.so,sha256=SNiYoDMJrubIjP7D_MV6fzVvMMoS0yaI0_RkRiyIwzg,105304
scipy/linalg/_matfuncs_expm.pyi,sha256=wZAZfVtEbB78ljXgQoiL0I4yaPhmHOqIpGBYGQPvS6k,178
scipy/linalg/_matfuncs_inv_ssq.py,sha256=8dL7xD6DU8D4h2YyHcYjRhZQvv1pSOEzMuKlGP6zonw,28095
scipy/linalg/_matfuncs_schur_sqrtm.cpython-311-darwin.so,sha256=CV3aUlOhadPvI8SqgHs1FeWbBfuWdcTRRZnPMs6NBrQ,70216
scipy/linalg/_matfuncs_sqrtm.py,sha256=p7iyvXyiSXXRi2AKRK0EstG4FQi34uS4KCc0VKoYnPE,3423
scipy/linalg/_matfuncs_sqrtm_triu.cpython-311-darwin.so,sha256=1dxXAuYfJCjnPvj1HR79IADEOALyjZS9opWOzbI52lQ,133640
scipy/linalg/_misc.py,sha256=udhvxGfEHxhS3ecQBuwQ65W9ezVQIaVBw8JOmfqH_oE,6301
scipy/linalg/_procrustes.py,sha256=OC_ywJ_PbwElGsX_86jdqYzw-RyGrUp3ZB5whUOhGtc,3606
scipy/linalg/_sketches.py,sha256=SeWX12sWtw-Eifr9Q2phyrsZVzc_snefbtTj_ICQgik,6609
scipy/linalg/_solve_toeplitz.cpython-311-darwin.so,sha256=OzSxD9s_JEDyhUSJsN7RLeZXhs4S7h7AkZKjBFzq68s,150840
scipy/linalg/_solvers.py,sha256=gn-foKt2PwqwXuf6L35i2o53DkJaNd6nz_UJqM8mJzU,29098
scipy/linalg/_special_matrices.py,sha256=0cnCnBD4qP8p9Rx5dNPUGoRQlfrqpLT056-LWep56Uo,40390
scipy/linalg/_testutils.py,sha256=IWA5vvdZ8yaHeXo2IxpQLqG9q54YIomHscYs85q9pd0,1807
scipy/linalg/basic.py,sha256=AuNvDlH8mnAJScycj4mV-Iq1M0bXxidpY4Vud_lRJlM,753
scipy/linalg/blas.py,sha256=hnF8nO00t9OrP_AP4QzfAOXO9KojtDemq9Br1gCbw80,11782
scipy/linalg/cython_blas.cpython-311-darwin.so,sha256=Kk9fXqo32jGB7XczKubWwa3D2IMsQVQL0A0Z-M8bgRY,165584
scipy/linalg/cython_blas.pxd,sha256=DCPBxNWP-BvdT_REj6_a4TjUrNaf6sCq_XoxU3pEbfc,15592
scipy/linalg/cython_blas.pyx,sha256=OgjBmmw5YP51aWtU-1pFgWWH29XWcnFWwcZP3_1sELk,65288
scipy/linalg/cython_lapack.cpython-311-darwin.so,sha256=CCmEj9mRw_BGLu1mDHD4uJk5UecoFJP50KsKhHYxHUU,703888
scipy/linalg/cython_lapack.pxd,sha256=Ld5hPwcYxpOPahFNsfNomsp0_DY8BfG-W8TmZxh-iYM,204556
scipy/linalg/cython_lapack.pyx,sha256=fDKWgTGV8swZzyxFvtHkeaQaVWzrze0t5lHYGu0GKgk,707005
scipy/linalg/decomp.py,sha256=w9HTI1OxXpX_rL72qcmykc5dUWal7lTlAU8k-9Eq7Dg,708
scipy/linalg/decomp_cholesky.py,sha256=1g45oc115ZZR3CfMW1bCPseF5ATz4Xf6Ih26NRqyjfs,649
scipy/linalg/decomp_lu.py,sha256=FPo9NHe9wg1FhCaoVV1_4mdfNj0S4plT4dHr4vMl1U8,593
scipy/linalg/decomp_qr.py,sha256=EJNpu6lSa36Eo-e4rbYu5kDlRTMse2mmGul_PLRFXHs,567
scipy/linalg/decomp_schur.py,sha256=vkVK3y-055523Q__ptxVNatDebPBE1HD-DFBe7kEh3w,602
scipy/linalg/decomp_svd.py,sha256=HrJqbmgde7d7EWxCsa9XkS9QuWgPYMFOHiF4NcAL_Qg,631
scipy/linalg/interpolative.py,sha256=8kCZv1z3UtzBuPvompAUUjHToLta4ffvOjVVLSaRLeQ,32757
scipy/linalg/lapack.py,sha256=xZW5TCKcgVZccUFc5vXEit8PirMlBMFUHm_MgcQZdpc,15937
scipy/linalg/matfuncs.py,sha256=vYw39D2LukCRCFJpx0qx8tgHlRZEDZI2wZfZwhh-Ubo,744
scipy/linalg/misc.py,sha256=uxpR80jJ5w5mslplWlL6tIathas8mEXvRIwDXYMcTOk,592
scipy/linalg/special_matrices.py,sha256=OXkkDj-ypZHiC17RUerraAzO8dC9aDuVujzb3Ft3GDY,757
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_batch.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-311.pyc,,
scipy/linalg/tests/_cython_examples/extending.pyx,sha256=scunPSonBTtsidhd2hLtg-DPWoFkvzWcXDMYEO9iygo,887
scipy/linalg/tests/_cython_examples/meson.build,sha256=DzG1UVjBrYOrtvgnaOVL0amQulrI8HTyYgryw2AhiKI,846
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=GsiliR8bd55rDVVznN6t4UEqDtJ3msO4nBSPrApmPqs,79690
scipy/linalg/tests/test_batch.py,sha256=VuT6YUUufHsjLOcSHLaLp1ONQG20anCLGUBp1dF7Gp4,27666
scipy/linalg/tests/test_blas.py,sha256=8w_6r4CBrif9MH69v15Iil5rEcyRDlUhgbbZnC8_Bck,41729
scipy/linalg/tests/test_cython_blas.py,sha256=0Y2w1Btw6iatfodZE7z0lisJJLVCr70DAW-62he_sz4,4087
scipy/linalg/tests/test_cython_lapack.py,sha256=McSFDUU4kgCavU1u3-uqBGlzUZiLGxM5qPfBFgPTqdE,796
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=IFvsqTaiq09K6p50gc1S2zndWyq8WcVn0770_Cd_j1g,4092
scipy/linalg/tests/test_decomp.py,sha256=HOBmd78nts_1AQZc7wSyAiK-9n9sV_ozHNEPvgonh5E,120180
scipy/linalg/tests/test_decomp_cholesky.py,sha256=5WxQbSxK6134NztaoNu-d4OmudQRfhgeyf2LmyJdx1w,9743
scipy/linalg/tests/test_decomp_cossin.py,sha256=b10EQSJzYwGwgEHj_s71tDPr59e3lZmid7M8G9qgv3A,12560
scipy/linalg/tests/test_decomp_ldl.py,sha256=kJkYphaal2EUlRNd8XiaTTuTJF9B4TUH5oD793Fe8L8,4971
scipy/linalg/tests/test_decomp_lu.py,sha256=spCYelU_CXmHAaKrJM4V5djLKq5MCeX4wN1SBCFkSOo,12629
scipy/linalg/tests/test_decomp_polar.py,sha256=fGKl3Skqz6IpHBeFcq6bdqvS8M53rXx2Wh6Kx4f5T3Y,3287
scipy/linalg/tests/test_decomp_update.py,sha256=MCSzhUD-bcCs1Ll5pHJqCdRTgEpimCglZ3lb8bzwZqs,68502
scipy/linalg/tests/test_extending.py,sha256=eirY2TQ2IwWje-5hW_kqvS0SnA2xEzLeG5sE0P3zuvI,1751
scipy/linalg/tests/test_fblas.py,sha256=Ykb7LKjbxPXAdJD-IkXMAsbUmXMAkku2FQCr-jlDTUE,18687
scipy/linalg/tests/test_interpolative.py,sha256=EVmkopJjhzDOs6h6NoSkQ-d7qRZDsys58mt4sp8yOoE,8577
scipy/linalg/tests/test_lapack.py,sha256=Gh3FQskUWIgyRJc1XQUjaDcAj9I__8yOpE0vBO5wI6w,138620
scipy/linalg/tests/test_matfuncs.py,sha256=RCBaohm9PPoY7ybyH_TecFGy7IpeUlLqtHAFfulvdHM,43881
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=73Qe51lCXEWZGpxk8GYv0owDSlN0IpnLJPlI0nsCdhY,4088
scipy/linalg/tests/test_procrustes.py,sha256=ZbCK1ULDF8DFXac1sxA6SmJgZSOv5fhuzStQp5wT0uc,7458
scipy/linalg/tests/test_sketches.py,sha256=FLqc8wn9esU8LbSsWS7_OC0sZ-BcGPROqPurBM8BZXc,3954
scipy/linalg/tests/test_solve_toeplitz.py,sha256=Msi6-fH0p1l85s5EHv8U9m7XhiywTha_g1On_WrbQcc,5111
scipy/linalg/tests/test_solvers.py,sha256=jIJ1YjC5epuQACS2h7GZZUuIbt89KPM8tnUlXTsPyjU,33951
scipy/linalg/tests/test_special_matrices.py,sha256=5KkQSu3aFmnIa3m3eupNp2-f-L_KRYgWDFMSoD5fBM4,24940
scipy/misc/__init__.py,sha256=dVfULY959nFwpl5NCxyCpiHyNcSNaR7HYOg7QU21a5s,135
scipy/misc/__pycache__/__init__.cpython-311.pyc,,
scipy/misc/__pycache__/common.cpython-311.pyc,,
scipy/misc/__pycache__/doccer.cpython-311.pyc,,
scipy/misc/common.py,sha256=nAGQOVR9ZEAb703uhOVQZqf-z0iCM4EDhbHK4_h_Tdc,142
scipy/misc/doccer.py,sha256=wHbpGV8todadz6MIzJHalDfRjiKI164qs6iMcHgsVu0,142
scipy/ndimage/__init__.py,sha256=KUbDfnLPN7B-U650Nh-XVf7fQ0bW8vkEDPssG8oZsi4,5175
scipy/ndimage/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/__pycache__/_delegators.cpython-311.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ndimage_api.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-311.pyc,,
scipy/ndimage/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/ndimage/__pycache__/filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-311.pyc,,
scipy/ndimage/_ctest.cpython-311-darwin.so,sha256=vHyC6D1I9y-Kbj1KjFCE5GgqW0jxFwPwvJn0ZZkqqQ8,51120
scipy/ndimage/_cytest.cpython-311-darwin.so,sha256=SsmtcjtJxPiysh0c826gB0ai8AYbwQ-rO-NdcGEDWMI,97536
scipy/ndimage/_delegators.py,sha256=EI2Xsmw6GDL8MnLeQYZ6uK9dVkMv01WOd1fZTLS_BrU,9410
scipy/ndimage/_filters.py,sha256=OuVuvfxY7ibLGzR3qiEcE7V_6Sqz69AE3amxOPExu4s,92349
scipy/ndimage/_fourier.py,sha256=SoAYRx7ax7Tv51MyYzDlZ3fN682x4T6N8yReX2La4-I,11266
scipy/ndimage/_interpolation.py,sha256=KKQMixU4VgfEprLNPUeLWNvzfBBJ_nhr9bnJYT3o7Nc,37740
scipy/ndimage/_measurements.py,sha256=MCdbyKlILgfmne0qFFOAFCKv-oWqqnEmb83iG8Tlwuk,56248
scipy/ndimage/_morphology.py,sha256=HlgR6X8edYsjOSNyCvmg1RqThVJeKhtxuqUALSCUVCU,100964
scipy/ndimage/_nd_image.cpython-311-darwin.so,sha256=pfOC4y6h8b0AMUZCsdAibmCj5MUNT7OPLrngX6e3aZI,156160
scipy/ndimage/_ndimage_api.py,sha256=S8DBRWydSRfAz-ZlHSMeCSbjYGgCLioa9_Q2VXGeC_g,586
scipy/ndimage/_ni_docstrings.py,sha256=EhrW-Q_R2fO9pDXYhc8xKS1QY5nXFxQtEXd2aLWG5GM,8727
scipy/ndimage/_ni_label.cpython-311-darwin.so,sha256=GecAhSMvKbgwlNgyEGpkFZDXII6Jbr3yvpDWFevT7m8,240736
scipy/ndimage/_ni_support.py,sha256=1JpV6XSyvMP6-rqKHHEBrc83RaVkDzynBHlO672sczY,5216
scipy/ndimage/_rank_filter_1d.cpython-311-darwin.so,sha256=482qyl-LF4q-pz3AJDXf5uXaBg94fK-8h5h3eUWypMI,51896
scipy/ndimage/_support_alternative_backends.py,sha256=cv4Q_RH5yQZ37QetD3ig3PW5uzLizimLgq287FdX_Tw,2977
scipy/ndimage/filters.py,sha256=cAv2zezrTJEm9JzKPV_pmXzZcgczCK_VaYJ4mdNW3FM,976
scipy/ndimage/fourier.py,sha256=gnifi4S_Epyu4DpNsebz4A5BKzBWoGf11FkXWeXsoqY,599
scipy/ndimage/interpolation.py,sha256=GHYvxCyQsLfKtNUc8AUN_vqmBhmAPwNnxm2-VpFMayk,664
scipy/ndimage/measurements.py,sha256=xdSs52Y5RjURLP710iGURXWQFeS3ok4WjoYufKh9OeA,788
scipy/ndimage/morphology.py,sha256=yFWSo7o_7PuYq61WGQOCIgMppneNLxqhJocyN0bMsVA,965
scipy/ndimage/tests/__init__.py,sha256=GbIXCsLtZxgmuisjxfFsd3pj6-RQhmauc6AVy6sybDc,314
scipy/ndimage/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_ni_support.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-311.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=JPbEnncwUyhlAAv6grN8ysQW9w9M7ZSIn_NPopqU7z4,294
scipy/ndimage/tests/data/label_results.txt,sha256=Cf2_l7FCWNjIkyi-XU1MaGzmLnf2J7NK2SZ_10O-8d0,4309
scipy/ndimage/tests/data/label_strels.txt,sha256=AU2FUAg0WghfvnPDW6lhMB1kpNdfv3coCR8blcRNBJ8,252
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=7Gv-hR91MWpiGQ32yjXIBjFytuaYLqz3wYiCXcC8ZSk,3738
scipy/ndimage/tests/test_datatypes.py,sha256=TYMiGyBcdOq3KVLzvjZPjerD1EXonyHFQYBLTWDwN7o,2819
scipy/ndimage/tests/test_filters.py,sha256=PS2M6NCrtB2u1gngDPg5eQze7udgsY4BssDiCuUGM_E,133189
scipy/ndimage/tests/test_fourier.py,sha256=BDKXgdV5wCnd7MIkvQ_Fnk8fQ7163mJBtFv5Zod16f4,7618
scipy/ndimage/tests/test_interpolation.py,sha256=mEq534rYzoVdfZ4fbiErDTWkzc_ntQDk7QVp7S_Ds0M,61116
scipy/ndimage/tests/test_measurements.py,sha256=LYERZh0uIM3HRkYoK1Njq_h-nAByW2NrFxjayH0UCP0,58418
scipy/ndimage/tests/test_morphology.py,sha256=wzZE4AzqfGTyY-gTLLSgqacMRRGYQDhoGMRispbiCY0,131280
scipy/ndimage/tests/test_ni_support.py,sha256=fcMPR9wmtOePd9eKg1ksGgolmKqVO2xboHsYOd4mC1I,2511
scipy/ndimage/tests/test_splines.py,sha256=vBVm4fKQI828BT5LYMK4tNm_k2jnfhsvaIvo5VzY5WQ,2427
scipy/odr/__init__.py,sha256=CErxMJ0yBfu_cvCoKJMu9WjqUaohLIqqf228Gm9XWJI,4325
scipy/odr/__odrpack.cpython-311-darwin.so,sha256=hszTlhcGGzmpjlqu2YdUZgKyQn4yaY2dt0CMKwH0ksI,240624
scipy/odr/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/odr/__pycache__/_models.cpython-311.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-311.pyc,,
scipy/odr/__pycache__/models.cpython-311.pyc,,
scipy/odr/__pycache__/odrpack.cpython-311.pyc,,
scipy/odr/_add_newdocs.py,sha256=GeWL4oIb2ydph_K3qCjiIbPCM3QvpwP5EZwEJVOzJrQ,1128
scipy/odr/_models.py,sha256=tfOLgqnV4LR3VKi7NAg1g1Jp_Zw8lG_PA5BHwU_pTH0,7800
scipy/odr/_odrpack.py,sha256=n30DVx78Oh0zDItjKdqDaJpiXSyVPqHYGk63a1-5NZg,42496
scipy/odr/models.py,sha256=Fcdj-P9rJ_B-Ct8bh3RrusnapeHLysVaDsM26Q8fHFo,590
scipy/odr/odrpack.py,sha256=OlRlBxKlzp5VDi2fnnA-Jdl6G0chDt95JNCvJYg2czs,632
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-311.pyc,,
scipy/odr/tests/test_odr.py,sha256=MkCfBdQvbCtiLgDFaIAp0jclwj2mIhwgL3J0Asvq31Q,22079
scipy/optimize/__init__.pxd,sha256=kFYBK9tveJXql1KXuOkKGvj4Fu67GmuyRP5kMVkMbyk,39
scipy/optimize/__init__.py,sha256=7ZzePqFF1X1377f_s3dpVdeg51I3YwManuh8Pl4M1mE,13279
scipy/optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-311.pyc,,
scipy/optimize/__pycache__/_bracket.cpython-311.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyqa_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-311.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-311.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-311.pyc,,
scipy/optimize/__pycache__/_elementwise.cpython-311.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-311.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-311.pyc,,
scipy/optimize/__pycache__/_milp.cpython-311.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-311.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-311.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_qap.cpython-311.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-311.pyc,,
scipy/optimize/__pycache__/_root.cpython-311.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-311.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-311.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-311.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-311.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-311.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-311.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-311.pyc,,
scipy/optimize/__pycache__/elementwise.cpython-311.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-311.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-311.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-311.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-311.pyc,,
scipy/optimize/__pycache__/tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/zeros.cpython-311.pyc,,
scipy/optimize/_basinhopping.py,sha256=IsHcv3i1MuYyBdgKzPm8oDjmqjIs8enX_15gXDvpXow,29922
scipy/optimize/_bglu_dense.cpython-311-darwin.so,sha256=-5H1XkHPwEPKc1V1szkeJT5KJHf-wVoP1daaTfD1XjY,191952
scipy/optimize/_bracket.py,sha256=RWZNYU3t8qsNVhW4TcP7etHTyoocZiPGCxzC1uKkPbA,30939
scipy/optimize/_chandrupatla.py,sha256=yfdxwFZNBb1SToOqkbheYr0rabz0T9LGsBzKV1kI4Rk,24548
scipy/optimize/_cobyla_py.py,sha256=szkLcnUBs0jIoiw9qOLATN4zWv0iHnfjGr8ua9VFTHo,10969
scipy/optimize/_cobyqa_py.py,sha256=_zejgs3XKkieGiMlRVn1x12cyWoulaPP2SpvxA4zK3k,2971
scipy/optimize/_constraints.py,sha256=wike0Rd2M6nm1V-hK3_st23lXmVD3OOYgt98XKZYxog,22897
scipy/optimize/_dcsrch.py,sha256=D5I9G4oH5kFD2Rrb61gppXFMwwz6JiQBYPvW3vbR5Gs,25235
scipy/optimize/_differentiable_functions.py,sha256=rw0YYafjP-w1kTOS63AI6wn5RmJXqx8AGaG0AAh4eBE,29644
scipy/optimize/_differentialevolution.py,sha256=gyOu7MY1cAyrk-SuRRm2lfgIyciAcrtSPb_6R4AwHwo,86513
scipy/optimize/_direct.cpython-311-darwin.so,sha256=6hXpMoatY390nFmrJIVT72f-SY56-hLiIa_Fz9bWJZc,69296
scipy/optimize/_direct_py.py,sha256=-tEx51_9jg63zmDcSmmqeMtTlxXpci8fSh9TR_dFD4M,11849
scipy/optimize/_dual_annealing.py,sha256=YU79aERoQuVZWEOfDyzdmj7O8V8tg3VMLX341wByNCI,31121
scipy/optimize/_elementwise.py,sha256=ejydwc2JUjpkCkbHs0h6BWICQPPDT8iJd3lGdY9YJyQ,33050
scipy/optimize/_group_columns.cpython-311-darwin.so,sha256=DOBtfwFe4ud2xf9He5RjD6P2IXdaz_KDna7v74myvFU,95224
scipy/optimize/_hessian_update_strategy.py,sha256=xmtREKGlLgVvlBynjb5eCnPbsH-xbPcprS-ZoziG80M,18423
scipy/optimize/_highspy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highspy/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_highspy/__pycache__/_highs_wrapper.cpython-311.pyc,,
scipy/optimize/_highspy/_core.cpython-311-darwin.so,sha256=bEw4t3v6SFpBq6ncDTh9lQfgPKHBZZgBJ9pTnnKBiJ0,4861688
scipy/optimize/_highspy/_highs_options.cpython-311-darwin.so,sha256=1XyPe8XXfH4wJK4CLVTuw4x4ICD9hEbPZhkAfPk5NiQ,363272
scipy/optimize/_highspy/_highs_wrapper.py,sha256=wVqUOgmFv3FthLk3GdCy9XLmmDc2VasCWGFLSyq2cwM,11294
scipy/optimize/_isotonic.py,sha256=WY-9jtT5VVafVALYIp6lJPQnBfYVNDP9oJpg-kErYYI,6077
scipy/optimize/_lbfgsb.cpython-311-darwin.so,sha256=-bnQR06BfX7YAMfOw9IMvFnlbVfqoh3OzCteUlHPogU,68592
scipy/optimize/_lbfgsb_py.py,sha256=skrkTC1jzYijSzqVsTgKIZ0nT4e7mbhPq6K0T2ttbdk,23093
scipy/optimize/_linesearch.py,sha256=sZ45z0K3l6LLURdAfzO5CI5DctDlXqD92PCaz9mKzYE,27215
scipy/optimize/_linprog.py,sha256=TGl9k9Ioh-hgHYgtndN5BNcU4vqfpZm8whRK2f4ehQQ,30262
scipy/optimize/_linprog_doc.py,sha256=AqRggJEqncrthBW0iCG2zhg-3Ks4-ZR_B3wTQSOfslE,61931
scipy/optimize/_linprog_highs.py,sha256=491Jt7-YCQGKM0YQnWgdMpYYVKZ8tBAPDFCdX9-1vGM,17142
scipy/optimize/_linprog_ip.py,sha256=QBlUjw6jl3mEZnouExtuac2dlIn9Gtki5yJVzkSffSU,46651
scipy/optimize/_linprog_rs.py,sha256=wRVGZxCSpo4ttw4CPpmXozSvM9WRXD179fGiGh8gOQ4,23146
scipy/optimize/_linprog_simplex.py,sha256=9_nxcVl-ofHN9p_dDyC1C6jHlPttSfO9kp8WF1ST4JM,24748
scipy/optimize/_linprog_util.py,sha256=try6j91fBidx3pTP3riueOMQWqXja9SO80SqQIgYhS0,62762
scipy/optimize/_lsap.cpython-311-darwin.so,sha256=hXHtldMDYOeRdlS70hjvrfusUubvtXSQvg62fezzGwQ,70696
scipy/optimize/_lsq/__init__.py,sha256=Yk4FSVEqe1h-qPqVX7XSkQNBYDtZO2veTmMAebCxhIQ,172
scipy/optimize/_lsq/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-311.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=7u5B8LfUbv3ZRZ8DAZKuDTSNRfDEBmTsn25VZtMMsKk,5195
scipy/optimize/_lsq/common.py,sha256=h_VsfPQM3fy-MWYY2WbPDvfzZssBOsZl3xmaEWxfsMc,20480
scipy/optimize/_lsq/dogbox.py,sha256=A-Q6_1XV3TbN7wUGS8vT2JFeZfSz-GuS8h9HKKRJUMU,12177
scipy/optimize/_lsq/givens_elimination.cpython-311-darwin.so,sha256=1h0eSfllA6SUQkyZ6fax_7Tq05TtEHxvprhP7Y4xaHw,94600
scipy/optimize/_lsq/least_squares.py,sha256=7bJeypmMILnMELOyL6-bCGH5vlJRIJSBtzn1bYV7G3w,42812
scipy/optimize/_lsq/lsq_linear.py,sha256=rsTDitLCK475gzp7X0vQ07KjHnZ899KV12kC3kRNoMI,15052
scipy/optimize/_lsq/trf.py,sha256=DHwp1dP9aRVyDpVH8FHUfhTKWoIeeHJoaqjPHRxOSJM,20516
scipy/optimize/_lsq/trf_linear.py,sha256=jIs7WviOu_8Kpb7sTln8W7YLgkcndv0eGIP15g_mC4g,7642
scipy/optimize/_milp.py,sha256=-K4uoM_i9pj9GfzVvS5w4X_EoazDEXBU9SyrWE57BoM,15229
scipy/optimize/_minimize.py,sha256=1He2Jy2mLnbOC-PFg3bkHcUC0bNTPchfHJMdCxOtDxs,53003
scipy/optimize/_minpack.cpython-311-darwin.so,sha256=vqeeqk7q33etQca1sdOaJG9t6iALbrRmUtc_x3R3eWw,103392
scipy/optimize/_minpack_py.py,sha256=zDy2mNhZxfcSdrvt8qPEmkiNJuzx07vi23gSyTdL3EY,45387
scipy/optimize/_moduleTNC.cpython-311-darwin.so,sha256=u68KFrheqPj-b7i4cCBJFyJLrc1yMetTYYlcdjbUmjo,148528
scipy/optimize/_nnls.py,sha256=GhcsSqShKONxTxP43I5unwvy8E5saDN2uMRn6DjiKno,2913
scipy/optimize/_nonlin.py,sha256=SwTWcznrvlfmJ-74i79xC_ixrnuDJGI0pslvfTBDrI4,51698
scipy/optimize/_numdiff.py,sha256=q5LoLQ-T8sejXroKKAC3DPgBXZfM8JSr9VYgYhG-sAs,35825
scipy/optimize/_optimize.py,sha256=JJ2MBM14DxS8YBUMNjqK_7l2-j1CWyqLh8bmOSXEK3A,149770
scipy/optimize/_pava_pybind.cpython-311-darwin.so,sha256=A1wl82kDH4EGIXZWNyXDYQVjGavE8tbNfTGK88EOHI0,200672
scipy/optimize/_qap.py,sha256=6bIzIiLwD4V2MCJrqQBOJ2h7uycy0qx01mkl-CR1U3I,29390
scipy/optimize/_remove_redundancy.py,sha256=00_Zc8_5uY6q2pBr8gu2_AYk7kX9YBjF8LqaxgqTKUY,18757
scipy/optimize/_root.py,sha256=Zh-WttrslloClCDg7VKhrVbRkDHBRkS4-ijJkI-_twg,28714
scipy/optimize/_root_scalar.py,sha256=XSwjKAXVZRJrGwSc7hIpQTucDwJv84cWZpcYlLDLF3U,20391
scipy/optimize/_shgo.py,sha256=44fC7R7kSYyEVOb50r9c75r0dAVLPE1-FekuIm79GJQ,62622
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=my2yyCrPKpLbmz0onA-Y-xLTw20KuwEiKbOrQ-fpe6E,50263
scipy/optimize/_shgo_lib/_vertex.py,sha256=I2TAqEEdTK66Km6UIkrDm2-tKpeJUuFX7DAfTk3XvUg,13996
scipy/optimize/_slsqp_py.py,sha256=5MfG7G-Be9z3ivtTcPWKmI0Ou_3XWPi8LxV0KZSd01U,23902
scipy/optimize/_slsqplib.cpython-311-darwin.so,sha256=SGgUtsQntgx4GQ5d3f5QVzaxj7_frS00dcchE5Qn1aU,70624
scipy/optimize/_spectral.py,sha256=uV4DgfWAKcEpB7CC1CUgtWJjIGfIbpk8SH1ZwUyGFXo,8128
scipy/optimize/_tnc.py,sha256=htQhspgXo-P0jt7TmynmAPzqq8qZaM4yVmI8O1cImGU,17339
scipy/optimize/_trlib/__init__.py,sha256=cNGWE1VffijqhPtSaqwagtBJvjJK-XrJ6K80RURLd48,524
scipy/optimize/_trlib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trlib/_trlib.cpython-311-darwin.so,sha256=9nBWfAVGJBe4FOR0ANrgXyxlZwEwoBzBv_yA3588-Cg,185264
scipy/optimize/_trustregion.py,sha256=Mcp96gvsrtBqXRJju2KHfCT-xGxaNa-88erFu2Fdo30,11458
scipy/optimize/_trustregion_constr/__init__.py,sha256=c8J2wYGQZr9WpLIT4zE4MUgEj4YNbHEWYYYsFmxAeXI,180
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=gc5KucHwFCz_-w2IYWd_C1X1E0TFy_sey-_0KBY_djE,12542
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=mNbnrNvKAkj7AzHTUpQQGgAfiUQ5ZSY0uzjk45B91Cs,9160
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=qhA0fmmkyaz1ivlJdU8hKhbQj6ldX4CopjtJktODgoE,26520
scipy/optimize/_trustregion_constr/projections.py,sha256=Q7zBucAd44hxj_KwusacAAnAuLSavr3T3qLUk4bwN_k,13517
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=AoLwMIDUMROleMGAldXH3gItoyzJ6qRTpNhDrijyvNA,22587
scipy/optimize/_trustregion_constr/report.py,sha256=_L-HrO5C1lzvKvaijgkOYD210dvM4PkrhBSEQrMhVlw,1782
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_nested_minimize.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=zVPxZDa0WkG_tw9Fm_eo_JzsQ8rQrUJyQicq4J12Nd4,9869
scipy/optimize/_trustregion_constr/tests/test_nested_minimize.py,sha256=tgBVQe97RwVu_GJACARyg0s9zHiFGVHSPNrXLCjlX7w,1216
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=LXshMEt2_l1yCkhTHUDxoO11P9qT1_gWv5KleSATA_A,8827
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=5nNaAOTP4Pox6ZIqQ9b9gpOBHALaPY3m4IS--7PX4a0,27642
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=hyRnUGBhDhKHR5SKD66ZME4zzCIViIh3_-700p0afXY,1104
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=Kt3jEh7tPo9cp3PaQd675ofg7JVk-84sYzaZS7UA6Uo,14395
scipy/optimize/_trustregion_dogleg.py,sha256=HS783IZYHE-EEuF82c4rkFp9u3MNKUdCeynZ6ap8y8s,4389
scipy/optimize/_trustregion_exact.py,sha256=TnUAmdymkfjCvzybOn-RCtGOoHpSO6Mg1rfeu516FGw,15557
scipy/optimize/_trustregion_krylov.py,sha256=KGdudJsoXXROXAc82aZ8ACojD3rimvyx5PYitbo4UzQ,3030
scipy/optimize/_trustregion_ncg.py,sha256=y7b7QjFBfnB1wDtbwnvKD9DYpz7y7NqVrJ9RhNPcipw,4580
scipy/optimize/_tstutils.py,sha256=BBaThpZNuwIQBqtVMOEB4bUHk3QdG2NpuLJBum8P6ak,34047
scipy/optimize/_zeros.cpython-311-darwin.so,sha256=E5Yqp1ZKFrTWYg4O0heeYuHGitRktUpYXKyg5E7irmQ,51312
scipy/optimize/_zeros_py.py,sha256=6NN_vJD-QncMghnvDIpho9Rxf2QITQuU2EGIi_87J9w,56659
scipy/optimize/cobyla.py,sha256=k2io8SM0vahYT5Zu4nS4yfa05_gyH0y-jVVxdWkC4dU,557
scipy/optimize/cython_optimize.pxd,sha256=ecYJEpT0CXN-2vtaZfGCChD-oiIaJyRDIsTHE8eUG5M,442
scipy/optimize/cython_optimize/__init__.py,sha256=eehEQNmLGy3e_XjNh6t5vQIC9l_OREeE4tYRRaFZdNs,4887
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/cython_optimize/_zeros.cpython-311-darwin.so,sha256=4IBKdnCeN4K09p0SyrjRnclMuTLr4tmHvThtHoh3BJg,115424
scipy/optimize/cython_optimize/_zeros.pxd,sha256=anyu-MgWhq24f1bywI4TlohvJjOnpNpkCtSzpKBJSSo,1239
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=6Gc0l1q-1nlCO9uKrYeXFiHsbimRZzU3t6EoTa8MVvA,1118
scipy/optimize/elementwise.py,sha256=8eEQW_PeNkr49YBTROr5xWDLgeJd7rxtdQk3tVuEECQ,1190
scipy/optimize/lbfgsb.py,sha256=XT7kclUTtom8JASPYyAScx-5irlBd9s9yEnZzRwFqu8,601
scipy/optimize/linesearch.py,sha256=w5OhOofynUbz7IzHAGEc6huLKV_rMR5eUq77VcskA9o,535
scipy/optimize/minpack.py,sha256=2S9tkmBI670qqeDN7k_1-ZLYsFZV1yXaDMkrCvMETiQ,664
scipy/optimize/minpack2.py,sha256=IPIduBcu0LRo75GJ9SiMa_GjfdKCOYzsWUs61_d1HR8,514
scipy/optimize/moduleTNC.py,sha256=qTEQ4IWtv_LT6fH3-iYmYNwrtrjG1gS4KFbZ73iDcd0,507
scipy/optimize/nonlin.py,sha256=uoKIYAdmhwNrC6zFbUIBCNdM1a59nn7hb5jxSOuK3rs,710
scipy/optimize/optimize.py,sha256=SivH06ZYrbIwJLTQj3ZShU4FXft7w2y1a2uYE9ILIMo,877
scipy/optimize/slsqp.py,sha256=Xei2XAZBNcz8cQdLdK8tTHeyIgpLEElP2ENdTH2QEi8,569
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_bracket.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyqa.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-311.pyc,,
scipy/optimize/tests/_cython_examples/extending.pyx,sha256=5TCYF9hvIYu8S9Y7PIql-xdJfcn_LI50yDrf4uh7i2M,1314
scipy/optimize/tests/_cython_examples/meson.build,sha256=GCeweHtWXjvk73tZN3HqsMTw7F1St0JuIhGyxmEiPv0,703
scipy/optimize/tests/test__basinhopping.py,sha256=nOlSqfngq5v3KxjVDmtgsV3NnrDIIwfjH3p6VO6I2Cg,19210
scipy/optimize/tests/test__differential_evolution.py,sha256=sRNjlVPwJFM7vmiRYX5oYXwQgec7tmtHtc9TkcuMTRM,69518
scipy/optimize/tests/test__dual_annealing.py,sha256=8qzPbCQwqmNRJ2GYk1X02qNvmF3TAgJxzUG_x0c07o4,16640
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=DsZjTGfPJow5w1TrAMSVoq2IJFM_dMcPFBToBdef01A,11680
scipy/optimize/tests/test__numdiff.py,sha256=fF_03UqDQ7b8uCKTAhhwQ7jyacbx8fTA2Ce9gieNwEk,34511
scipy/optimize/tests/test__remove_redundancy.py,sha256=lEivoPtGzK-My4EMhQ8DZSKiuJH-R4XbZ4NW6tUX4jw,6797
scipy/optimize/tests/test__root.py,sha256=yBSibeODBJwOqjTJHWXP9qWqh_D9XBnMjn5hFuTVQpo,4230
scipy/optimize/tests/test__shgo.py,sha256=ZUnpdjXzSFl0wvIsrWQDZzz1NEPMagfX_aTVqT__8xY,40141
scipy/optimize/tests/test__spectral.py,sha256=xh-4SMIAWkx_ND2nt7rGACy3ckfw_votfyfxMpQ8m2I,6664
scipy/optimize/tests/test_bracket.py,sha256=hZMo6d5G-SJSr45OaBGyHt6UffLeUJt15x9HxaZY8RQ,36797
scipy/optimize/tests/test_chandrupatla.py,sha256=BT_TqwU8akZBwPqHKl6z6dDxVMoKmdHVr6-KehHpfzQ,39142
scipy/optimize/tests/test_cobyla.py,sha256=gLCVj8xmmSoIx5oikBMr9PVpgyGsoyAG5JdcNAZJmmU,6822
scipy/optimize/tests/test_cobyqa.py,sha256=5sHRoBc4ZVfjZZAYMGObwSAtWq2A53L9KSwHuUUhQLk,8143
scipy/optimize/tests/test_constraint_conversion.py,sha256=7uRZeOxVD6KFbyVi6h-PSts3BxBPFiFZPVczhiVd5b4,12563
scipy/optimize/tests/test_constraints.py,sha256=X3Y31naHyXBgyI03UowHOPY_qxhRrL2I9fJtCvZ2kGo,9407
scipy/optimize/tests/test_cython_optimize.py,sha256=n-HccBWoUmmBWq_OsNrAVnt4QrdssIYm4PWG29Ocias,2638
scipy/optimize/tests/test_differentiable_functions.py,sha256=5NgNUwcioIRbPvpLQ_tfcTbnOrghdX56lJwI89fN8U4,38499
scipy/optimize/tests/test_direct.py,sha256=_R4_VkYkIJcS7X9a7n9rxwnZClK5i9nXSiYYkX0aRiA,13267
scipy/optimize/tests/test_extending.py,sha256=r9Phn1PUn0U3U6QJeMiPreKG6jKmnWFqwpf1Al7w7K0,1104
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=EiL5ImqkGFmUTjgZjv0FGpGBjTzWXqT3w6eCrzQtPmo,14337
scipy/optimize/tests/test_isotonic_regression.py,sha256=aJakW5zYcILN3wa--CYFBoZ3MB6n5Rzwd4WfNs_SFQk,7113
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=XnInFBGl9BQZS-wndHuPhNFDkb4kIsd02QMdVsg3Iy4,1934
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=6Aqn26aKUJp75unFqCAzesLq_tWPsQpp2rCftauSOS8,3582
scipy/optimize/tests/test_least_squares.py,sha256=4OvuFxmToFbOQ2vWEVi7uaCN6i6_Lt9e4xzuXl4imKU,37671
scipy/optimize/tests/test_linear_assignment.py,sha256=-IGbiBidLNWAgMo3LBsa1ak8v_IH-MT6nn44MhTPfUs,4109
scipy/optimize/tests/test_linesearch.py,sha256=xmK2zvgIbLMOWkb2B1ALBWiPHQyGGxzDG0MXaHjNlqA,11400
scipy/optimize/tests/test_linprog.py,sha256=VWOkH9vfeXFBSAzDYaaGQ-0BMpY6x15uHb-Bc-So0UQ,102695
scipy/optimize/tests/test_lsq_common.py,sha256=alCLPPQB4mrxLIAo_rn7eg9xrCEH7DerNBozSimOQRA,9500
scipy/optimize/tests/test_lsq_linear.py,sha256=uVFSH6MFBg6JfqfA2Mrl3_Wzr2Aiyc6i0Y3kmnxrQyg,10974
scipy/optimize/tests/test_milp.py,sha256=V4KeW9Z3CfCvCk_NT88yqvw9E_t2r-aIq-yJFwVIaWY,18302
scipy/optimize/tests/test_minimize_constrained.py,sha256=avT1wMWHBXQnezIGrhq96k4R41qSXaJRB2dObeb5QAI,27940
scipy/optimize/tests/test_minpack.py,sha256=H73NNF83gZBBX18Iw6uEev2GCnXFNafSCfwLoZhWamg,44845
scipy/optimize/tests/test_nnls.py,sha256=ib5rfMaFlDIq28Ir8H5ZJfvYuVd5ZzPjrXtqKJ7BtM0,27195
scipy/optimize/tests/test_nonlin.py,sha256=cbSdWAV_k4imavhDDaa9u7EAvBq8Uc7zV405bDni7ps,20244
scipy/optimize/tests/test_optimize.py,sha256=y34yJrdYqJjJMdLVzkDZrQphB9MsnBEw1pfloX9EK1s,130630
scipy/optimize/tests/test_quadratic_assignment.py,sha256=4BKOjpEPgSi0YATody23JUjzZ749rh-F7sMWlpuvy4g,17598
scipy/optimize/tests/test_regression.py,sha256=CSg8X-hq6-6jW8vki6aVfEFYRUGTWOg58silM1XNXbU,1077
scipy/optimize/tests/test_slsqp.py,sha256=wi90g6b5s8WxFswZbsTAClaxqSNT-6K12V8oql_1lWs,24616
scipy/optimize/tests/test_tnc.py,sha256=ahSwu8F1tUcPV09l1MsbacUXXi1avQHzQNniYhZRf4s,12700
scipy/optimize/tests/test_trustregion.py,sha256=y49k3H03wdf21FFrUBJpJP7-sqvbxRdvk63cMHkKO3Y,4669
scipy/optimize/tests/test_trustregion_exact.py,sha256=pPY_GRZZ0dwXqUboObatYMpRuwVSwRScCfuu4WkuSbw,12933
scipy/optimize/tests/test_trustregion_krylov.py,sha256=otFMoHYcJZzPdyv7UKOgerehGJXpOB8YWP0-lYHYhUk,6616
scipy/optimize/tests/test_zeros.py,sha256=Wmhadazb1qPcZnzy6WsL1vGy2g1ZyOTlgGrn1QS0-4A,38076
scipy/optimize/tnc.py,sha256=aEKhka8wryg4mVlbrGFwzTJF_KYB49joMkSxKgh1KnA,560
scipy/optimize/zeros.py,sha256=Sc06-J8JUazdfR36UamHhPndJoPK0FkOzHR-unHWoBw,620
scipy/signal/__init__.py,sha256=NWDXthQALmuHicLFhPWnAUFArOyIwV3znFYWImOou9Q,13500
scipy/signal/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-311.pyc,,
scipy/signal/__pycache__/_czt.cpython-311.pyc,,
scipy/signal/__pycache__/_delegators.cpython-311.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-311.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-311.pyc,,
scipy/signal/__pycache__/_polyutils.cpython-311.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-311.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-311.pyc,,
scipy/signal/__pycache__/_signal_api.cpython-311.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-311.pyc,,
scipy/signal/__pycache__/_spline_filters.cpython-311.pyc,,
scipy/signal/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-311.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-311.pyc,,
scipy/signal/__pycache__/bsplines.cpython-311.pyc,,
scipy/signal/__pycache__/filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/spectral.cpython-311.pyc,,
scipy/signal/__pycache__/spline.cpython-311.pyc,,
scipy/signal/__pycache__/waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/wavelets.cpython-311.pyc,,
scipy/signal/_arraytools.py,sha256=k3kHbl9RzcqsyftIYSFJZvJFL4zlcMAHyaRFUkFxOXY,8294
scipy/signal/_czt.py,sha256=t5P1kRCM3iw3eCaL9hTgctMfQKezkqnjbghLjCkffQE,19445
scipy/signal/_delegators.py,sha256=HXAb5GhW-yeNd8BSoxLtk6iMjQndBJNzgaCjcW9WsV0,13943
scipy/signal/_filter_design.py,sha256=K3fo2w0o1oGqYMOTca_gUdV4_auMsQG6GwdAENRYdjk,197028
scipy/signal/_fir_filter_design.py,sha256=uSH2PHCxLOzODCmcTpbLu19rboorpe4foVByu_TPPx8,57266
scipy/signal/_lti_conversion.py,sha256=eYW0yxUFV_pnKJZMOmBII6kjJgI5QvqcQGdbEFFeWdg,16138
scipy/signal/_ltisys.py,sha256=a_cBi71vXzryPQvknlkHb06jiwp63mSGRWGmHmdXkoM,121028
scipy/signal/_max_len_seq.py,sha256=8QkMWoYY3qy3bCKfsuXaS93Bnb2zd-ue6j5i5-3_hi0,5060
scipy/signal/_max_len_seq_inner.cpython-311-darwin.so,sha256=rBr-atP4qQjye1KKZZbMeITG3AmvsQ_sc2O5alNVCAM,76008
scipy/signal/_peak_finding.py,sha256=e9vpWL98OQ9Ik1f7gwLl4d5feTAiyLwPm_yarJq3T_8,48856
scipy/signal/_peak_finding_utils.cpython-311-darwin.so,sha256=9qL9ndUTPz-mz4CL8buAvzcph0tyQsOk_z51Mhc-Pf8,150168
scipy/signal/_polyutils.py,sha256=4_1_Y38PYfMEWgjbOQh024DbBP1_Nnngp-8PLFcJGtA,5368
scipy/signal/_savitzky_golay.py,sha256=AahANBsLy8d6FKmVgteGiAw1l_4wWWItZYSyOVnj_nk,13447
scipy/signal/_short_time_fft.py,sha256=VSgkms7pg7f7GOxhrRi_ZbTqt2pZXr48u5Fe8sjNrTc,101342
scipy/signal/_signal_api.py,sha256=wp3qv0vBhANNCCq1S--VUWmHRdEDy5u5obbNDNUk_Cw,1237
scipy/signal/_signaltools.py,sha256=TWX2tIdmEsYdXE5oUFysM62jUKy20uNFZavWsdeCEuw,192737
scipy/signal/_sigtools.cpython-311-darwin.so,sha256=IuncpURtqXlkc7v7fyQE7FIna9lqvt1M7N8LBlSI5I4,123456
scipy/signal/_sosfilt.cpython-311-darwin.so,sha256=GVRBhRD0kuXvc4n4rAaHNBSqjLzgkCpQjuoQe099ADI,151264
scipy/signal/_spectral_py.py,sha256=93xuYT8jrjF5H3cEMtMOYbGpfVAERTFctxQ-t4C9hVE,96067
scipy/signal/_spline.cpython-311-darwin.so,sha256=A1yEyZ_7aptfVattWTWq166bdttamCpv1b8FcpJum4A,69744
scipy/signal/_spline.pyi,sha256=9tWZQCI7D84ONLwICZG6psBGtwKxAvLF7JaZ1tQUKoY,948
scipy/signal/_spline_filters.py,sha256=meTSSe0pIRnutEufLvxJNWPpxbLEFhLnv8cenALplnI,25528
scipy/signal/_support_alternative_backends.py,sha256=T6wGDT97cIl8iZjuJSqVrBb4ivOvPD3RVZHUbCW8Nzo,2504
scipy/signal/_upfirdn.py,sha256=bE78hIj-iGh7wCXcMh-3Tus9WM_pptNoGLHKXHLFvu0,7976
scipy/signal/_upfirdn_apply.cpython-311-darwin.so,sha256=VTcEAHbPVuQQFxGXyAUnSOp-faQ2Gy1VCD09DE1FU2U,205464
scipy/signal/_waveforms.py,sha256=0Gembo6HsY--mZJ82tFCEw5RYzfhxeDY-gFHOHdJXBk,22912
scipy/signal/_wavelets.py,sha256=K7wj6hMQgJrJ1sb3b2SB4LRc7JlZx5ej9tX6v7E1YCw,886
scipy/signal/bsplines.py,sha256=G1sa6en1z_41sU7ckRY8-flJjUKSqJJihaxBlwzUd3s,651
scipy/signal/filter_design.py,sha256=EyHs8OX4mdeUi6e3Zf7IWuz6r5Re2eR_t0Bi10JuntM,1112
scipy/signal/fir_filter_design.py,sha256=mJr3FG_K_4qbzLOUasIJmXSycuFmotHkEMCAn812v6I,657
scipy/signal/lti_conversion.py,sha256=6uQ1qaT7XI75DoFmtRqRS94Hkpm-Qvy66CRNhmQ-Lbw,639
scipy/signal/ltisys.py,sha256=TFul9jyL0ujEIchiOnDdIiJKIXZ8SSgOV066DvmX_QA,869
scipy/signal/signaltools.py,sha256=I7U_hMuMf02zpdNi0LcPogucTDf0nUVUSkMZ1eAoq3E,1038
scipy/signal/spectral.py,sha256=RA3jj6AWV6ptNwXfpVrbuyxxed8P7nWw8bLsD0iZIgw,662
scipy/signal/spline.py,sha256=rC_E8HwcpDqwIGdaDF0Cb5uC6kPoAQP57jeQNq-aCmI,536
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-311.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_splines.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-311.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=ioVfbdXOV77dDef-hcpuwqnDES1ZaQg29ZjYTd_ts1g,12748
scipy/signal/tests/mpsig.py,sha256=DHB3eHB0KYA-E0SBebKG36YLk-T5egbwwryne3RwIHM,3308
scipy/signal/tests/test_array_tools.py,sha256=QN4SGbtxDSP2MFvyYl00RasYYyNF4A1g8Y6_1Sij7YQ,3589
scipy/signal/tests/test_bsplines.py,sha256=EHM_-hhG43QD0W4v2gnp3shHohVeLu7LUP7PxVVs4sI,17835
scipy/signal/tests/test_cont2discrete.py,sha256=kIzbS38tcUe2mvIhYHcqDX98Pk5a8DaAZdCoq2hWhBg,14843
scipy/signal/tests/test_czt.py,sha256=2-kcWyadICVl_mF0vbq1KYii-rYMtZiuiOSb6HkYn7w,7156
scipy/signal/tests/test_dltisys.py,sha256=WEs5DsDSKQDm4H7deYr6lCUvm8TkiFd9S4SJIluRWfg,21483
scipy/signal/tests/test_filter_design.py,sha256=BNl2jMiLA-3WdyNvW8Lh1rZkhI-9GH6iKtz0fHS4WcU,209611
scipy/signal/tests/test_fir_filter_design.py,sha256=_FGH3Cyyr3ZE0s8nqngytSc1LPO6o4BhzBJh1nx_yfo,35817
scipy/signal/tests/test_ltisys.py,sha256=wU2ZC7E-lKDQ23_1Uvbem3PA_oNayRvzyccIaUqJbnc,45070
scipy/signal/tests/test_max_len_seq.py,sha256=JzfWWN4n6FO9Axw6H6xWrWyc21LlkqMwkGl23f-V664,3318
scipy/signal/tests/test_peak_finding.py,sha256=ZSybjXxgtO3Go-l9S8d3NMdCR_wgKMllEivr8NDjyRo,36076
scipy/signal/tests/test_result_type.py,sha256=F48EQGbFfQfMwcnt-sMofHGNHVTbHntbMlgoeS2vYcY,1573
scipy/signal/tests/test_savitzky_golay.py,sha256=afOF6B97cKQVR68D_u3NZdF6D0IvUFgmd_EzVZdk-C8,12470
scipy/signal/tests/test_short_time_fft.py,sha256=Xhg3x39QftwNsLwFcirWDa3q-hYtFVlMYu4HCwFGsTM,47868
scipy/signal/tests/test_signaltools.py,sha256=6I_4hXoeECCO1sGQLausMrVPB-g_ph5EuErXRspkOJA,191159
scipy/signal/tests/test_spectral.py,sha256=yy105yO-aqU0Y851If51ojX8fLGWT8E5AiqXN2BYKWQ,81685
scipy/signal/tests/test_splines.py,sha256=dP9Ua8FGgw_z_GUxGd_AxKAbH62Y0rvrGabVJwH9SMA,17078
scipy/signal/tests/test_upfirdn.py,sha256=utXj0C32iwg_N3XPs32EGLEuQp4_YPCCUKB6_AzMQQQ,12602
scipy/signal/tests/test_waveforms.py,sha256=HfyUh2X65Qfv0qNLGOk99XAtwy8ELEcIDJnCCoHR6WY,13554
scipy/signal/tests/test_wavelets.py,sha256=42yMux80J-K7Ue9QLnzN84U9K3j2GRdywMxGpbLldeM,2145
scipy/signal/tests/test_windows.py,sha256=NbBbheU4_0HSbL71eMVuEMhquTtocZkUhVABz8Y4hSk,50589
scipy/signal/waveforms.py,sha256=jfOXW7kgtGdh1nrMo1YLAh79W_Ln3WgzEN2esrp70wE,599
scipy/signal/wavelets.py,sha256=7pA7HVMiXwG4fZZ0Q4nzz47hWWALMTYtxwGrIqV3bNE,510
scipy/signal/windows/__init__.py,sha256=BUSXzc_D5Agp59RacDdG6EE9QjkXXtlcfQrTop_IJwo,2119
scipy/signal/windows/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-311.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-311.pyc,,
scipy/signal/windows/_windows.py,sha256=2Tw5gwXH5wp6RSj8bBbfLJc3zl6PBPTWZgCv0Oxu48Q,89501
scipy/signal/windows/windows.py,sha256=FI6w8mt0V1221Rqv3Do3LuWRWrtKo3hYYTvpB_5UB1c,839
scipy/sparse/__init__.py,sha256=7sYqDxLcEsW7lw3H19wOGn6m1KAysKQ8gn-l9oPyL7M,9950
scipy/sparse/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/__pycache__/_base.cpython-311.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/_construct.cpython-311.pyc,,
scipy/sparse/__pycache__/_coo.cpython-311.pyc,,
scipy/sparse/__pycache__/_csc.cpython-311.pyc,,
scipy/sparse/__pycache__/_csr.cpython-311.pyc,,
scipy/sparse/__pycache__/_data.cpython-311.pyc,,
scipy/sparse/__pycache__/_dia.cpython-311.pyc,,
scipy/sparse/__pycache__/_dok.cpython-311.pyc,,
scipy/sparse/__pycache__/_extract.cpython-311.pyc,,
scipy/sparse/__pycache__/_index.cpython-311.pyc,,
scipy/sparse/__pycache__/_lil.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-311.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-311.pyc,,
scipy/sparse/__pycache__/base.cpython-311.pyc,,
scipy/sparse/__pycache__/bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/construct.cpython-311.pyc,,
scipy/sparse/__pycache__/coo.cpython-311.pyc,,
scipy/sparse/__pycache__/csc.cpython-311.pyc,,
scipy/sparse/__pycache__/csr.cpython-311.pyc,,
scipy/sparse/__pycache__/data.cpython-311.pyc,,
scipy/sparse/__pycache__/dia.cpython-311.pyc,,
scipy/sparse/__pycache__/dok.cpython-311.pyc,,
scipy/sparse/__pycache__/extract.cpython-311.pyc,,
scipy/sparse/__pycache__/lil.cpython-311.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-311.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/sputils.cpython-311.pyc,,
scipy/sparse/_base.py,sha256=N16G_kB0bnOdqY7oXbr_S7-gR1AEhGXrf46_KZegZdw,58568
scipy/sparse/_bsr.py,sha256=0OruL9evP-j9W_fKIkRM3zcc390c98iaUOI_mNsQzN8,30957
scipy/sparse/_compressed.py,sha256=JJn2BE_li1dkeo5PtOR7GRd5c6JWcAw5UmxN9mlAgK4,51673
scipy/sparse/_construct.py,sha256=iZSnx2FCeeJCOG98FULiMRt1rreane8b7hoXzpF9TA0,49620
scipy/sparse/_coo.py,sha256=w0rN8r3k3828VLEi8eJps6lMDulPA4UWC2g9_h0ONVk,61518
scipy/sparse/_csc.py,sha256=A-2ur15at5JLILa9-W02bomBiNuaU5t66pVI5Utum48,11138
scipy/sparse/_csparsetools.cpython-311-darwin.so,sha256=qCB4Lg6Dp1g39Vk74AyVBLUTB-rMo4-5nOi2PBUy9Nc,441456
scipy/sparse/_csr.py,sha256=VQG6ThO-oG9YAUkZHyde5r2fgzyei8O8IewoBBSLlqo,18152
scipy/sparse/_data.py,sha256=nWd86zJkqpe7fa9JWMKmlRzhjTCm7dPGCbPiqN5Nt5o,20970
scipy/sparse/_dia.py,sha256=9sGeRhnVUSuO2i1AM3XCh_-r7nSylmkpySDp8d7lxLU,23914
scipy/sparse/_dok.py,sha256=jCXwW8xPXw9JUyj36Ict8SCCE4bpfymvFCERb_LZSVc,22251
scipy/sparse/_extract.py,sha256=0NWW00hxjk5gl4CjNRHtvcqsx54yNei2VVbqARMOlAo,5058
scipy/sparse/_index.py,sha256=-tcFrFk_YAJ3YGjV7DDqjZm_Bnthx-mcbr-1ytUMtRk,16368
scipy/sparse/_lil.py,sha256=uS3i5M_yhLjTDk9xySG_4COGgJA2QcwIpKphuwhcCV4,21125
scipy/sparse/_matrix.py,sha256=e57TxL-4_ZNCWWwW4GdwYmY2fplQQCQqhpBheJn-XRk,5022
scipy/sparse/_matrix_io.py,sha256=0ZEoczSQq59zOGd_eWk6sfACt62vdQmth3ia7uqWFTM,5960
scipy/sparse/_sparsetools.cpython-311-darwin.so,sha256=Jk8MAcJMFnEXtPTJNZKNh4w7guUGjL3H4lsNlrqxOKo,3579168
scipy/sparse/_spfuncs.py,sha256=lDVTp6CiQIuMxTfSzOi3-k6p97ayXJxdKPTf7j_4GWc,1987
scipy/sparse/_sputils.py,sha256=sc3DLX5J-rWt1-wU8IWDISdRhuCDNi5swmKPb-9aZB8,21095
scipy/sparse/base.py,sha256=Kyn-S8HXKVlf_mHMGhcxbnLYqFyZpx5ydS707bnOhDM,609
scipy/sparse/bsr.py,sha256=M_uhxnBwHnh0p4C7xE8tj82PiXATa4_gHIfOvLyh3gg,561
scipy/sparse/compressed.py,sha256=lyEi-UD6ygcufeIqRBHpGfjVckp-vIlojYBjfnLc7Us,550
scipy/sparse/construct.py,sha256=vfz2WK4r2bviiKZSUiIsfdDFDu9osyFlXF8ZsxSAn3E,812
scipy/sparse/coo.py,sha256=ui1P-vcbPnLArYHxLWO06N1KFsCfw-CD6TWz_YSQ8-k,592
scipy/sparse/csc.py,sha256=X9TL9GN1YtbJluM0Iq9_cDyDQBACYlJ7MToGQomvKOA,561
scipy/sparse/csgraph/__init__.py,sha256=znrEd48JFLdlcevl8IFDSM104Yl1YvXC0O_f8OUWATs,7842
scipy/sparse/csgraph/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-311.pyc,,
scipy/sparse/csgraph/_flow.cpython-311-darwin.so,sha256=CJCKaJ5Yhlqn26KZubSPT1H4vyljX5LHYalNhMQ-Qa8,186264
scipy/sparse/csgraph/_laplacian.py,sha256=bpCduRWjIhcDpclvPbftx74PExTiW0P3EE6_Ztiop1Y,18273
scipy/sparse/csgraph/_matching.cpython-311-darwin.so,sha256=h-cvshdIqKSYGYvubk7xjwMLmSZekyZj5ovD5vfXz-o,184000
scipy/sparse/csgraph/_min_spanning_tree.cpython-311-darwin.so,sha256=Rn28ocVuSAWQ7f3CmdorUYetY20-H0okx95RWzaPhDs,115272
scipy/sparse/csgraph/_reordering.cpython-311-darwin.so,sha256=KiOhRNSwMzaY3cYtUznDjyxJZmqbOzJezpI6mDjjb6A,170384
scipy/sparse/csgraph/_shortest_path.cpython-311-darwin.so,sha256=_Z1UpvZBXtJjjZOxO1cFuxQWm_zk68aWYX6NIyXJVY8,386296
scipy/sparse/csgraph/_tools.cpython-311-darwin.so,sha256=DkoZpktrdZnrZ062v2WEHSqt5FkuiovGyCXfdjEApy4,186336
scipy/sparse/csgraph/_traversal.cpython-311-darwin.so,sha256=FD7PjD8lYOUHL8vT1L7AV3Jr4XOrDCf30TmxuL_Hvlo,372944
scipy/sparse/csgraph/_validation.py,sha256=SxINtd4jYyH0YWdzspr8JR0syZfO3nMj7C60GWBUr6k,2629
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-311.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=a2HZjm7HsC0STqiDnhN6OJL4yIMcM28VNVtMXDI2BqE,3948
scipy/sparse/csgraph/tests/test_conversions.py,sha256=3n2UJ_rwdcTkD8NfwDrk-8UBplJkqMFw12yPIwX9-R8,1854
scipy/sparse/csgraph/tests/test_flow.py,sha256=I7csygtef5f6Uv67t2y3UZsln8Gg4eS1RE5zr7Xm-Eg,7718
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=9nQDRj5_oVK0CXM-DW2Xb2jofW3YCiI0QBezdBUl_60,10936
scipy/sparse/csgraph/tests/test_matching.py,sha256=AjWHeuMYy6qE8m2RCHPmcVektmM5nTbGl44ic9a2lLU,12394
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=GSv9fe0hPROU8yynAf2m6WO9JC2yyY1aLSXxd7_eiR8,4967
scipy/sparse/csgraph/tests/test_reordering.py,sha256=_WNqdGcU-WNhQRpjCq4Nhp8YY6cmVKb13au5sJPpzig,2569
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=DNpHp6-6ekXx-_O4xXhh3PF-35HjWW20bp2_Z3zpYGw,18484
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=q4LYiXxfwWUc1io4vRVBr9uxMacfdefPvcRlb3TOEnw,2164
scipy/sparse/csgraph/tests/test_traversal.py,sha256=PD1EJ8XD3xyCWU7SF9-Qw-skhEAI3tiNDxrabsXgU2I,6149
scipy/sparse/csr.py,sha256=MaN7K1B7ejJdKlpOZhrkP5YcBVxVPYk_io4okYc_koQ,561
scipy/sparse/data.py,sha256=R-tvadwaAxifKWzzxhz0awKv2OA89AduDenjz1B2Z1A,504
scipy/sparse/dia.py,sha256=KsOmYg5wsX-kOKM_vaarB_3LpeZo0eo1qC1UdsiBEnE,561
scipy/sparse/dok.py,sha256=ki_m850wPAbA8w3qxkXqAWIDo5p2h5cU14c7Pg9PNos,561
scipy/sparse/extract.py,sha256=6qT2PNOilsEhDWl6MhmgpveIuQr4QCs3LATwIrBroOQ,567
scipy/sparse/lil.py,sha256=Gve3XHYPYZavcUPJz1TSOhjv6AtPpkKBHTzCK6FG8ek,562
scipy/sparse/linalg/__init__.py,sha256=KL54k4eDwEf7mHbL21uZe87S2rnSPIFcEI-pT3UpLIw,4111
scipy/sparse/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=PIX7n_d0LOMZZZ65Dz4Mgz9trjKGB2kLaF16PQLkAIs,2039
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=4Nm6RAKQlKI4lQt4z20v0D6m0Vk8eqp0mIzEk5gfztA,3743
scipy/sparse/linalg/_dsolve/_superlu.cpython-311-darwin.so,sha256=iCDURaIt7Fkk7Rhb9a5sP05aWAWN2SnBLU2gRggsJOk,280768
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=NA8YTVL5KcrwjRItpilR1cyQTIidpuU4LhLE8f8CZe4,31177
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=wW-zv9L5PvPHJa94uR6nZqOqYS-T2cj8HoVpxDNPw8M,33213
scipy/sparse/linalg/_eigen/__init__.py,sha256=SwNho3iWZu_lJvcdSomA5cQdcDU8gocKbmRnm6Bf9-0,460
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=niV8PR0Aonw85rbiSPpL-RswAn9TltpUwcni3Qu_kl8,19908
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=0_sC8kKbu3b5BYpGl16sPLrZu6mDxiFhj8xkbG2w5-U,15003
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=CSZWb59AYXjRIU-Mx5bhZrEhPdfAXgxbRhqLisnlC74,1892
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=zDxf9LokyPitn3_0d-PUXoBCh6tWK0eUSvsAj6nkXI0,562
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-311-darwin.so,sha256=ojLWuT13v6dyg9NYfCY6cSuxSEtTIrYP4x56KdoVKoU,480816
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=E1QLSJxqJ8E7hx0ZziQvY-__gjHbJ99cCEl2jBhjJEE,67286
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=yiL2zpB7ti0rwEP5DYXRZD-7JE3m6Wer07MJ4O65e5s,23735
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=E5JEPRoVz-TaLrj_rPm5LP3jCwei4XD-RxbcxYwf5lM,420
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=CdM3hIe0Rm7pfdXAIlt-ddAmhJjTLJGx4FT2mgJTuIs,41967
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=15uXmcxi0BwPYtuD5kaoddsLE9-bN7QvHJimqFGmtOE,27421
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=3rQz_qRbkEpu9tFNK98MfRDYMDVv5ZyPaALTzWhBW54,36794
scipy/sparse/linalg/_expm_multiply.py,sha256=KOSuV2qF4OSKrLGSwUAFT1ibnv4bhU9JBFJkvy9AVXY,26491
scipy/sparse/linalg/_interface.py,sha256=L_MLquiNwSIXqCtKcrmxtVdFn6F6H7mZifKjJAwUglY,29463
scipy/sparse/linalg/_isolve/__init__.py,sha256=Z_eQUYbe6RWMSNi09T9TfPEWm8RsVxcIKYAlihM-U-c,479
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=C6CIO3qXgmCod_AmV3ErdIDrymYCx5_tbqhb0ZwdDa8,15746
scipy/sparse/linalg/_isolve/iterative.py,sha256=-des4DKVAA17GnbdEiKSCJCKWRQLdBZp6WG2lzMRDo8,33423
scipy/sparse/linalg/_isolve/lgmres.py,sha256=4o_BMPrhyjXHH6HABhkU8jpEjVDQPaAzdLrdAFZZTxw,8623
scipy/sparse/linalg/_isolve/lsmr.py,sha256=8MRtv-FJa7nOHlJ7MZ4TsQiWAkZwntD0r55SOQuRqvI,15650
scipy/sparse/linalg/_isolve/lsqr.py,sha256=Ca2SjyNwMFXSckUTW_LqYFkFc5CWOaZ1yiYB0tK2uB8,21322
scipy/sparse/linalg/_isolve/minres.py,sha256=akSoJYFPE5no3XPZrkIpdY8DEVLKdf8XCmmN_6zceCE,10862
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=QiLhe-Z9KRv1TMfe5cbCLO9Nm4vhpNtJEXPChaP_4Lg,5861
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=cDCvcVc_a3aPzDNWKX_3CHUADQ0SpAFeyNsejbQEdE8,26181
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=9J0oq4KEg4UkIOwPQnp7z7U9bJMpCV9NslHCDANCccI,7448
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=6D3aZELcgJrp3Qf_HisAIowcwxnCzAiCfTf77YNsbrg,6362
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=tYKtlTuXMYYHvfpmrhdCqlzk0BIyohl2b-4b0SA6nBg,3759
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=d_rLkqdObBDD4FBpTOYgzwysTqBtYjgV5v1IDLhyr-8,2434
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=VlmvctRaQtjuYvQuoe2t2ufib74Tua_7qsiVrs3j-p0,265
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=c8zWhLGlxrY2fUhZReBTqz-Bm274ds97nES19sylkXs,6161
scipy/sparse/linalg/_isolve/utils.py,sha256=o3T9hbZOk0XXz36AfmTM1KLFHry29atMCldJ1WoZx_k,3387
scipy/sparse/linalg/_matfuncs.py,sha256=O5I5AmSCVoPzdhBMNzmjxoeCOmO8dN57zTfJbbV5GWo,29338
scipy/sparse/linalg/_norm.py,sha256=fr4AgQTg1c5nSB5sFKSmL8-oQKut96ASJRGjWe68v5U,6171
scipy/sparse/linalg/_onenormest.py,sha256=BkWu89ffmifkBdLH--IQ7DiW0hvDkVEiudUx4HRVmcI,15480
scipy/sparse/linalg/_propack/_cpropack.cpython-311-darwin.so,sha256=1Hk_zsgF1Ekq-gg2ADpWMrHE7HTcghdffGhw-wCBQpE,179376
scipy/sparse/linalg/_propack/_dpropack.cpython-311-darwin.so,sha256=na7iCaVacLLC8zSkwOwVUrCoSdDWxs1NX3ZgKFCpGQ4,161360
scipy/sparse/linalg/_propack/_spropack.cpython-311-darwin.so,sha256=Kb8qS2kP6XbpiFZX0gp4ZT0MtcsZErXAtTc_mqDQOyY,161344
scipy/sparse/linalg/_propack/_zpropack.cpython-311-darwin.so,sha256=EDD3dltCZyAVo3hl2zehRXD0dOTUJ2QVa7ojPxRuLzQ,179408
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=1Sqwuz1qoxehANvNjW2sSffmWjLLxZ7imU4kaAu1fwk,34239
scipy/sparse/linalg/_svdp.py,sha256=dUr5v53cR5S40r71QCAVy0qUdKMxOviaWAT0ptrcjTQ,11200
scipy/sparse/linalg/dsolve.py,sha256=fvCzVUda-h-WzwGWDss4FVuv6TVE-OKHzARBlUCDIJw,654
scipy/sparse/linalg/eigen.py,sha256=4BTo8Tc9SNQaruyrF4gRdFE5NstiA0XH9I44IyikZQ4,626
scipy/sparse/linalg/interface.py,sha256=_KXBkGhZWvY_ZmGixqWMZe6J64bCPdjtrqr63HvicUI,573
scipy/sparse/linalg/isolve.py,sha256=diSAxpbYg8PeH75QOEE-CREO8p39f4BZK2dGynJDKIc,649
scipy/sparse/linalg/matfuncs.py,sha256=H2qJl4ZZqZ4bI-E9NCbu1oFfto0EdFxCTKTugMPHRHg,570
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=K7tSwySHF0sMxq06391fhzBwn-eRskwVn74QussqerE,14845
scipy/sparse/linalg/tests/test_interface.py,sha256=q5rZwUzJBIwiW__n-IzztR6HkZEkv8oePzfG0f1j8K8,21086
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=TqDnJFHiKdiwXP0Gb6yaXNAeiReV6TdBe4wMQXmXTI4,21740
scipy/sparse/linalg/tests/test_norm.py,sha256=dJp4VNGpnL5xET60-b1epJqIBZ4g-zDALZWS5Wg60cQ,6716
scipy/sparse/linalg/tests/test_onenormest.py,sha256=Tzn0FcVcKmbjYoseUkkxjq4mCOhG2cPfDyo9fQCYVPI,9252
scipy/sparse/linalg/tests/test_propack.py,sha256=--SIFSXDGzyBOTdGwOhgrYhSkbVy1RiyL_Dt_Yonp_4,5567
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=eawVssB3pRqxRmfzoHAVY7xvJU-Flo7JYsnoxhr76Mw,7302
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=2Z7r1LPx7QTekuXNTLcspGOdJ9riRwioGIpxzIa0Kh4,12854
scipy/sparse/sparsetools.py,sha256=pCcuyQYvIahrvr43V398XHyqwcGtWCPLFH6n1uSYmB8,516
scipy/sparse/spfuncs.py,sha256=TWpfkZk3JErNajVFUH5B85d3r6UuSv0Rsx0lMtUSac0,508
scipy/sparse/sputils.py,sha256=PsqT7RUmiO8ph5jG8GHYmPbacDQFljjc0SL7RMxweJU,508
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_arithmetic1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_common1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_coo.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_dok.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_indexing1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_minmax1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-311.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_arithmetic1d.py,sha256=4woi2qefAsFXR94Tjj_rlvOfdI-e_Vl6wGAFFw01HkI,11984
scipy/sparse/tests/test_array_api.py,sha256=U8TBj4ZJ5Bc6sOsJ6Q8HgnGBhGJK-sLXS1QD_9pK-4c,14201
scipy/sparse/tests/test_base.py,sha256=zFb_GsXr6dSwOU90PrSPxtdPHNWHcmD9MTuSjvnEIGU,220477
scipy/sparse/tests/test_common1d.py,sha256=q1LHzO7HzGulvFrJCren3Vy3RMPXZNxO8aSxq68MUb8,15471
scipy/sparse/tests/test_construct.py,sha256=lX0Yo17OkR2AwAYvlJvbjpHHOHdXNlLOCaPPJs_EcpE,38434
scipy/sparse/tests/test_coo.py,sha256=v6mdl7NCT-hOm_ouIbWchdz13V5ssP40LuHCSVgHCzw,39632
scipy/sparse/tests/test_csc.py,sha256=rB2cBXznxPdQbMZpdQyQitUdCdEeO6bWt7tQ_LBGGDw,2958
scipy/sparse/tests/test_csr.py,sha256=J8q7e22jt0mGv0OdhdRX5xxcAkVWRclHAOmWwWMeauA,7623
scipy/sparse/tests/test_dok.py,sha256=25jxMgYsQ_q-aN5uDvALRX6PuV83LVktQeEF3gVINm8,5959
scipy/sparse/tests/test_extract.py,sha256=4qUPrtCv9H7xd-c9Xs51seQCiIlK45n-9ZEVTDuPiv8,1685
scipy/sparse/tests/test_indexing1d.py,sha256=r6G8k9GNGfMcVgDg13N2kvmaDkl9FL2CzYYfbLfKXQU,20754
scipy/sparse/tests/test_matrix_io.py,sha256=sLyFQeZ8QpiSoTM1A735j-LK4K0MV-L7VnWtNaBJhw4,3305
scipy/sparse/tests/test_minmax1d.py,sha256=UBeHcN4Pw_VAPXtgsyDev5pK3eXvisiiLjibeaiA8S0,4269
scipy/sparse/tests/test_sparsetools.py,sha256=mryRJI-L7sC_eURqSsYk4oQAC-ygwwB3YABAbZsQxk4,10769
scipy/sparse/tests/test_spfuncs.py,sha256=ECs34sgYYhTBWe4hIkx357obH2lLsnJWkh7TfacjThw,3258
scipy/sparse/tests/test_sputils.py,sha256=RXx_xjrYf0xOUyd6AeeKy4UoeHS3kHxna_2ZXiHGYQs,16486
scipy/spatial/__init__.py,sha256=-FVg_WjbK0J0U2kyei6Fz6NgqEso5cipWZ5gHnqjErs,3731
scipy/spatial/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-311.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-311.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-311.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/distance.cpython-311.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/qhull.cpython-311.pyc,,
scipy/spatial/_ckdtree.cpython-311-darwin.so,sha256=WKtIYPIVr6JHXf4SX0tGrIIfJK3dL1mhlznS78JyePg,559568
scipy/spatial/_distance_pybind.cpython-311-darwin.so,sha256=_aITmQX9kBIJB3_kSD9oL8D1brYAhERIbefgZnpDNHU,593368
scipy/spatial/_distance_wrap.cpython-311-darwin.so,sha256=SMg7ZrgABkRiOA_X975BCABuF-GlQLQIk4irux7FMPM,121128
scipy/spatial/_geometric_slerp.py,sha256=d3pavtaMuIIKjupWLwFLt7WrfqvtT18u7wcsBdnuOTs,7951
scipy/spatial/_hausdorff.cpython-311-darwin.so,sha256=o1YD83RXUCl4IuXz4cHq95csiXuZzMJgxmTCwbf0flo,114064
scipy/spatial/_kdtree.py,sha256=ImDiR14DOAhwK--x9VhMjAlH_uhumsKuMin1Np63O7Q,33479
scipy/spatial/_plotutils.py,sha256=cp94kSvt1QzWV6YWjeTrLh0lbWoVQu_0-iagVpoIgMo,7557
scipy/spatial/_procrustes.py,sha256=qvhHPHt_OIKo-ge_k19S4VWqbP6ZgMXLVnNey0JxTb8,4427
scipy/spatial/_qhull.cpython-311-darwin.so,sha256=9ggbgbFgmscDAGiUIr6w_MQqZx3JLMUHCgBecKZHUf8,766640
scipy/spatial/_qhull.pyi,sha256=dmvze3QcaoA_Be6H8zswajVatOPwtJFIFxoZFE9qR-A,5969
scipy/spatial/_spherical_voronoi.py,sha256=v1XkbWI7yoXQ6EJmJHs185vl0qHV8yfRrm3c_gBGyzg,13577
scipy/spatial/_voronoi.cpython-311-darwin.so,sha256=-oxFV33iFraTYHvxim_GSszzIHM6m5DRzfM8kS8uVXo,97056
scipy/spatial/_voronoi.pyi,sha256=aAOiF4fvHz18hmuSjieKkRItssD443p2_w1ggXOIs1g,126
scipy/spatial/ckdtree.py,sha256=0IssUT415ieBOJuvfZJxIra-TeYyd0KxDGLrXDZ_GGw,523
scipy/spatial/distance.py,sha256=QRyRa9OLuD16euqNFSBcUnhEPMGEE0H387Hg5iKprKU,98245
scipy/spatial/distance.pyi,sha256=rVZpbHbTPWeqYN7aBSDBDIt3MLQWbUIYmgwzWJiODjE,5238
scipy/spatial/kdtree.py,sha256=ZYJL8A_WpLyEH29aFQGLbxd9ttFdGBgdglbgAfsvhz8,636
scipy/spatial/qhull.py,sha256=aFE-KscuINt6QIhFC2dqhwFCYu3HSBkVXDH5exHH71s,622
scipy/spatial/qhull_src/COPYING_QHULL.txt,sha256=EG1VyTH9aoSCLlNF2QAnPQWfHCcxDQJWfMsxPF0YxV0,1720
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=ULnYAgX2_AwOVF-VE7XfnW5S0pzhx7UAoocxSnXMaWs,5750
scipy/spatial/tests/data/cdist-X2.txt,sha256=_IJVjXsp3pvd8NNPNTLmVbHOrzl_RiEXz7cb86NfvZ4,11500
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=k19QSfkqhMmByqNMzwWDmM6wf5dt6whdGyfAyUO3AW0,15000
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=5Z9SMsXrtmzeUwJlVmGkrPDC_Km7nVpZIbBl7p3Hdc0,50000
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Yerj1wqIzcdyULlha-q02WBNGyS2Q5o2wAr0XVEkzis,178801
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=NEd2b-DONqUMV9f8gJ2yod17C_5fXGHHZ38PeFsXkyw,3041
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=UCWZJeMkMajbpjeG0FW60b0q-4r1geAyguNY6Chx5bM,178801
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=8Iq7cF8oMJjpqd6qsDt_mKPQK0T8Ldot2P8C5rgbGIU,3041
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=l2kEAu0Pm3OsFJsQtHf9Qdy5jnnoOu1v3MooBISnjP0,178801
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=S4GY3z-rf_BGuHmsnColMvR8KwYDyE9lqEbYT_a3Qag,3041
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=hQzzoZrmw9OXAbqkxC8eTFXtJZrbFzMgcWMLbJlOv7U,178801
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=P92Tm6Ie8xg4jGSP7k7bmFRAP5MfxtVR_KacS73a6PI,3041
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=0Sx5yL8D8pyYDXTIBZAoTiSsRpG_eJz8uD2ttVrklhU,50000
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=3-UwBM7WZa4aCgmW_ZAdRSq8KYMq2gnkIUqU73Z0OLI,178801
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=rkQA2-_d7uByKmw003lFXbXNDjHrUGBplZ8nB_TU5pk,3041
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=IAYroplsdz6n7PZ-vIMIJ4FjG9jC1OSxc3-oVJdSFDM,3041
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=Zb42SoVEnlTj_N_ndnym3_d4RNZWeHm290hTtpp_zO8,3041
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=L7STTmlRX-z-YvksmiAxEe1UoTmDnQ_lnAjZH53Szp0,172738
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=-sZUikGMWskONojs6fJIMX8VEWpviYYg4u1vipY6Bak,2818
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=N5L5CxRT5yf_vq6pFjorJ09Sr-RcnrAlH-_F3kEsyUU,178801
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=DRgzqxRtvQVzFnpFAjNC9TDNgRtk2ZRkWPyAaeOx3q4,3041
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=jz7SGKU8GuJWASH2u428QL9c-G_-8nZvOFSOUlMdCyA,178801
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=37H01o6GibccR_hKIwwbWxGX0Tuxnb-4Qc6rmDxwwUI,178801
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=YmcI7LZ6i-Wg1wjAkLVX7fmxzCj621Pc5itO3PvCm_k,3041
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=IrtJmDQliv4lDZ_UUjkZNso3EZyu7pMACxMB-rvHUj0,3041
scipy/spatial/tests/data/random-bool-data.txt,sha256=MHAQdE4hPVzgu-csVVbm1DNJ80dP7XthJ1kb2In8ImM,6000
scipy/spatial/tests/data/random-double-data.txt,sha256=GA8hYrHsTBeS864GJf0X6JRTvGlbpM8P8sJairmfnBU,75000
scipy/spatial/tests/data/random-int-data.txt,sha256=xTUbCgoT4X8nll3kXu7S9lv-eJzZtwewwm5lFepxkdQ,10266
scipy/spatial/tests/data/random-uint-data.txt,sha256=8IPpXhwglxzinL5PcK-PEqleZRlNKdx3zCVMoDklyrY,8711
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=rkVhIL1mupGuqDrw1a5QFaODzZkdoaLMbGI_DbLLTzM,480
scipy/spatial/tests/test__plotutils.py,sha256=fASbg0i7iLiJIEj5vIkiDuTq3wU0z3mKJY019kzKrFk,3814
scipy/spatial/tests/test__procrustes.py,sha256=wmmnUHRdw_oID0YLi404IEWPH6vEGhvHXSeGPY_idHo,4974
scipy/spatial/tests/test_distance.py,sha256=NRcJaORboRlM-NWLGjwVRVRfbeHK0p8Vs38J6bhcfXU,88434
scipy/spatial/tests/test_hausdorff.py,sha256=XcDEzwFuOR9BaLegIj-DPp5GrAi_RsvcW8oGqJf0xkg,8217
scipy/spatial/tests/test_kdtree.py,sha256=dlSaXMAIXFS73SMM2Vl9UPEe8Vtbyyiz69zmdb8ddYA,49340
scipy/spatial/tests/test_qhull.py,sha256=wf_jw289-0zv-fJmD8nk7cd68yoG8VE95My336NTovU,50183
scipy/spatial/tests/test_slerp.py,sha256=gjBdGVUbaPctmw05Z297dUjq5a1lH3erm1meMQoVzeo,16427
scipy/spatial/tests/test_spherical_voronoi.py,sha256=YCVSpO7-RrmKaAivwrLh5rZJ6CTTNKuIJ9iyhXsi178,14500
scipy/spatial/transform/__init__.py,sha256=n5D6QjY20YvFBXGvDbC7SfgAkSpuaGVIt5tgwTpGOaI,826
scipy/spatial/transform/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-311.pyc,,
scipy/spatial/transform/_rigid_transform.cpython-311-darwin.so,sha256=OGBA7gwajLHmuQK5gckmlqxrFsHVK8xWXgDuNxLH1Oc,350760
scipy/spatial/transform/_rotation.cpython-311-darwin.so,sha256=EMWp8atAFoF85f0sFvIsyD7Vr-A3dPLIDtglKVJPfpI,629712
scipy/spatial/transform/_rotation_groups.py,sha256=XS-9K6xYnnwWywMMYMVznBYc1-0DPhADHQp_FIT3_f8,4422
scipy/spatial/transform/_rotation_spline.py,sha256=B1wmFTqR34W-CMAggNFvFgZwVrgP2v2iFVIzjnAxnA8,14076
scipy/spatial/transform/rotation.py,sha256=co5Bpny89EfCywilEeeLDvJPESBLrSXTCCJqRlfdYzg,556
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rigid_transform.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/tests/test_rigid_transform.py,sha256=EzCh8BV6at4hR--x_mh2U0uVN73olOQS485PNcjTvqY,44978
scipy/spatial/transform/tests/test_rotation.py,sha256=Mfg5YsU7I2lAUYbDp8Hu4bUb5mKu3mN_y7AsYamfIiQ,87126
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=qnG7kfzs5jDe0_nYxVycziOED3zSABZhxoInxNNCfr0,5552
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=Q9foNO0YWoGEzjy3hou8BgMr5HXhqFTp-rtq_3F5P80,5702
scipy/special/__init__.pxd,sha256=l9Y21wnx5fZLvrxCeCMUWQvBI5gHx7LBhimDWptxke8,42
scipy/special/__init__.py,sha256=PD6E0AAQtr-DpS1Z-g2eaqNfk7YZT8KKPyO_LZPRe6Q,32121
scipy/special/__pycache__/__init__.cpython-311.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/_basic.cpython-311.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-311.pyc,,
scipy/special/__pycache__/_input_validation.cpython-311.pyc,,
scipy/special/__pycache__/_lambertw.cpython-311.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-311.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-311.pyc,,
scipy/special/__pycache__/_multiufuncs.cpython-311.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/_sf_error.cpython-311.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-311.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-311.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/special/__pycache__/_testutils.cpython-311.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/basic.cpython-311.pyc,,
scipy/special/__pycache__/orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/sf_error.cpython-311.pyc,,
scipy/special/__pycache__/specfun.cpython-311.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-311.pyc,,
scipy/special/_add_newdocs.py,sha256=j5Zad94PJo1JFqnTHiGgVoRbZisGjhTvHjCCik6Oh14,272716
scipy/special/_basic.py,sha256=hEjDUV3jGTSxKuHB2oF8QAZXEu-cqM08hPzqbTWLM0Y,111830
scipy/special/_comb.cpython-311-darwin.so,sha256=wsqF0vLvzKnqMImofpZbnI9DgWUoX_VcQB7AXSa3Pmk,77672
scipy/special/_ellip_harm.py,sha256=YHHFZXMtzdJxyjZXKsy3ocIsV-eg6ne3Up79BuFl9P8,5382
scipy/special/_ellip_harm_2.cpython-311-darwin.so,sha256=yM3HmsxCxicKyf-7DGGk7NBzFde4t2t2rTNPgX69rDc,115232
scipy/special/_gufuncs.cpython-311-darwin.so,sha256=HQ-NEEYFSM0BG7F1DzO7_hCrwVodUJw-yqIT0S3d798,627520
scipy/special/_input_validation.py,sha256=ZEwg_sZaesaqzaVA_btZQAi_uPXtIViL_u3Zms6UnyQ,474
scipy/special/_lambertw.py,sha256=-oSEnHFQWZiUZXMamxPWjfntWq5tt0rzHmI13DxGHBY,3962
scipy/special/_logsumexp.py,sha256=zn-8NdTWebijsz8NVXoToHKs13nlG3k4F6HbiRQ9Sok,14635
scipy/special/_mptestutils.py,sha256=ocy_wBXqHGIg311jfjATEA8O29ICl4qPnvTgsmTm5qg,14441
scipy/special/_multiufuncs.py,sha256=z9UQsy0fwHF-f6tUZOFAjmhw6EXx3njzA2mkyRk-Zho,18522
scipy/special/_orthogonal.py,sha256=9RcRoMBby-UMRN8bBqK_m34b9gcAhvP3i630SzAnKJk,74230
scipy/special/_orthogonal.pyi,sha256=13Ta8dtK-pe7Jqa9fqhiQm-eeWE7gMNP4kHCnftcbtQ,8265
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/hyp2f1_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-311.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=ZGSeDDpLRsapyx2GbIrqqYR98fvaEQrLn7IE-fuodhE,354
scipy/special/_precompute/expn_asy.py,sha256=JAz0hY1gBJu3Q_dvscQrSJdgKuwpjqFZVwz-sOQQ21w,1265
scipy/special/_precompute/gammainc_asy.py,sha256=P5OFRcPkkpjGQeYCaMZ8SFSUmZG_CjrEHv8OLwgcGFc,2502
scipy/special/_precompute/gammainc_data.py,sha256=jogxBuXLr3uEpMBvpqScDz5TzEEalksH8f-cRGzasck,4077
scipy/special/_precompute/hyp2f1_data.py,sha256=STSBybQ2pCAu6sh8c9tiHsoDOgnisnSp4tkP2cK4MuI,14707
scipy/special/_precompute/lambertw.py,sha256=7f4F3ivouVNZwuvVX8TAi2lPB7LirPS8IfN5lEw9zI0,1961
scipy/special/_precompute/loggamma.py,sha256=iq7ZBrUmk8pXYZwO_wINI4u8ENsLbL9VUShGjGO0Pt0,1094
scipy/special/_precompute/struve_convergence.py,sha256=z7R0Q5_Ye-EqLI9g-yARdl_j5FooofXMRXPLVrIFJQQ,3624
scipy/special/_precompute/utils.py,sha256=JXJuI07Jlm4bDHJFVtj0jHq05p-V1ofeXZB16Y05kzI,887
scipy/special/_precompute/wright_bessel.py,sha256=7z2W3spGANZO31r_xauMA6hIQ0eseRlXx-zJW6du5tU,12868
scipy/special/_precompute/wright_bessel_data.py,sha256=f1id2Gk5TPyUmSt-Evhoq2_hfRgLUU7Qu_mELKtaXGg,5647
scipy/special/_precompute/wrightomega.py,sha256=YpmLwtGJ4qazMDY0RXjhnQiuRAISI-Pr9MwKc7pZlhc,955
scipy/special/_precompute/zetac.py,sha256=LmhJP7JFg7XktHvfm-DgzuiWZFtVdpvYzzLOB1ePG1Q,591
scipy/special/_sf_error.py,sha256=q_Rbfkws1ttgTQKYLt6zFTdY6DFX2HajJe_lXiNWC0c,375
scipy/special/_specfun.cpython-311-darwin.so,sha256=v3YFCZe6o3_XGl4VN1ZxhEigJ3QX2HrXq1nyzBmpddg,189088
scipy/special/_special_ufuncs.cpython-311-darwin.so,sha256=p_5g5-CjEX8JaVv9kLTRkgFbs-cf8L3PM7WIgWagJNA,1132008
scipy/special/_spfun_stats.py,sha256=IjK325nhaTa7koQyvlVaeCo01TN9QWRpK6mDzkuuAq0,3779
scipy/special/_spherical_bessel.py,sha256=Qh8ihvfYZfcyRuk54tGR77o78gkwhVHWKgG2EARuH5g,12457
scipy/special/_support_alternative_backends.py,sha256=nXxQ0ThCDsOrm6jjVHE_VY6KsBSd2JUcFMFNvkhISEc,10720
scipy/special/_test_internal.cpython-311-darwin.so,sha256=4KzYpExKBsSrgaAMzf-M6nonKHvuz6XbZ8l-ozKXqc4,114408
scipy/special/_test_internal.pyi,sha256=cye6-VI7Jxvb4JDfa1R_f7slEDjYUUfM4edFZ_e0XiE,394
scipy/special/_testutils.py,sha256=cvlrivMgy18oJqQKzEe3AdZbm4nRTACkC9kqg7wwgqw,11971
scipy/special/_ufuncs.cpython-311-darwin.so,sha256=magVx60hRGA3ATfCz11hTcMhgDTAX4P8FsoTsJchjXQ,761664
scipy/special/_ufuncs.pyi,sha256=K8yYsK_0QQ9SDas_8uEI8RWDJv5N_0jAnMjXywUEzOg,8859
scipy/special/_ufuncs.pyx,sha256=aKP5hjCNHxTShDb-blqLsEQgj1vw9FWFGVy1r1Aq5xg,559551
scipy/special/_ufuncs_cxx.cpython-311-darwin.so,sha256=kBWV6Ia6rBRnHkbz-I9mZjywPx7ihMEoFH2W_MvIruw,1284112
scipy/special/_ufuncs_cxx.pxd,sha256=-HoYy0THaVlF6C2UuWIDmBhRKFARS_FBG3GA0AL9R3s,5158
scipy/special/_ufuncs_cxx.pyx,sha256=1JKw03Wdkk4sSnupTS6wNdslp6JZY-J5E6Vd8i4eRDc,28797
scipy/special/_ufuncs_cxx_defs.h,sha256=Mc8MRnYwRZO8UH70zfEiicDzW-DQy8LQ95MNRLYeliI,8995
scipy/special/_ufuncs_defs.h,sha256=G5TQaBgvI1PhE7StGCIB7xpcQ3YcHD70wR8CcaGMcP8,2876
scipy/special/add_newdocs.py,sha256=Wnd-5R0wQAVxSolD4QY2CamTSbe1k48Aie3XaBWRKKc,436
scipy/special/basic.py,sha256=LRU8rIxXx42O4eVZv21nFwswAu7JFtQ42_4xT5BwYpE,1582
scipy/special/cython_special.cpython-311-darwin.so,sha256=FuYI-l8xmIZsjNx7GAiWuykIYv9f3Oc8AbjtL7NRG54,2240440
scipy/special/cython_special.pxd,sha256=Zc5_uVRpnzwF-SoJzvjZ37TpxDtObWfIkGSaQ1irbl4,16382
scipy/special/cython_special.pyi,sha256=BQVUCzV8lCylnmLCtnN0Yz_ttlqyzcLc-BZx2KPXPzM,58
scipy/special/orthogonal.py,sha256=aLzv7PzJgsdLpyTrV6Cu-rpHNHWlUAEqOImiW4fuzuE,1724
scipy/special/sf_error.py,sha256=wOZqzX7iipkH39hOHqBlkmretJRbYy-K7PsnZPyaJFU,573
scipy/special/specfun.py,sha256=V1ZaKH1FFHPvzgkFa-UBVaVTLJRO4fodr7NAW_1jExo,588
scipy/special/spfun_stats.py,sha256=ESJXGUwH7iijUk6aXZQVI1pnaWiVZ6_l0hVpC4bBSIw,535
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boost_ufuncs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cephes_intp_cast.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_iv_ratio.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_legendre.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_log1mexp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_specfun.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ufunc_signatures.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-311.pyc,,
scipy/special/tests/_cython_examples/extending.pyx,sha256=0ISFhXHFnwuWXg5m9VIYdWGjP_W7hxUE8SwFNkvAM_s,292
scipy/special/tests/_cython_examples/meson.build,sha256=7WUABNMPYujt2fmD4YNKqihhwaT9AaOs942x7ah0MWw,810
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/data/boost.npz,sha256=V7XCtn7gHHQVNqrmrZ-PEoEGt_3_FSr889j3dLBkWEQ,1270643
scipy/special/tests/data/gsl.npz,sha256=y_Gv3SeZmAanECeZEKLrL59_VZAzx-y3lt6qEMRP6zE,51433
scipy/special/tests/data/local.npz,sha256=bCnljOgnCE-E258bupYEWmHOafHT6j18gop5wTPPiPI,203438
scipy/special/tests/test_basic.py,sha256=yvOjImn5WgPYKARyQQoPL_uNX65k78K81ngnsIj-KLY,194874
scipy/special/tests/test_bdtr.py,sha256=QwGyt0tnutuou25mS0u2LjRgDTYI6ohM2cbZ-He6Os4,3231
scipy/special/tests/test_boost_ufuncs.py,sha256=sU0fS2zduaR_mkCn2J8nbN3VlmmRQ9ZzbUQ2h_v7R8I,2303
scipy/special/tests/test_boxcox.py,sha256=KK6Ti9TMWKbVaxPVfycrUnM09Th1J2ARhVnI7t7y098,3114
scipy/special/tests/test_cdflib.py,sha256=YW4q4l56i9wbchxMS6wytRQlwFt4sVTRILHwDjWi6JY,24533
scipy/special/tests/test_cdft_asymptotic.py,sha256=DBVVLaduZUHSWlKJ5aBXmxgdNm_YjLvWgyiTTcQq04c,1441
scipy/special/tests/test_cephes_intp_cast.py,sha256=yllVoacRDDS_mH7E_pvDux_Jpf7_Fdt3F9Jsgj3_BaY,1129
scipy/special/tests/test_cosine_distr.py,sha256=zL7aWLisIEy1oNKjcynqncgsCxcPKvPb9Odr-J5Xa1M,2690
scipy/special/tests/test_cython_special.py,sha256=Y79hvQdFnT3w62Lhg8lFDN34hRpDf7vfV3DyNoCqNEY,19128
scipy/special/tests/test_data.py,sha256=n6p4MFRXEejYCe_b0Q7CfIu3OXng4jn1nHnMPT9gCOA,30180
scipy/special/tests/test_dd.py,sha256=I7xSqxTD-GYaO0ol25ZjsGZgqCVt13vbcQlUN7teeG4,1564
scipy/special/tests/test_digamma.py,sha256=Bm7Hh_aETx6MTN3Wu7Sijy4rYGR_1haNGsi3xfzrAKM,1382
scipy/special/tests/test_ellip_harm.py,sha256=0Kooy3pTFwWqmDT33sjxQZ1S8qjNe-MqO4gJhgcPrrI,9635
scipy/special/tests/test_erfinv.py,sha256=fzdEHd6MxfSyzQDO93qndXukG2jWj-XNY2X4BJRIdBI,3059
scipy/special/tests/test_exponential_integrals.py,sha256=hlzNhZEXjo5ioPteG0P85qXuMmVD-WVc67e049tvY8Q,3687
scipy/special/tests/test_extending.py,sha256=7Q8NRxp-QBASTY9y0b8xOcAJmrMKhLaruE_MX7nmJ0M,1184
scipy/special/tests/test_faddeeva.py,sha256=YLY3Ylp4u_8zxTGxOb5kxNfXXEW0ld_GP2ceOR2ev_Y,2568
scipy/special/tests/test_gamma.py,sha256=hb-ZlA2ZNz6gUGvVtMBgXFl_w30HPmthuUEAmNcz0sw,258
scipy/special/tests/test_gammainc.py,sha256=QdOylOmN2CY5cFw0BihCP-05x_d8q0Pitb7-a4DgSic,4441
scipy/special/tests/test_hyp2f1.py,sha256=lvERrDZuLSA7tzVo8rzFuHSNCijGjBuDCDTktJVAcKE,92261
scipy/special/tests/test_hypergeometric.py,sha256=DUDe1YvIXt4IocGlJuqDO5swZ-QOyR2Etj2rwkF-NqQ,9996
scipy/special/tests/test_iv_ratio.py,sha256=6Wa4PDSboT1srHiGUOR78_cTvStWgct31cGkLFvDT5A,10108
scipy/special/tests/test_kolmogorov.py,sha256=-Ika_ORUwxDuaCXATLb489T9lDWoPkJR7r7PNRAE0mE,19280
scipy/special/tests/test_lambertw.py,sha256=vd5G_70CQz3N_U15mcyE0-2KZ_8QYLKmrJ4ZL-RwFXY,4560
scipy/special/tests/test_legendre.py,sha256=ndelP3mnTsONEs2TBKC_y1SBK9oCnYV2o8fTgRslFwU,57925
scipy/special/tests/test_log1mexp.py,sha256=Spw_PfKgjer3aNUEnYiZUaC-dpDwg0cqwsfFxNgYG1U,3142
scipy/special/tests/test_loggamma.py,sha256=x6kuJf-bEnn5ECdkDSgvk3An_A-9UxVsZpqa49IwAq8,1992
scipy/special/tests/test_logit.py,sha256=8tkUtuoxbu42WZ2LWMrHA2aW_IuB3M0Iqe9FZ0VrJbI,6503
scipy/special/tests/test_logsumexp.py,sha256=8euJFCJ8ptFLbHHz85h_XDTQh-hK5zHyLZx6yaz1sZI,18800
scipy/special/tests/test_mpmath.py,sha256=jxxqNVhzhc0ruRNSAES6jEBx-S3_pRwI-nb7AItGrLA,73789
scipy/special/tests/test_nan_inputs.py,sha256=4hBxWwgTIeR2mdjhk-B6CBBC4xNNzEfqXtQcGGP-Ad4,1867
scipy/special/tests/test_ndtr.py,sha256=-UMxTIi4CaaLoJ5-SGW9THChPIM3e1_fTY0L877ioNA,2680
scipy/special/tests/test_ndtri_exp.py,sha256=13eabgdbfcL37RReiUH7g9amT9XMsTLOfwxFJXR_2Ww,3708
scipy/special/tests/test_orthogonal.py,sha256=MPGmoiOWWWcH-auAo2RQN-MpVouFmvZcaoZzbTibDac,32154
scipy/special/tests/test_orthogonal_eval.py,sha256=OPW5OeQWVFHyY7SMG2tY8Ar85StXyz0zfsZe9y9ne14,9571
scipy/special/tests/test_owens_t.py,sha256=zRbiKje7KrYJ25f1ZuIBfiFSyNtK_bnkIW7dRETIqME,1792
scipy/special/tests/test_pcf.py,sha256=RNjEWZGFS99DOGZkkPJ8HNqLULko8UkX0nEWFYX26NE,664
scipy/special/tests/test_pdtr.py,sha256=VmupC2ezUR3p5tgZx0rqXEHAtzsikBW2YgaIxuGwO5A,1284
scipy/special/tests/test_powm1.py,sha256=9hZeiQVKqV63J5oguYXv_vqolpnJX2XRO1JN0ouLWAM,2276
scipy/special/tests/test_precompute_expn_asy.py,sha256=bCQikPkWbxVUeimvo79ToVPgwaudzxGC7Av-hPBgIU4,583
scipy/special/tests/test_precompute_gammainc.py,sha256=6XSz0LTbFRT-k0SlnPhYtpzrlxKHaL_CZbPyDhhfT5E,4459
scipy/special/tests/test_precompute_utils.py,sha256=MOvdbLbzjN5Z1JQQgtIyjwjuIMPX4s2bTc_kxaX67wc,1165
scipy/special/tests/test_round.py,sha256=Zv32kFQrDdOPawfGDeZo1PfBG4UsOyKfd3zjbCWLii0,511
scipy/special/tests/test_sf_error.py,sha256=3-AkADhpVfeaykSORfw7NA6As2QdBNPtrex-IhK8QDg,4240
scipy/special/tests/test_sici.py,sha256=w4anBf8fiq2fmkwMSz3MX0uy35NLXVqfuW3Fwt2Nqek,1227
scipy/special/tests/test_specfun.py,sha256=q2JYEnqmUq78rO8no9hXQZ3fc3RuxPrRCcpsLruovDg,1687
scipy/special/tests/test_spence.py,sha256=fChPw7xncNCTPMUGb0C8BC-lDKHWoEXSz8Rb4Wv8vNo,1099
scipy/special/tests/test_spfun_stats.py,sha256=mKJZ2-kLmVK3ZqX3UlDi9Mx4bRQZ9YoXQW2fxrW2kZs,1997
scipy/special/tests/test_sph_harm.py,sha256=LJjXq4JTKHhEGSHk5GonqLrTTiVRPHbfQZ6lkq4b8PM,3002
scipy/special/tests/test_spherical_bessel.py,sha256=yvwnfjt-eCOChCOi48LsPOEhxCLppo1fA8Qcnp8Hzcg,15027
scipy/special/tests/test_support_alternative_backends.py,sha256=Xiq5FYNFITG16ClZctdmlEtPvM-Bs3HrsDxnhO2C-T4,9732
scipy/special/tests/test_trig.py,sha256=ZlzoL1qKvw2ZCbIYTNYm6QkeKqYUSeE7kUghELXZwzU,2332
scipy/special/tests/test_ufunc_signatures.py,sha256=5tsAbc-QwVe_7YbjbjjYNM1Phiwf51YYqqRx0Hk9EmE,1838
scipy/special/tests/test_wright_bessel.py,sha256=6WHuXB97skPSsoMgXwRlO7bHydFLnl9iDfctEpZE0uE,7694
scipy/special/tests/test_wrightomega.py,sha256=BW8TS_CuDjR7exA4l6ADnKhXwgFWUYaN1UIopMBJUZY,3560
scipy/special/tests/test_zeta.py,sha256=IEPRUdSX5kerDYPmhLWYkYixmUg1ErqHSprQpfkZTP0,11549
scipy/stats/__init__.py,sha256=mUrEnW9fiJ4memdw1jH_gSuar39XNq4IztecOF64a6g,18746
scipy/stats/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-311.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-311.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-311.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-311.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-311.pyc,,
scipy/stats/__pycache__/_common.cpython-311.pyc,,
scipy/stats/__pycache__/_constants.cpython-311.pyc,,
scipy/stats/__pycache__/_continued_fraction.cpython-311.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_correlation.cpython-311.pyc,,
scipy/stats/__pycache__/_covariance.cpython-311.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-311.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-311.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-311.pyc,,
scipy/stats/__pycache__/_distribution_infrastructure.cpython-311.pyc,,
scipy/stats/__pycache__/_entropy.cpython-311.pyc,,
scipy/stats/__pycache__/_finite_differences.cpython-311.pyc,,
scipy/stats/__pycache__/_fit.cpython-311.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-311.pyc,,
scipy/stats/__pycache__/_kde.cpython-311.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-311.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-311.pyc,,
scipy/stats/__pycache__/_mgc.cpython-311.pyc,,
scipy/stats/__pycache__/_morestats.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-311.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-311.pyc,,
scipy/stats/__pycache__/_new_distributions.cpython-311.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-311.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-311.pyc,,
scipy/stats/__pycache__/_probability_distribution.cpython-311.pyc,,
scipy/stats/__pycache__/_qmc.cpython-311.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-311.pyc,,
scipy/stats/__pycache__/_quantile.cpython-311.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-311.pyc,,
scipy/stats/__pycache__/_resampling.cpython-311.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-311.pyc,,
scipy/stats/__pycache__/_sampling.cpython-311.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-311.pyc,,
scipy/stats/__pycache__/_survival.cpython-311.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/__pycache__/_variation.cpython-311.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-311.pyc,,
scipy/stats/__pycache__/_wilcoxon.cpython-311.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-311.pyc,,
scipy/stats/__pycache__/contingency.cpython-311.pyc,,
scipy/stats/__pycache__/distributions.cpython-311.pyc,,
scipy/stats/__pycache__/kde.cpython-311.pyc,,
scipy/stats/__pycache__/morestats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/mvn.cpython-311.pyc,,
scipy/stats/__pycache__/qmc.cpython-311.pyc,,
scipy/stats/__pycache__/sampling.cpython-311.pyc,,
scipy/stats/__pycache__/stats.cpython-311.pyc,,
scipy/stats/_ansari_swilk_statistics.cpython-311-darwin.so,sha256=iK4IjLoL9RFk0YI9La51gkL4N6XyR3Q3DOFgduqpb4Q,116688
scipy/stats/_axis_nan_policy.py,sha256=Iy98OVtoOmzhF3f4mFFcz1AbUomigpifHv47pUM8Sz8,31382
scipy/stats/_biasedurn.cpython-311-darwin.so,sha256=ZmOV2CqeLNbgImtnNmVDmeRKXfYtLj1MdkDaVhsCb60,178160
scipy/stats/_biasedurn.pxd,sha256=bQC6xG4RH1E5h2jCKXRMADfgGctiO5TgNlJegKrR7DY,1046
scipy/stats/_binned_statistic.py,sha256=ATvrikTtX6zW8FKbjpV7O7IvAKSCBBLQSH1JKFR9R7Q,32702
scipy/stats/_binomtest.py,sha256=aW6p-vRkv3pSB8_0nTfT3kNAhV8Ip44A39EEPyl9Wlc,13118
scipy/stats/_bws_test.py,sha256=XQMGiLMPKFN3b6O4nD5tkZdcI8D8vggSx8B7XLJ5EGs,7062
scipy/stats/_censored_data.py,sha256=Ts7GSYYti2z-8yoOJTedj6aCLnGhugLlDRdxZc4rPxs,18306
scipy/stats/_common.py,sha256=4RqXT04Knp1CoOJuSBV6Uy_XmcmtVr0bImAbSk_VHlQ,172
scipy/stats/_constants.py,sha256=mBeJgvWcDZBmPFStDNEjlzeZY3aMDMCHWoj7dCmgugQ,1002
scipy/stats/_continued_fraction.py,sha256=2WyLuQWsx9aIHkYvTE4_VlepAfSKG4otiu_Y5wYbzKA,15508
scipy/stats/_continuous_distns.py,sha256=sktJ4sY37OLvvymZKfeMKghjxWH64CsOYld3LEoHRzQ,406584
scipy/stats/_correlation.py,sha256=kj9EhgPYOnqwQkEgTwdj67iYEwDsntKgcUtQElgQpk0,7914
scipy/stats/_covariance.py,sha256=SLFFrCly5UPu0d-nn2P_U-jdI71qa3w6AGVMnDsxvi0,22660
scipy/stats/_crosstab.py,sha256=djdU7xCQ-513VlxFEOvLN8oaY4QyUPHDJHWlilhyEVA,7351
scipy/stats/_discrete_distns.py,sha256=LZ_MakDbm14ygu24l-BqWT9k41lSptVu9OVq91bQ2K0,65473
scipy/stats/_distn_infrastructure.py,sha256=AjUhOgqm-_R_3leAsqowo_hs0GZvvGg0jaLc7LbICC4,152345
scipy/stats/_distr_params.py,sha256=bD2Sdq0etEh0NYfi3-vFM-C7PevQfH0dRLbNnXeOtYY,9052
scipy/stats/_distribution_infrastructure.py,sha256=UoCMUslqepEtGz_YsrYjlAlj3JkqcoXgITFScmI89UU,233676
scipy/stats/_entropy.py,sha256=lT10WPcnWF23Z9hsiY6cC82aC0MPMXFGu27dfsan5Tc,15768
scipy/stats/_finite_differences.py,sha256=QaA5p36T0oDt4e_oMOE3QGBT8gB2C3E3ziZSWkkBF9g,4168
scipy/stats/_fit.py,sha256=PmLg5oE25gnOIHVV-4U-nfUEsKdfgac4M9OaBSjKrow,59747
scipy/stats/_hypotests.py,sha256=8wEEnCrNIs9Mroff0cCdbgzaUat4-kcPxOAGqLJ2rN0,81346
scipy/stats/_kde.py,sha256=eLh5TP8UDJyKqQlx3-q27UyLvSibimDooZNPpcKLhDI,25678
scipy/stats/_ksstats.py,sha256=8Oo_0BAAZnDkLgckkySAFGxUo51ksnDADzkBe4RdkmU,20140
scipy/stats/_levy_stable/__init__.py,sha256=4JyBm_fpz41F34NSRk-CyZjGeLoovBlyCR7RmMgQ-2M,45903
scipy/stats/_levy_stable/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_levy_stable/levyst.cpython-311-darwin.so,sha256=e1-VZY3UubqbPvSZduZWgEzEvoZtlpmyqCToViXdeoU,78880
scipy/stats/_mannwhitneyu.py,sha256=LQII0f5CF4-OfWXqBuP4uPjNJ8IuVgPp04itqacy1EA,19330
scipy/stats/_mgc.py,sha256=iImSUbFmYh_7Ouap70PFP6O6CVpUylf5y44z33j3obg,21359
scipy/stats/_morestats.py,sha256=kJp4WGWU7Nkrk_96-ZW8c5hbu0FfFih2DWk95d_MnMQ,172445
scipy/stats/_mstats_basic.py,sha256=Thh1IkZUX3HwIumwUvN4SSLIsEGYTkv3hWysLufoEE4,122909
scipy/stats/_mstats_extras.py,sha256=0LL3I-tOG17fI5CKPBK7a8e5-yrgX4XLjfsHOs5MMQs,16362
scipy/stats/_multicomp.py,sha256=x9XBSCbTWl4V-hUZ_YaMYZ5smpE95qBCUic6yYygnpA,16836
scipy/stats/_multivariate.py,sha256=G85Nc9ZyxDjiRe2RMbmpt2Ov9Sv2e9RWAHVgDFlUQNg,248624
scipy/stats/_new_distributions.py,sha256=VgLUIBsFZF07H9D6fMwIQfUGLgw_qVJx6JqJrgX7fAc,16192
scipy/stats/_odds_ratio.py,sha256=zZvZsD7ftKeWUrypXeUapcNoq006XldVAkMMC3RLbWE,17005
scipy/stats/_page_trend_test.py,sha256=7wOh2MFavBQm2bJkn-myMqG9AjWPIDLcGsXJ_J9DVlA,19234
scipy/stats/_probability_distribution.py,sha256=lR63klqPgCI787OqH5hqcKsLRe_7R5t_If1SreGQ9G8,69914
scipy/stats/_qmc.py,sha256=zwIRp5hyRaysWg1y2f5G-LYHWCIoAEqMCa4esi8hbUM,107807
scipy/stats/_qmc_cy.cpython-311-darwin.so,sha256=3ohwgm-CcY-zamfeBFW4WKidHiuYTCl8L7U6ziegtGA,140144
scipy/stats/_qmc_cy.pyi,sha256=xOpTSlaG_1YDZhkJjQQtukbcgOTAR9FpcRMkU5g9mXc,1134
scipy/stats/_qmvnt.py,sha256=IPhvQkRSNPjsFteV-2zlUSdtvvOGDS6hkPlknlBvdu4,15718
scipy/stats/_qmvnt_cy.cpython-311-darwin.so,sha256=_2G9Y_ERjklJ9ZvLo-sIr4MJ6NskipFYQi4Mt8a4F-g,133920
scipy/stats/_quantile.py,sha256=tJodJ0k_aVGw0NpFe_0JkScq_4Rubbj8-XUWXVHT8dA,13418
scipy/stats/_rcont/__init__.py,sha256=dUzWdRuJNAxnGYVFjDqUB8DMYti3by1WziKEfBDOlB4,84
scipy/stats/_rcont/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_rcont/rcont.cpython-311-darwin.so,sha256=Q5FTmUcnkI-iu9W2DBXLaezR1HzVlt01CucJ9oB2NuU,113816
scipy/stats/_relative_risk.py,sha256=5zeYBMshYwtomiLTkaXc1nmWYD0FsaQNjf0iuDadtSc,9571
scipy/stats/_resampling.py,sha256=rYM1J5KBxp1KO79ESRAOOsXh9bx682CWMTOXPtKmTX4,103004
scipy/stats/_result_classes.py,sha256=_ghuGdpFsCMuEmnfHg1AeorR-fASc77ACXYWEmQzXjI,1085
scipy/stats/_sampling.py,sha256=jVdGtsHyII1GPTwHFKba_aUQAEUIYnaPKz_3v0yKGfI,46407
scipy/stats/_sensitivity_analysis.py,sha256=rSzMU4dmjN_zL-bt8tcxTTQbpRxNZuKrKn46zQtJyJc,25041
scipy/stats/_sobol.cpython-311-darwin.so,sha256=2PFoVKpLfLw-ZOFN6LvpMDRgYlRUSMDusNIe5C7WBXA,220928
scipy/stats/_sobol.pyi,sha256=TAywylI75AF9th9QZY8TYfHvIQ1cyM5QZi7eBOAkrbg,971
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cpython-311-darwin.so,sha256=qod-MbaYTX-Ty1taG-GRCboQKxKn9nJZYDMqjMEvfBU,448144
scipy/stats/_stats.pxd,sha256=T_7IrDqgIahKMECV5WAtxtsoV91XBVRM359kAXPIhww,709
scipy/stats/_stats_mstats_common.py,sha256=f9B_XmuN2OTZei2CpWQnrvHO_rcdfdBXsvQgByISY4o,12472
scipy/stats/_stats_py.py,sha256=wfmIZ7zkoL2zNrbDO9kwvPUPi5quamVOH5ptIGS9o7Q,422794
scipy/stats/_stats_pythran.cpython-311-darwin.so,sha256=IX_E7TDP2Tp8bWRiSD_TbFSTxu8oq4PC6DZgJc931xU,170152
scipy/stats/_survival.py,sha256=JexV_eUz0H_2QSwpido_M_LJr4mkODmhHVwjzFXjgj8,25939
scipy/stats/_tukeylambda_stats.py,sha256=eodvo09rCVfcYa1Uh6BKHKvXyY8K5Zg2uGQX1phQ6Ew,6871
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_unuran/unuran_wrapper.cpython-311-darwin.so,sha256=UBMzDobLJvwg9vLAMnH5SszXcZA0OXQWnH5SQHkDf_w,547544
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=KFcmefkNMDwGB6BDttAzcvi7vZrU6XuD23BcYFdsyBQ,5615
scipy/stats/_variation.py,sha256=qUg6GOwwPytwDjpK5av_t-71U2UijPfST1J-QLbGgi4,4565
scipy/stats/_warnings_errors.py,sha256=MpucxNFYEDytXh7vrZCMqTkRfuXTvvMpQ2W_Ak2OnPk,1196
scipy/stats/_wilcoxon.py,sha256=1Biio5qRv9hhxE9cC6_2luBwCcaMS31tN5fJgAOtxQ8,9507
scipy/stats/biasedurn.py,sha256=ECfilE4KrIhU2sK-KWtr8yxqthfVsyz_-o4F2TnMXU4,431
scipy/stats/contingency.py,sha256=psNLzIB1A00rE4U9LwdYyt6XpYZlPRBCqQSMOEjHH04,18649
scipy/stats/distributions.py,sha256=9Kt2fyTohorJcf6a7M9DYH8Nu4jEU66nKP01cRhKmuE,859
scipy/stats/kde.py,sha256=8ZThSc3lz-l1Gb2jzIvy1J87_HTd7eXzxuPLClVpo7c,516
scipy/stats/morestats.py,sha256=GdMXz4MSuPp7hsff_DoijVtFsCEyy6J3_M7BITKGiP4,973
scipy/stats/mstats.py,sha256=aRbrykjrvl-qOBkmGjlFMH4rbWYSqBBQHReanSAomFg,2466
scipy/stats/mstats_basic.py,sha256=PjgL37PCPwiDx_ptqnmKXc1W3QGlRjjPrG0nI5FA4So,1394
scipy/stats/mstats_extras.py,sha256=925lNnnf_NTRoyAnXql-k9syzhv7MF6T2kPGsdE2FHc,721
scipy/stats/mvn.py,sha256=pOcB_Dd_DHpfbYnuJKq-wqmNNGCun1M0294xK1bX0KQ,498
scipy/stats/qmc.py,sha256=b6gLkc_FSm11Ssb9uIai4XxLk4XL_qqK6Jc2k4RSeN0,11703
scipy/stats/sampling.py,sha256=VYwxxGosFs-T3qdCmdw4tJYEFLlegwj-JgDin7iwndE,1939
scipy/stats/stats.py,sha256=EgWjDdnlfCRKJymUcBDvMvPn0ZLO3G_ml1XJ7wvMbCI,1512
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continued_fraction.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_correlation.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_marray.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mgc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_quantile.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-311.pyc,,
scipy/stats/tests/common_tests.py,sha256=RgrKEEuoxKRmoPyxvjJR2RIKQ9AeKh8qHlQUSKOft-g,12521
scipy/stats/tests/data/__pycache__/_mvt.cpython-311.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-311.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=OvFCmMqI74DWIgo32UV55dP1nzvFvYBSyYcmKJes9pI,6905
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=BKxPAi4h3IOebcZYGxCbutYuAX0tlb40P0DEkfEi918,27349
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=Qdd0i7H4cNhAABfFOZPuplhi_9SCquFpO-hNkyRcMD8,3063
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=x9wJ2g1qnzf4DK_w9F_WiOiDMDEg4td2z6uU77G07xM,1947
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=KdnJedRthF7XLA-w7XkIPIMTgzu89yBAMmZA2H4uQOQ,6055
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=nCPyxRk1dAoSPWiC7kG4dLaXs2GL3-KRXRt2NwgXoIA,46561
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=6yPHiQSk0KI4oURQOk99t-uEm-IZN-8eIPHb_y0mQ1U,451566
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=fI-HpgJF9cdGdBinclhVzOcWCCc5ZJZuXalUwirV-lc,6815
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=iJTaAWUFn7DPLTd9bQh_EMKEK1DPG0fnN8xk7BQlPRE,53799
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=riOkYT-LRgmJhPpCK32x7xYnD38gwnh_Eo1X8OK3eN8,523605
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=QtSS11d-vkVvqaIEeJ6oNwyET1CKoyQqjlfBl2sTOJA,7381
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=qrxQQ0I6gnhrefygKwT48x-bz-8laD8Vpn7c81nITRg,59228
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=qmELOQyNlH7CWOMt8PQ0Z_yxgg9Hxc4lqZOuHZxxWuc,577633
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=zD_RTRxfqJHVZTAAyddzLDDbhCzKSfwFGr3hwZ1nq30,2591
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=icZGNBodwmJNzOyEki9MreI2lS6nQJNWfnVJiHRNRNM,29239
scipy/stats/tests/test_axis_nan_policy.py,sha256=gz2oT1Qj1u3NrQZ5kurQV0kxKzwe3tPWrXMZUXLr80Q,60706
scipy/stats/tests/test_binned_statistic.py,sha256=WE5KdJq4zJxZ1LuYp8lv-RMcTEyjuSkjvFHWsGMujkM,18814
scipy/stats/tests/test_censored_data.py,sha256=pAQfSHhmcetcxoS1ZgIHVm1pEbapW7az7I-y_8phb5w,6935
scipy/stats/tests/test_contingency.py,sha256=00QIN99yybM_HhrLf8kck85gWPUAQmYIKI7XnVzPF94,10937
scipy/stats/tests/test_continued_fraction.py,sha256=GGRwIZ6zhsVNaR0Kbc7x2GFpOxSV48URXKz7B5_kEBQ,6709
scipy/stats/tests/test_continuous.py,sha256=mN2XHmS98MKhxu7SMsVyviR4pAxdPx-m_bB7CMxx8Ac,93168
scipy/stats/tests/test_continuous_basic.py,sha256=TTpHRJHoQOS-2KJYLdXHUDtHi8e0hNDe3WKvKVHHhs0,43201
scipy/stats/tests/test_continuous_fit_censored.py,sha256=7hu1sSo9hhh0g9pmPMmjj2BI2rkxvA1h20XdMYZeyog,24188
scipy/stats/tests/test_correlation.py,sha256=I_iO0q5jqRa7yWMexR5hDdoeSuJS73HIUjOzzZUpBxE,3507
scipy/stats/tests/test_crosstab.py,sha256=2zqnoWW70MkvFjxAQlpW4vzWI624rcYLAlAVf7vZ9DU,3906
scipy/stats/tests/test_discrete_basic.py,sha256=6xd7X5VxwxRMD9dWOEX37gcm7gRzLRISxz_HkfI9Qpc,21339
scipy/stats/tests/test_discrete_distns.py,sha256=_O6vUQ7TBJpZMegZb50gVwHgxymhNQUsYUQ_8ZGuhXI,25332
scipy/stats/tests/test_distributions.py,sha256=IXavxGdVNF4ASaR-gasGil7F2SeuTAQCngOA2avcsXE,413903
scipy/stats/tests/test_entropy.py,sha256=frDNKbgk4kxYfh6_xQWxSQ41M1Rkbk1nHcKba_TnbD4,12967
scipy/stats/tests/test_fast_gen_inversion.py,sha256=B_I5i2YClc_UK6FDDiDyZI5-dgOcVD9BlQhgaHfMKNY,16050
scipy/stats/tests/test_fit.py,sha256=hE9oIZOhdq8DOFrAGDn-8A58QhGVgmkpaI4TN0CzNEQ,48931
scipy/stats/tests/test_hypotests.py,sha256=Zy8LAp9el7IUSyWRKsfxz8zEmhrCMlFkaWxoSzh9rx4,85240
scipy/stats/tests/test_kdeoth.py,sha256=37Eq00PueMwWZpgxG5F-V3pcNUco2goxb-hJzAT-7WE,22823
scipy/stats/tests/test_marray.py,sha256=drm1QfSibMZ9ucfvKoizVyxMBFXpjtUAFR23MpBOXMw,12454
scipy/stats/tests/test_mgc.py,sha256=x8e8Y1xmBeYZSc9IXoJVSJWudUD8CCbFPe5lmCghfrw,7961
scipy/stats/tests/test_morestats.py,sha256=Vtp0lFiYuAdZivhgaTK1wQTjRX7jV6k2aK0C8YRbaL0,143021
scipy/stats/tests/test_mstats_basic.py,sha256=DiFNZacJ_55dxfIeYHR3yPg5ADK1rZp-Em1-R0P7OEY,87294
scipy/stats/tests/test_mstats_extras.py,sha256=CCexzT1lksTG_WvGvHn6-CuWd_ZXoFviNGnBZd_hE7Y,7297
scipy/stats/tests/test_multicomp.py,sha256=s5mL9NQMvD4khQ12n2_maXKX9Q5pI0HFjcaYMZyhcJ0,17826
scipy/stats/tests/test_multivariate.py,sha256=1I2M38xSiPO-VcllfzS5b0M96IWfiO7p_GMbzwIRYPo,173301
scipy/stats/tests/test_odds_ratio.py,sha256=ZII-yvP_vhuaNa3qPB0Q5lh9yzRF-08ZcdkAwuu5E94,6727
scipy/stats/tests/test_qmc.py,sha256=a-mYEibkr1iLSoh6bBCENVaNrRqYctsl1yOywlAGP4Y,57605
scipy/stats/tests/test_quantile.py,sha256=HAcgUpP4lWWjy9LZ2oyMEWkR7HwBeafhnkDjRQh_IxQ,8512
scipy/stats/tests/test_rank.py,sha256=5fBUqumr2xySlHlFyvWFePy__GVpgOJPGSxR6bxSzJs,12648
scipy/stats/tests/test_relative_risk.py,sha256=jzOGNQ2y9_YfFnXiGAiRDrgahy66qQkw6ZkHgygCJMA,3646
scipy/stats/tests/test_resampling.py,sha256=AS3H9i30YLeL_3Cu6VrdOnfQMjZE2PutzfSSaGX-m_8,82080
scipy/stats/tests/test_sampling.py,sha256=d1hAHT4c950eqbKo3zmOzmBKM54qfLvqHIhXh8t6fOg,54757
scipy/stats/tests/test_sensitivity_analysis.py,sha256=nNF_B6Zl5YxmvppI8TEPOGroDsbgyLTF6jBmdJH2AUw,10678
scipy/stats/tests/test_stats.py,sha256=Ou9n6V3a82j2R9kKt383V7TCTQ8VtoY1YpBrt8g8TXk,408623
scipy/stats/tests/test_survival.py,sha256=Wmig-n93Y2wCuye9btK4QqXwUAdzF0xR_MO9iYZARjU,21958
scipy/stats/tests/test_tukeylambda_stats.py,sha256=6WUBNVoTseVjfrHfWXtU11gTgmRcdnwAPLQOI0y_5U8,3231
scipy/stats/tests/test_variation.py,sha256=1KLkgmYwVLzRIZWFJ5b-nrh4tx9qhw13NVPrKMXpTMo,9393
scipy/version.py,sha256=vHJ9lZPUxgMxHCE5G2mj_MZ0sRHXkJaNLav3O2xizA0,318
