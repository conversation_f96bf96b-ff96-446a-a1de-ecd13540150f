{"summary": {"total_videos": 10, "successful_inferences": 10, "failed_inferences": 0, "success_rate": 1.0, "total_processing_time_seconds": 5.285449981689453, "average_processing_time_per_video_seconds": 0.5282995939254761, "prediction_distribution": {"doctor": 9, "glasses": 1}, "model_info": {"model_path": "models/model_ep013.pth", "device": "mps", "classes": ["doctor", "glasses", "help", "phone", "my_mouth_is_dry", "my_back_hurts", "i_need_to_move"]}}, "individual_results": [{"filename": "doctor 13.mp4", "preprocessing": {"filename": "doctor 13.mp4", "success": true, "error": null, "original_frames": 34, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.8745137453079224, "all_probabilities": {"doctor": 0.8745137453079224, "glasses": 0.11997085809707642, "help": 0.0005084819858893752, "phone": 2.745057372521842e-06, "my_mouth_is_dry": 1.6536313296455774e-06, "my_back_hurts": 0.004935492295771837, "i_need_to_move": 6.700207450194284e-05}, "processing_time_seconds": 1.21999192237854}}, {"filename": "doctor 20.mp4", "preprocessing": {"filename": "doctor 20.mp4", "success": true, "error": null, "original_frames": 46, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.8155990242958069, "all_probabilities": {"doctor": 0.8155990242958069, "glasses": 0.17723408341407776, "help": 0.0012538906885311007, "phone": 4.835149593418464e-05, "my_mouth_is_dry": 6.746566668880405e-06, "my_back_hurts": 0.0056238663382828236, "i_need_to_move": 0.0002340317441849038}, "processing_time_seconds": 0.46171092987060547}}, {"filename": "doctor 3.mp4", "preprocessing": {"filename": "doctor 3.mp4", "success": true, "error": null, "original_frames": 62, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "glasses", "confidence": 0.5139286518096924, "all_probabilities": {"doctor": 0.4784577488899231, "glasses": 0.5139286518096924, "help": 0.0016662145499140024, "phone": 6.02027284912765e-05, "my_mouth_is_dry": 1.2103427252441179e-05, "my_back_hurts": 0.004953686613589525, "i_need_to_move": 0.0009213376324623823}, "processing_time_seconds": 0.45859599113464355}}, {"filename": "glasses 10.mp4", "preprocessing": {"filename": "glasses 10.mp4", "success": true, "error": null, "original_frames": 35, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.919943630695343, "all_probabilities": {"doctor": 0.919943630695343, "glasses": 0.0731075331568718, "help": 0.0003029377548955381, "phone": 1.1294722526145051e-06, "my_mouth_is_dry": 1.2965081168658799e-06, "my_back_hurts": 0.006600816268473864, "i_need_to_move": 4.2646068322937936e-05}, "processing_time_seconds": 0.43043994903564453}}, {"filename": "glasses 8.mp4", "preprocessing": {"filename": "glasses 8.mp4", "success": true, "error": null, "original_frames": 43, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.8700792193412781, "all_probabilities": {"doctor": 0.8700792193412781, "glasses": 0.12173394858837128, "help": 0.00024864828446879983, "phone": 1.623992375243688e-06, "my_mouth_is_dry": 1.2556555475384812e-06, "my_back_hurts": 0.00788042787462473, "i_need_to_move": 5.481965854414739e-05}, "processing_time_seconds": 0.4365682601928711}}, {"filename": "help 10.mp4", "preprocessing": {"filename": "help 10.mp4", "success": true, "error": null, "original_frames": 38, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.8470378518104553, "all_probabilities": {"doctor": 0.8470378518104553, "glasses": 0.14709733426570892, "help": 0.0006102823535911739, "phone": 4.263837126927683e-06, "my_mouth_is_dry": 2.664642579475185e-06, "my_back_hurts": 0.005119269713759422, "i_need_to_move": 0.00012836237146984786}, "processing_time_seconds": 0.45279407501220703}}, {"filename": "help 15.mp4", "preprocessing": {"filename": "help 15.mp4", "success": true, "error": null, "original_frames": 44, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.93015456199646, "all_probabilities": {"doctor": 0.93015456199646, "glasses": 0.06082550063729286, "help": 0.0002859211526811123, "phone": 3.960816457038163e-07, "my_mouth_is_dry": 6.694777425764187e-07, "my_back_hurts": 0.00871125515550375, "i_need_to_move": 2.1708743588533252e-05}, "processing_time_seconds": 0.4501378536224365}}, {"filename": "help 8.mp4", "preprocessing": {"filename": "help 8.mp4", "success": true, "error": null, "original_frames": 37, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.8961217999458313, "all_probabilities": {"doctor": 0.8961217999458313, "glasses": 0.09660719335079193, "help": 0.00021040784486103803, "phone": 1.0353155630582478e-06, "my_mouth_is_dry": 9.273660452890908e-07, "my_back_hurts": 0.007017964497208595, "i_need_to_move": 4.061382060172036e-05}, "processing_time_seconds": 0.44920873641967773}}, {"filename": "phone 9.mp4", "preprocessing": {"filename": "phone 9.mp4", "success": true, "error": null, "original_frames": 46, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.578030526638031, "all_probabilities": {"doctor": 0.578030526638031, "glasses": 0.4111148416996002, "help": 0.0008456644136458635, "phone": 2.9692775569856167e-05, "my_mouth_is_dry": 7.670520062674768e-06, "my_back_hurts": 0.00951826386153698, "i_need_to_move": 0.00045334233436733484}, "processing_time_seconds": 0.47751712799072266}}, {"filename": "pillow 11.mp4", "preprocessing": {"filename": "pillow 11.mp4", "success": true, "error": null, "original_frames": 53, "original_size": [1280, 720], "processed_frames": 24, "processed_size": [96, 96]}, "inference": {"success": true, "predicted_class": "doctor", "confidence": 0.7838647961616516, "all_probabilities": {"doctor": 0.7838647961616516, "glasses": 0.20768815279006958, "help": 0.001245864317752421, "phone": 1.5688614439568482e-05, "my_mouth_is_dry": 5.553142273129197e-06, "my_back_hurts": 0.0069324248470366, "i_need_to_move": 0.00024757179198786616}, "processing_time_seconds": 0.4460310935974121}}], "timestamp": "2025-10-01 17:12:53"}