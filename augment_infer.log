2025-10-01 17:10:19,123 - INFO - ✅ VideoPreprocessor initialized
2025-10-01 17:10:19,123 - INFO - 🚀 ICUInferenceEngine initialized
2025-10-01 17:10:19,123 - INFO - 📱 Device: mps
2025-10-01 17:10:19,123 - INFO - 🎯 Model path: models/model_ep013.pth
2025-10-01 17:10:19,123 - INFO - 📁 Test directory: data/TEST SET
2025-10-01 17:10:19,123 - INFO - 🎯 Starting comprehensive inference...
2025-10-01 17:10:19,642 - INFO - ✅ Created GrayscaleR2Plus1D18 with 7 classes
2025-10-01 17:10:20,321 - ERROR - ❌ Error loading model: Error(s) in loading state_dict for GrayscaleR2Plus1D18:
	Missing key(s) in state_dict: "backbone.stem.0.weight", "backbone.stem.1.weight", "backbone.stem.1.bias", "backbone.stem.1.running_mean", "backbone.stem.1.running_var", "backbone.stem.3.weight", "backbone.stem.4.weight", "backbone.stem.4.bias", "backbone.stem.4.running_mean", "backbone.stem.4.running_var", "backbone.layer1.0.conv1.0.0.weight", "backbone.layer1.0.conv1.0.1.weight", "backbone.layer1.0.conv1.0.1.bias", "backbone.layer1.0.conv1.0.1.running_mean", "backbone.layer1.0.conv1.0.1.running_var", "backbone.layer1.0.conv1.0.3.weight", "backbone.layer1.0.conv1.1.weight", "backbone.layer1.0.conv1.1.bias", "backbone.layer1.0.conv1.1.running_mean", "backbone.layer1.0.conv1.1.running_var", "backbone.layer1.0.conv2.0.0.weight", "backbone.layer1.0.conv2.0.1.weight", "backbone.layer1.0.conv2.0.1.bias", "backbone.layer1.0.conv2.0.1.running_mean", "backbone.layer1.0.conv2.0.1.running_var", "backbone.layer1.0.conv2.0.3.weight", "backbone.layer1.0.conv2.1.weight", "backbone.layer1.0.conv2.1.bias", "backbone.layer1.0.conv2.1.running_mean", "backbone.layer1.0.conv2.1.running_var", "backbone.layer1.1.conv1.0.0.weight", "backbone.layer1.1.conv1.0.1.weight", "backbone.layer1.1.conv1.0.1.bias", "backbone.layer1.1.conv1.0.1.running_mean", "backbone.layer1.1.conv1.0.1.running_var", "backbone.layer1.1.conv1.0.3.weight", "backbone.layer1.1.conv1.1.weight", "backbone.layer1.1.conv1.1.bias", "backbone.layer1.1.conv1.1.running_mean", "backbone.layer1.1.conv1.1.running_var", "backbone.layer1.1.conv2.0.0.weight", "backbone.layer1.1.conv2.0.1.weight", "backbone.layer1.1.conv2.0.1.bias", "backbone.layer1.1.conv2.0.1.running_mean", "backbone.layer1.1.conv2.0.1.running_var", "backbone.layer1.1.conv2.0.3.weight", "backbone.layer1.1.conv2.1.weight", "backbone.layer1.1.conv2.1.bias", "backbone.layer1.1.conv2.1.running_mean", "backbone.layer1.1.conv2.1.running_var", "backbone.layer2.0.conv1.0.0.weight", "backbone.layer2.0.conv1.0.1.weight", "backbone.layer2.0.conv1.0.1.bias", "backbone.layer2.0.conv1.0.1.running_mean", "backbone.layer2.0.conv1.0.1.running_var", "backbone.layer2.0.conv1.0.3.weight", "backbone.layer2.0.conv1.1.weight", "backbone.layer2.0.conv1.1.bias", "backbone.layer2.0.conv1.1.running_mean", "backbone.layer2.0.conv1.1.running_var", "backbone.layer2.0.conv2.0.0.weight", "backbone.layer2.0.conv2.0.1.weight", "backbone.layer2.0.conv2.0.1.bias", "backbone.layer2.0.conv2.0.1.running_mean", "backbone.layer2.0.conv2.0.1.running_var", "backbone.layer2.0.conv2.0.3.weight", "backbone.layer2.0.conv2.1.weight", "backbone.layer2.0.conv2.1.bias", "backbone.layer2.0.conv2.1.running_mean", "backbone.layer2.0.conv2.1.running_var", "backbone.layer2.0.downsample.0.weight", "backbone.layer2.0.downsample.1.weight", "backbone.layer2.0.downsample.1.bias", "backbone.layer2.0.downsample.1.running_mean", "backbone.layer2.0.downsample.1.running_var", "backbone.layer2.1.conv1.0.0.weight", "backbone.layer2.1.conv1.0.1.weight", "backbone.layer2.1.conv1.0.1.bias", "backbone.layer2.1.conv1.0.1.running_mean", "backbone.layer2.1.conv1.0.1.running_var", "backbone.layer2.1.conv1.0.3.weight", "backbone.layer2.1.conv1.1.weight", "backbone.layer2.1.conv1.1.bias", "backbone.layer2.1.conv1.1.running_mean", "backbone.layer2.1.conv1.1.running_var", "backbone.layer2.1.conv2.0.0.weight", "backbone.layer2.1.conv2.0.1.weight", "backbone.layer2.1.conv2.0.1.bias", "backbone.layer2.1.conv2.0.1.running_mean", "backbone.layer2.1.conv2.0.1.running_var", "backbone.layer2.1.conv2.0.3.weight", "backbone.layer2.1.conv2.1.weight", "backbone.layer2.1.conv2.1.bias", "backbone.layer2.1.conv2.1.running_mean", "backbone.layer2.1.conv2.1.running_var", "backbone.layer3.0.conv1.0.0.weight", "backbone.layer3.0.conv1.0.1.weight", "backbone.layer3.0.conv1.0.1.bias", "backbone.layer3.0.conv1.0.1.running_mean", "backbone.layer3.0.conv1.0.1.running_var", "backbone.layer3.0.conv1.0.3.weight", "backbone.layer3.0.conv1.1.weight", "backbone.layer3.0.conv1.1.bias", "backbone.layer3.0.conv1.1.running_mean", "backbone.layer3.0.conv1.1.running_var", "backbone.layer3.0.conv2.0.0.weight", "backbone.layer3.0.conv2.0.1.weight", "backbone.layer3.0.conv2.0.1.bias", "backbone.layer3.0.conv2.0.1.running_mean", "backbone.layer3.0.conv2.0.1.running_var", "backbone.layer3.0.conv2.0.3.weight", "backbone.layer3.0.conv2.1.weight", "backbone.layer3.0.conv2.1.bias", "backbone.layer3.0.conv2.1.running_mean", "backbone.layer3.0.conv2.1.running_var", "backbone.layer3.0.downsample.0.weight", "backbone.layer3.0.downsample.1.weight", "backbone.layer3.0.downsample.1.bias", "backbone.layer3.0.downsample.1.running_mean", "backbone.layer3.0.downsample.1.running_var", "backbone.layer3.1.conv1.0.0.weight", "backbone.layer3.1.conv1.0.1.weight", "backbone.layer3.1.conv1.0.1.bias", "backbone.layer3.1.conv1.0.1.running_mean", "backbone.layer3.1.conv1.0.1.running_var", "backbone.layer3.1.conv1.0.3.weight", "backbone.layer3.1.conv1.1.weight", "backbone.layer3.1.conv1.1.bias", "backbone.layer3.1.conv1.1.running_mean", "backbone.layer3.1.conv1.1.running_var", "backbone.layer3.1.conv2.0.0.weight", "backbone.layer3.1.conv2.0.1.weight", "backbone.layer3.1.conv2.0.1.bias", "backbone.layer3.1.conv2.0.1.running_mean", "backbone.layer3.1.conv2.0.1.running_var", "backbone.layer3.1.conv2.0.3.weight", "backbone.layer3.1.conv2.1.weight", "backbone.layer3.1.conv2.1.bias", "backbone.layer3.1.conv2.1.running_mean", "backbone.layer3.1.conv2.1.running_var", "backbone.layer4.0.conv1.0.0.weight", "backbone.layer4.0.conv1.0.1.weight", "backbone.layer4.0.conv1.0.1.bias", "backbone.layer4.0.conv1.0.1.running_mean", "backbone.layer4.0.conv1.0.1.running_var", "backbone.layer4.0.conv1.0.3.weight", "backbone.layer4.0.conv1.1.weight", "backbone.layer4.0.conv1.1.bias", "backbone.layer4.0.conv1.1.running_mean", "backbone.layer4.0.conv1.1.running_var", "backbone.layer4.0.conv2.0.0.weight", "backbone.layer4.0.conv2.0.1.weight", "backbone.layer4.0.conv2.0.1.bias", "backbone.layer4.0.conv2.0.1.running_mean", "backbone.layer4.0.conv2.0.1.running_var", "backbone.layer4.0.conv2.0.3.weight", "backbone.layer4.0.conv2.1.weight", "backbone.layer4.0.conv2.1.bias", "backbone.layer4.0.conv2.1.running_mean", "backbone.layer4.0.conv2.1.running_var", "backbone.layer4.0.downsample.0.weight", "backbone.layer4.0.downsample.1.weight", "backbone.layer4.0.downsample.1.bias", "backbone.layer4.0.downsample.1.running_mean", "backbone.layer4.0.downsample.1.running_var", "backbone.layer4.1.conv1.0.0.weight", "backbone.layer4.1.conv1.0.1.weight", "backbone.layer4.1.conv1.0.1.bias", "backbone.layer4.1.conv1.0.1.running_mean", "backbone.layer4.1.conv1.0.1.running_var", "backbone.layer4.1.conv1.0.3.weight", "backbone.layer4.1.conv1.1.weight", "backbone.layer4.1.conv1.1.bias", "backbone.layer4.1.conv1.1.running_mean", "backbone.layer4.1.conv1.1.running_var", "backbone.layer4.1.conv2.0.0.weight", "backbone.layer4.1.conv2.0.1.weight", "backbone.layer4.1.conv2.0.1.bias", "backbone.layer4.1.conv2.0.1.running_mean", "backbone.layer4.1.conv2.0.1.running_var", "backbone.layer4.1.conv2.0.3.weight", "backbone.layer4.1.conv2.1.weight", "backbone.layer4.1.conv2.1.bias", "backbone.layer4.1.conv2.1.running_mean", "backbone.layer4.1.conv2.1.running_var", "backbone.fc.weight", "backbone.fc.bias". 
	Unexpected key(s) in state_dict: "base.stem.0.weight", "base.stem.1.weight", "base.stem.1.bias", "base.stem.1.running_mean", "base.stem.1.running_var", "base.stem.1.num_batches_tracked", "base.stem.3.weight", "base.stem.4.weight", "base.stem.4.bias", "base.stem.4.running_mean", "base.stem.4.running_var", "base.stem.4.num_batches_tracked", "base.layer1.0.conv1.0.0.weight", "base.layer1.0.conv1.0.1.weight", "base.layer1.0.conv1.0.1.bias", "base.layer1.0.conv1.0.1.running_mean", "base.layer1.0.conv1.0.1.running_var", "base.layer1.0.conv1.0.1.num_batches_tracked", "base.layer1.0.conv1.0.3.weight", "base.layer1.0.conv1.1.weight", "base.layer1.0.conv1.1.bias", "base.layer1.0.conv1.1.running_mean", "base.layer1.0.conv1.1.running_var", "base.layer1.0.conv1.1.num_batches_tracked", "base.layer1.0.conv2.0.0.weight", "base.layer1.0.conv2.0.1.weight", "base.layer1.0.conv2.0.1.bias", "base.layer1.0.conv2.0.1.running_mean", "base.layer1.0.conv2.0.1.running_var", "base.layer1.0.conv2.0.1.num_batches_tracked", "base.layer1.0.conv2.0.3.weight", "base.layer1.0.conv2.1.weight", "base.layer1.0.conv2.1.bias", "base.layer1.0.conv2.1.running_mean", "base.layer1.0.conv2.1.running_var", "base.layer1.0.conv2.1.num_batches_tracked", "base.layer1.1.conv1.0.0.weight", "base.layer1.1.conv1.0.1.weight", "base.layer1.1.conv1.0.1.bias", "base.layer1.1.conv1.0.1.running_mean", "base.layer1.1.conv1.0.1.running_var", "base.layer1.1.conv1.0.1.num_batches_tracked", "base.layer1.1.conv1.0.3.weight", "base.layer1.1.conv1.1.weight", "base.layer1.1.conv1.1.bias", "base.layer1.1.conv1.1.running_mean", "base.layer1.1.conv1.1.running_var", "base.layer1.1.conv1.1.num_batches_tracked", "base.layer1.1.conv2.0.0.weight", "base.layer1.1.conv2.0.1.weight", "base.layer1.1.conv2.0.1.bias", "base.layer1.1.conv2.0.1.running_mean", "base.layer1.1.conv2.0.1.running_var", "base.layer1.1.conv2.0.1.num_batches_tracked", "base.layer1.1.conv2.0.3.weight", "base.layer1.1.conv2.1.weight", "base.layer1.1.conv2.1.bias", "base.layer1.1.conv2.1.running_mean", "base.layer1.1.conv2.1.running_var", "base.layer1.1.conv2.1.num_batches_tracked", "base.layer2.0.conv1.0.0.weight", "base.layer2.0.conv1.0.1.weight", "base.layer2.0.conv1.0.1.bias", "base.layer2.0.conv1.0.1.running_mean", "base.layer2.0.conv1.0.1.running_var", "base.layer2.0.conv1.0.1.num_batches_tracked", "base.layer2.0.conv1.0.3.weight", "base.layer2.0.conv1.1.weight", "base.layer2.0.conv1.1.bias", "base.layer2.0.conv1.1.running_mean", "base.layer2.0.conv1.1.running_var", "base.layer2.0.conv1.1.num_batches_tracked", "base.layer2.0.conv2.0.0.weight", "base.layer2.0.conv2.0.1.weight", "base.layer2.0.conv2.0.1.bias", "base.layer2.0.conv2.0.1.running_mean", "base.layer2.0.conv2.0.1.running_var", "base.layer2.0.conv2.0.1.num_batches_tracked", "base.layer2.0.conv2.0.3.weight", "base.layer2.0.conv2.1.weight", "base.layer2.0.conv2.1.bias", "base.layer2.0.conv2.1.running_mean", "base.layer2.0.conv2.1.running_var", "base.layer2.0.conv2.1.num_batches_tracked", "base.layer2.0.downsample.0.weight", "base.layer2.0.downsample.1.weight", "base.layer2.0.downsample.1.bias", "base.layer2.0.downsample.1.running_mean", "base.layer2.0.downsample.1.running_var", "base.layer2.0.downsample.1.num_batches_tracked", "base.layer2.1.conv1.0.0.weight", "base.layer2.1.conv1.0.1.weight", "base.layer2.1.conv1.0.1.bias", "base.layer2.1.conv1.0.1.running_mean", "base.layer2.1.conv1.0.1.running_var", "base.layer2.1.conv1.0.1.num_batches_tracked", "base.layer2.1.conv1.0.3.weight", "base.layer2.1.conv1.1.weight", "base.layer2.1.conv1.1.bias", "base.layer2.1.conv1.1.running_mean", "base.layer2.1.conv1.1.running_var", "base.layer2.1.conv1.1.num_batches_tracked", "base.layer2.1.conv2.0.0.weight", "base.layer2.1.conv2.0.1.weight", "base.layer2.1.conv2.0.1.bias", "base.layer2.1.conv2.0.1.running_mean", "base.layer2.1.conv2.0.1.running_var", "base.layer2.1.conv2.0.1.num_batches_tracked", "base.layer2.1.conv2.0.3.weight", "base.layer2.1.conv2.1.weight", "base.layer2.1.conv2.1.bias", "base.layer2.1.conv2.1.running_mean", "base.layer2.1.conv2.1.running_var", "base.layer2.1.conv2.1.num_batches_tracked", "base.layer3.0.conv1.0.0.weight", "base.layer3.0.conv1.0.1.weight", "base.layer3.0.conv1.0.1.bias", "base.layer3.0.conv1.0.1.running_mean", "base.layer3.0.conv1.0.1.running_var", "base.layer3.0.conv1.0.1.num_batches_tracked", "base.layer3.0.conv1.0.3.weight", "base.layer3.0.conv1.1.weight", "base.layer3.0.conv1.1.bias", "base.layer3.0.conv1.1.running_mean", "base.layer3.0.conv1.1.running_var", "base.layer3.0.conv1.1.num_batches_tracked", "base.layer3.0.conv2.0.0.weight", "base.layer3.0.conv2.0.1.weight", "base.layer3.0.conv2.0.1.bias", "base.layer3.0.conv2.0.1.running_mean", "base.layer3.0.conv2.0.1.running_var", "base.layer3.0.conv2.0.1.num_batches_tracked", "base.layer3.0.conv2.0.3.weight", "base.layer3.0.conv2.1.weight", "base.layer3.0.conv2.1.bias", "base.layer3.0.conv2.1.running_mean", "base.layer3.0.conv2.1.running_var", "base.layer3.0.conv2.1.num_batches_tracked", "base.layer3.0.downsample.0.weight", "base.layer3.0.downsample.1.weight", "base.layer3.0.downsample.1.bias", "base.layer3.0.downsample.1.running_mean", "base.layer3.0.downsample.1.running_var", "base.layer3.0.downsample.1.num_batches_tracked", "base.layer3.1.conv1.0.0.weight", "base.layer3.1.conv1.0.1.weight", "base.layer3.1.conv1.0.1.bias", "base.layer3.1.conv1.0.1.running_mean", "base.layer3.1.conv1.0.1.running_var", "base.layer3.1.conv1.0.1.num_batches_tracked", "base.layer3.1.conv1.0.3.weight", "base.layer3.1.conv1.1.weight", "base.layer3.1.conv1.1.bias", "base.layer3.1.conv1.1.running_mean", "base.layer3.1.conv1.1.running_var", "base.layer3.1.conv1.1.num_batches_tracked", "base.layer3.1.conv2.0.0.weight", "base.layer3.1.conv2.0.1.weight", "base.layer3.1.conv2.0.1.bias", "base.layer3.1.conv2.0.1.running_mean", "base.layer3.1.conv2.0.1.running_var", "base.layer3.1.conv2.0.1.num_batches_tracked", "base.layer3.1.conv2.0.3.weight", "base.layer3.1.conv2.1.weight", "base.layer3.1.conv2.1.bias", "base.layer3.1.conv2.1.running_mean", "base.layer3.1.conv2.1.running_var", "base.layer3.1.conv2.1.num_batches_tracked", "base.layer4.0.conv1.0.0.weight", "base.layer4.0.conv1.0.1.weight", "base.layer4.0.conv1.0.1.bias", "base.layer4.0.conv1.0.1.running_mean", "base.layer4.0.conv1.0.1.running_var", "base.layer4.0.conv1.0.1.num_batches_tracked", "base.layer4.0.conv1.0.3.weight", "base.layer4.0.conv1.1.weight", "base.layer4.0.conv1.1.bias", "base.layer4.0.conv1.1.running_mean", "base.layer4.0.conv1.1.running_var", "base.layer4.0.conv1.1.num_batches_tracked", "base.layer4.0.conv2.0.0.weight", "base.layer4.0.conv2.0.1.weight", "base.layer4.0.conv2.0.1.bias", "base.layer4.0.conv2.0.1.running_mean", "base.layer4.0.conv2.0.1.running_var", "base.layer4.0.conv2.0.1.num_batches_tracked", "base.layer4.0.conv2.0.3.weight", "base.layer4.0.conv2.1.weight", "base.layer4.0.conv2.1.bias", "base.layer4.0.conv2.1.running_mean", "base.layer4.0.conv2.1.running_var", "base.layer4.0.conv2.1.num_batches_tracked", "base.layer4.0.downsample.0.weight", "base.layer4.0.downsample.1.weight", "base.layer4.0.downsample.1.bias", "base.layer4.0.downsample.1.running_mean", "base.layer4.0.downsample.1.running_var", "base.layer4.0.downsample.1.num_batches_tracked", "base.layer4.1.conv1.0.0.weight", "base.layer4.1.conv1.0.1.weight", "base.layer4.1.conv1.0.1.bias", "base.layer4.1.conv1.0.1.running_mean", "base.layer4.1.conv1.0.1.running_var", "base.layer4.1.conv1.0.1.num_batches_tracked", "base.layer4.1.conv1.0.3.weight", "base.layer4.1.conv1.1.weight", "base.layer4.1.conv1.1.bias", "base.layer4.1.conv1.1.running_mean", "base.layer4.1.conv1.1.running_var", "base.layer4.1.conv1.1.num_batches_tracked", "base.layer4.1.conv2.0.0.weight", "base.layer4.1.conv2.0.1.weight", "base.layer4.1.conv2.0.1.bias", "base.layer4.1.conv2.0.1.running_mean", "base.layer4.1.conv2.0.1.running_var", "base.layer4.1.conv2.0.1.num_batches_tracked", "base.layer4.1.conv2.0.3.weight", "base.layer4.1.conv2.1.weight", "base.layer4.1.conv2.1.bias", "base.layer4.1.conv2.1.running_mean", "base.layer4.1.conv2.1.running_var", "base.layer4.1.conv2.1.num_batches_tracked", "base.fc.weight", "base.fc.bias". 
2025-10-01 17:10:20,323 - ERROR - ❌ Inference failed: Failed to load model
2025-10-01 17:12:13,581 - INFO - ✅ VideoPreprocessor initialized
2025-10-01 17:12:13,582 - INFO - 🚀 ICUInferenceEngine initialized
2025-10-01 17:12:13,582 - INFO - 📱 Device: mps
2025-10-01 17:12:13,582 - INFO - 🎯 Model path: models/model_ep013.pth
2025-10-01 17:12:13,582 - INFO - 📁 Test directory: data/TEST SET
2025-10-01 17:12:13,582 - INFO - 🎯 Starting comprehensive inference...
2025-10-01 17:12:14,098 - INFO - ✅ Created GrayscaleR2Plus1D18 with 7 classes
2025-10-01 17:12:14,939 - ERROR - ❌ Error loading model: Error(s) in loading state_dict for GrayscaleR2Plus1D18:
	size mismatch for base.stem.0.weight: copying a param with shape torch.Size([45, 1, 3, 7, 7]) from checkpoint, the shape in current model is torch.Size([45, 1, 1, 7, 7]).
2025-10-01 17:12:14,939 - ERROR - ❌ Inference failed: Failed to load model
2025-10-01 17:12:47,391 - INFO - ✅ VideoPreprocessor initialized
2025-10-01 17:12:47,391 - INFO - 🚀 ICUInferenceEngine initialized
2025-10-01 17:12:47,392 - INFO - 📱 Device: mps
2025-10-01 17:12:47,392 - INFO - 🎯 Model path: models/model_ep013.pth
2025-10-01 17:12:47,392 - INFO - 📁 Test directory: data/TEST SET
2025-10-01 17:12:47,392 - INFO - 🎯 Starting comprehensive inference...
2025-10-01 17:12:47,896 - INFO - ✅ Created GrayscaleR2Plus1D18 with 7 classes
2025-10-01 17:12:48,682 - INFO - ✅ Model loaded successfully
2025-10-01 17:12:48,682 - INFO - 📊 Total parameters: 31,303,716
2025-10-01 17:12:48,682 - INFO - 🎯 Trainable parameters: 31,303,716
2025-10-01 17:12:48,684 - INFO - 📹 Found 10 video files
2025-10-01 17:12:48,684 - INFO - 📹 Processing video 1/10: doctor 13.mp4
2025-10-01 17:12:48,684 - INFO - 🎬 Processing: doctor 13.mp4
2025-10-01 17:12:49,904 - INFO - ✅ doctor 13.mp4 -> doctor (87.5%)
2025-10-01 17:12:49,904 - INFO - 📹 Processing video 2/10: doctor 20.mp4
2025-10-01 17:12:49,904 - INFO - 🎬 Processing: doctor 20.mp4
2025-10-01 17:12:50,366 - INFO - ✅ doctor 20.mp4 -> doctor (81.6%)
2025-10-01 17:12:50,366 - INFO - 📹 Processing video 3/10: doctor 3.mp4
2025-10-01 17:12:50,366 - INFO - 🎬 Processing: doctor 3.mp4
2025-10-01 17:12:50,825 - INFO - ✅ doctor 3.mp4 -> glasses (51.4%)
2025-10-01 17:12:50,825 - INFO - 📹 Processing video 4/10: glasses 10.mp4
2025-10-01 17:12:50,825 - INFO - 🎬 Processing: glasses 10.mp4
2025-10-01 17:12:51,255 - INFO - ✅ glasses 10.mp4 -> doctor (92.0%)
2025-10-01 17:12:51,256 - INFO - 📹 Processing video 5/10: glasses 8.mp4
2025-10-01 17:12:51,256 - INFO - 🎬 Processing: glasses 8.mp4
2025-10-01 17:12:51,692 - INFO - ✅ glasses 8.mp4 -> doctor (87.0%)
2025-10-01 17:12:51,692 - INFO - 📹 Processing video 6/10: help 10.mp4
2025-10-01 17:12:51,692 - INFO - 🎬 Processing: help 10.mp4
2025-10-01 17:12:52,145 - INFO - ✅ help 10.mp4 -> doctor (84.7%)
2025-10-01 17:12:52,145 - INFO - 📹 Processing video 7/10: help 15.mp4
2025-10-01 17:12:52,145 - INFO - 🎬 Processing: help 15.mp4
2025-10-01 17:12:52,596 - INFO - ✅ help 15.mp4 -> doctor (93.0%)
2025-10-01 17:12:52,596 - INFO - 📹 Processing video 8/10: help 8.mp4
2025-10-01 17:12:52,596 - INFO - 🎬 Processing: help 8.mp4
2025-10-01 17:12:53,045 - INFO - ✅ help 8.mp4 -> doctor (89.6%)
2025-10-01 17:12:53,045 - INFO - 📹 Processing video 9/10: phone 9.mp4
2025-10-01 17:12:53,045 - INFO - 🎬 Processing: phone 9.mp4
2025-10-01 17:12:53,523 - INFO - ✅ phone 9.mp4 -> doctor (57.8%)
2025-10-01 17:12:53,523 - INFO - 📹 Processing video 10/10: pillow 11.mp4
2025-10-01 17:12:53,523 - INFO - 🎬 Processing: pillow 11.mp4
2025-10-01 17:12:53,969 - INFO - ✅ pillow 11.mp4 -> doctor (78.4%)
2025-10-01 17:12:53,970 - INFO - 💾 Results saved to: inference_results.json
2025-10-01 17:12:53,970 - INFO - 🎉 Comprehensive inference complete!
2025-10-01 17:19:39,573 - INFO - ✅ VideoPreprocessor initialized
2025-10-01 17:19:39,573 - INFO - 🚀 ICUInferenceEngine initialized
2025-10-01 17:19:39,573 - INFO - 📱 Device: mps
2025-10-01 17:19:39,573 - INFO - 🎯 Model path: models/model_ep013.pth
2025-10-01 17:19:39,573 - INFO - 📁 Test directory: data/TEST SET
2025-10-01 17:19:39,573 - INFO - 🎯 Starting comprehensive inference...
2025-10-01 17:19:40,086 - INFO - ✅ Created GrayscaleR2Plus1D18 with 6 classes
2025-10-01 17:19:40,671 - INFO - ✅ Adapted final layer from 7 classes to 6 classes (excluded 'doctor')
2025-10-01 17:19:40,671 - INFO - 📊 FC layer: torch.Size([7, 512]) -> torch.Size([6, 512])
2025-10-01 17:19:40,828 - INFO - ✅ Model loaded successfully
2025-10-01 17:19:40,829 - INFO - 📊 Total parameters: 31,303,203
2025-10-01 17:19:40,829 - INFO - 🎯 Trainable parameters: 31,303,203
2025-10-01 17:19:40,830 - INFO - 🚫 Excluding doctor class video: doctor 3.mp4
2025-10-01 17:19:40,831 - INFO - 🚫 Excluding doctor class video: doctor 13.mp4
2025-10-01 17:19:40,831 - INFO - 🚫 Excluding doctor class video: doctor 20.mp4
2025-10-01 17:19:40,831 - INFO - 📹 Found 7 video files (excluded 3 doctor videos)
2025-10-01 17:19:40,831 - INFO - 📹 Processing video 1/7: glasses 10.mp4
2025-10-01 17:19:40,831 - INFO - 🎬 Processing: glasses 10.mp4
2025-10-01 17:19:41,541 - INFO - ✅ glasses 10.mp4 -> glasses (91.3%)
2025-10-01 17:19:41,541 - INFO - 📹 Processing video 2/7: glasses 8.mp4
2025-10-01 17:19:41,541 - INFO - 🎬 Processing: glasses 8.mp4
2025-10-01 17:19:41,994 - INFO - ✅ glasses 8.mp4 -> glasses (93.7%)
2025-10-01 17:19:41,994 - INFO - 📹 Processing video 3/7: help 10.mp4
2025-10-01 17:19:41,994 - INFO - 🎬 Processing: help 10.mp4
2025-10-01 17:19:42,451 - INFO - ✅ help 10.mp4 -> glasses (96.2%)
2025-10-01 17:19:42,451 - INFO - 📹 Processing video 4/7: help 15.mp4
2025-10-01 17:19:42,451 - INFO - 🎬 Processing: help 15.mp4
2025-10-01 17:19:42,899 - INFO - ✅ help 15.mp4 -> glasses (87.1%)
2025-10-01 17:19:42,900 - INFO - 📹 Processing video 5/7: help 8.mp4
2025-10-01 17:19:42,900 - INFO - 🎬 Processing: help 8.mp4
2025-10-01 17:19:43,340 - INFO - ✅ help 8.mp4 -> glasses (93.0%)
2025-10-01 17:19:43,340 - INFO - 📹 Processing video 6/7: phone 9.mp4
2025-10-01 17:19:43,340 - INFO - 🎬 Processing: phone 9.mp4
2025-10-01 17:19:43,769 - INFO - ✅ phone 9.mp4 -> glasses (97.4%)
2025-10-01 17:19:43,769 - INFO - 📹 Processing video 7/7: pillow 11.mp4
2025-10-01 17:19:43,769 - INFO - 🎬 Processing: pillow 11.mp4
2025-10-01 17:19:44,215 - INFO - ✅ pillow 11.mp4 -> glasses (96.1%)
2025-10-01 17:19:44,216 - INFO - 💾 Results saved to: inference_results_6classes.json
2025-10-01 17:19:44,216 - INFO - 🎉 Comprehensive inference complete!
2025-10-01 17:24:45,844 - INFO - ✅ VideoPreprocessor initialized
2025-10-01 17:24:45,844 - INFO - 🚀 ICUInferenceEngine initialized
2025-10-01 17:24:45,844 - INFO - 📱 Device: mps
2025-10-01 17:24:45,844 - INFO - 🎯 Model path: models/model_ep013.pth
2025-10-01 17:24:45,844 - INFO - 📁 Test directory: data/TEST SET
2025-10-01 17:24:45,844 - INFO - 🎯 Starting comprehensive inference...
2025-10-01 17:24:46,361 - INFO - ✅ Created GrayscaleR2Plus1D18 with 5 classes
2025-10-01 17:24:46,997 - INFO - ✅ Adapted final layer from 7 classes to 5 classes (excluded 'doctor' and 'glasses')
2025-10-01 17:24:46,997 - INFO - 📊 FC layer: torch.Size([7, 512]) -> torch.Size([5, 512])
2025-10-01 17:24:47,275 - INFO - ✅ Model loaded successfully
2025-10-01 17:24:47,275 - INFO - 📊 Total parameters: 31,302,690
2025-10-01 17:24:47,275 - INFO - 🎯 Trainable parameters: 31,302,690
2025-10-01 17:24:47,276 - INFO - 🚫 Excluding doctor class video: doctor 3.mp4
2025-10-01 17:24:47,276 - INFO - 🚫 Excluding doctor class video: doctor 13.mp4
2025-10-01 17:24:47,277 - INFO - 🚫 Excluding glasses class video: glasses 8.mp4
2025-10-01 17:24:47,277 - INFO - 🚫 Excluding doctor class video: doctor 20.mp4
2025-10-01 17:24:47,277 - INFO - 🚫 Excluding glasses class video: glasses 10.mp4
2025-10-01 17:24:47,277 - INFO - 📹 Found 5 video files (excluded 5 videos from classes: {'doctor', 'glasses'})
2025-10-01 17:24:47,277 - INFO - 📹 Processing video 1/5: help 10.mp4
2025-10-01 17:24:47,277 - INFO - 🎬 Processing: help 10.mp4
2025-10-01 17:24:47,906 - INFO - ✅ help 10.mp4 -> my_back_hurts (87.3%)
2025-10-01 17:24:47,906 - INFO - 📹 Processing video 2/5: help 15.mp4
2025-10-01 17:24:47,906 - INFO - 🎬 Processing: help 15.mp4
2025-10-01 17:24:48,353 - INFO - ✅ help 15.mp4 -> my_back_hurts (96.6%)
2025-10-01 17:24:48,353 - INFO - 📹 Processing video 3/5: help 8.mp4
2025-10-01 17:24:48,353 - INFO - 🎬 Processing: help 8.mp4
2025-10-01 17:24:48,780 - INFO - ✅ help 8.mp4 -> my_back_hurts (96.5%)
2025-10-01 17:24:48,781 - INFO - 📹 Processing video 4/5: phone 9.mp4
2025-10-01 17:24:48,781 - INFO - 🎬 Processing: phone 9.mp4
2025-10-01 17:24:49,211 - INFO - ✅ phone 9.mp4 -> my_back_hurts (87.7%)
2025-10-01 17:24:49,211 - INFO - 📹 Processing video 5/5: pillow 11.mp4
2025-10-01 17:24:49,211 - INFO - 🎬 Processing: pillow 11.mp4
2025-10-01 17:24:49,633 - INFO - ✅ pillow 11.mp4 -> my_back_hurts (82.1%)
2025-10-01 17:24:49,634 - INFO - 💾 Results saved to: inference_results_5classes.json
2025-10-01 17:24:49,634 - INFO - 🎉 Comprehensive inference complete!
