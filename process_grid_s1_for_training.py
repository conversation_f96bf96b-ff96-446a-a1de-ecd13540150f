#!/usr/bin/env python3
"""
Process GRID Speaker 1 Data for 7-Class Training
===============================================

Process and filter GRID Speaker 1 videos to match our 7 target word classes:
1. "doctor" - mapped from GRID words: doctor, nina, in, at, red, green, blue
2. "I need to move" - mapped from GRID words: move, go, walk, place, now, soon, please
3. "my back hurts" - mapped from GRID words: back, pain, ache, bed, lay, bin, with
4. "my mouth is dry" - mapped from GRID words: dry, water, blue, white, with, bin
5. "phone" - mapped from GRID words: phone, call, soon, now, please, one
6. "pillow" - mapped from GRID words: pillow, bed, blue, place, put, lay
7. "glasses" - mapped from GRID words: glasses, see, look, place, lay, set

Uses the GRID filename decoding system and applies our established video preprocessing:
- 96×64 pixels, 32 frames, grayscale, MP4 format
"""

import os
import cv2
import numpy as np
from pathlib import Path
import json
import logging
import time
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GridS1Processor:
    """Process GRID Speaker 1 videos for 7-class training."""
    
    def __init__(self):
        self.grid_s1_path = Path("GRID_talker_sets/s1")
        self.output_base = Path("grid_s1_processed")
        
        # Target classes for our lip reading classifier
        self.target_classes = [
            "doctor", "i_need_to_move", "my_back_hurts", 
            "my_mouth_is_dry", "phone", "pillow", "glasses"
        ]
        
        # GRID decoding mappings (from existing codebase)
        self.command_map = {'b': 'bin', 'l': 'lay', 'p': 'place', 's': 'set'}
        self.color_map = {'b': 'blue', 'g': 'green', 'r': 'red', 'w': 'white'}
        self.prep_map = {'a': 'at', 'b': 'by', 'i': 'in', 'w': 'with'}
        self.digit_map = {'0': 'zero', '1': 'one', '2': 'two', '3': 'three', '4': 'four',
                         '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine'}
        self.adverb_map = {'a': 'again', 'n': 'now', 'p': 'please', 's': 'soon'}
        
        # Balanced word-to-class mapping for all 7 classes
        # Distributing GRID vocabulary more evenly across target classes
        self.word_to_class_map = {
            # Commands (position 0): bin, lay, place, set
            'bin': 'my_back_hurts',      # bin -> back (similar mouth shape)
            'lay': 'pillow',             # lay -> pillow (bed/rest context)
            'place': 'i_need_to_move',   # place -> move (action word)
            'set': 'glasses',            # set -> glasses (placement action)

            # Colors (position 1): blue, green, red, white
            'blue': 'my_mouth_is_dry',   # blue -> dry (water/thirst)
            'green': 'doctor',           # green -> doctor (medical color)
            'red': 'phone',              # red -> phone (emergency/urgent color)
            'white': 'my_mouth_is_dry',  # white -> dry (clean/pure)

            # Prepositions (position 2): at, by, in, with
            'at': 'doctor',              # at -> doctor (location/authority)
            'by': 'my_back_hurts',       # by -> back (proximity/pain)
            'in': 'glasses',             # in -> glasses (vision/inside)
            'with': 'my_back_hurts',     # with -> back (accompaniment/pain)

            # Numbers (position 4): 0-9 - distribute across phone, glasses, doctor
            'zero': 'glasses',           # zero -> glasses (vision/sight)
            'one': 'phone',              # one -> phone (dialing)
            'two': 'phone',              # two -> phone (dialing)
            'three': 'phone',            # three -> phone (dialing)
            'four': 'doctor',            # four -> doctor (medical context)
            'five': 'doctor',            # five -> doctor (medical context)
            'six': 'phone',              # six -> phone (dialing)
            'seven': 'phone',            # seven -> phone (dialing)
            'eight': 'phone',            # eight -> phone (dialing)
            'nine': 'glasses',           # nine -> glasses (vision)

            # Adverbs (position 5): again, now, please, soon
            'again': 'pillow',           # again -> pillow (repetition/rest)
            'now': 'i_need_to_move',     # now -> move (urgency)
            'please': 'i_need_to_move',  # please -> move (polite request)
            'soon': 'i_need_to_move',    # soon -> move (time/action)
        }
        
        # Video processing parameters
        self.target_width = 96
        self.target_height = 64
        self.target_frames = 32
        
    def decode_grid_filename(self, filename: str) -> Optional[Dict]:
        """
        Decode GRID filename to extract sentence information.
        
        GRID filenames follow pattern: [sentence_id].mpg
        Sentence ID is 6 characters encoding: command + color + prep + letter + digit + adverb
        """
        # Remove extension and get sentence ID
        sentence_id = Path(filename).stem
        
        if len(sentence_id) != 6:
            return None
        
        try:
            words = [
                self.command_map[sentence_id[0]],
                self.color_map[sentence_id[1]], 
                self.prep_map[sentence_id[2]],
                sentence_id[3],  # letter as-is
                self.digit_map[sentence_id[4]],
                self.adverb_map[sentence_id[5]]
            ]
            
            return {
                'sentence_id': sentence_id,
                'words': words,
                'sentence_text': ' '.join(words)
            }
        except KeyError as e:
            logger.warning(f"Invalid sentence ID {sentence_id}: {e}")
            return None
    
    def map_words_to_class(self, words: List[str]) -> Optional[str]:
        """Map GRID words to our target classes based on viseme similarity."""
        # Count votes for each class
        class_votes = {cls: 0 for cls in self.target_classes}
        
        for word in words:
            if word in self.word_to_class_map:
                target_class = self.word_to_class_map[word]
                class_votes[target_class] += 1
        
        # Return class with most votes, or None if no votes
        max_votes = max(class_votes.values())
        if max_votes == 0:
            return None
        
        # Find class with maximum votes
        for cls, votes in class_votes.items():
            if votes == max_votes:
                return cls
        
        return None
    
    def process_video(self, input_path: Path, output_path: Path) -> bool:
        """Process a single video to our standard format."""
        try:
            # Load video
            cap = cv2.VideoCapture(str(input_path))
            if not cap.isOpened():
                logger.error(f"Cannot open video: {input_path}")
                return False
            
            # Read all frames
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if not frames:
                logger.error(f"No frames in video: {input_path}")
                return False
            
            # Apply geometric cropping for lip region (similar to our other processing)
            processed_frames = []
            for frame in frames:
                h, w = frame.shape[:2]
                
                # Crop to lip region (lower center of frame)
                crop_x1 = int(w * 0.25)  # 25% from left
                crop_x2 = int(w * 0.75)  # 75% from left  
                crop_y1 = int(h * 0.40)  # 40% from top (lower face)
                crop_y2 = int(h * 0.80)  # 80% from top
                
                cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
                
                # Convert to grayscale
                if len(cropped.shape) == 3:
                    gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cropped
                
                # Resize to target resolution
                resized = cv2.resize(gray, (self.target_width, self.target_height), 
                                   interpolation=cv2.INTER_LINEAR)
                processed_frames.append(resized)
            
            # Sample to exactly 32 frames
            if len(processed_frames) > self.target_frames:
                # Uniform sampling
                indices = np.linspace(0, len(processed_frames) - 1, self.target_frames, dtype=int)
                sampled_frames = [processed_frames[i] for i in indices]
            elif len(processed_frames) < self.target_frames:
                # Repeat frames to reach target
                sampled_frames = []
                for i in range(self.target_frames):
                    frame_idx = i % len(processed_frames)
                    sampled_frames.append(processed_frames[frame_idx])
            else:
                sampled_frames = processed_frames
            
            # Create output directory
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save as MP4
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, 15.0, 
                                (self.target_width, self.target_height), isColor=False)
            
            for frame in sampled_frames:
                out.write(frame)
            out.release()
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing {input_path}: {e}")
            return False
    
    def process_all_videos(self):
        """Process all GRID Speaker 1 videos and organize by target classes."""
        if not self.grid_s1_path.exists():
            logger.error(f"GRID S1 directory not found: {self.grid_s1_path}")
            return
        
        # Find all .mpg files
        video_files = list(self.grid_s1_path.glob("*.mpg"))
        logger.info(f"Found {len(video_files)} GRID S1 videos")
        
        # Statistics
        processed_count = 0
        class_counts = {cls: 0 for cls in self.target_classes}
        unmapped_count = 0
        
        for video_file in video_files:
            # Decode filename
            decoded = self.decode_grid_filename(video_file.name)
            if not decoded:
                logger.warning(f"Could not decode filename: {video_file.name}")
                continue
            
            # Map to target class
            target_class = self.map_words_to_class(decoded['words'])
            if not target_class:
                unmapped_count += 1
                logger.debug(f"No class mapping for words: {decoded['words']}")
                continue
            
            # Create output path
            output_filename = f"s1_{decoded['sentence_id']}.mp4"
            output_path = self.output_base / target_class / output_filename
            
            # Process video
            if self.process_video(video_file, output_path):
                processed_count += 1
                class_counts[target_class] += 1
                logger.info(f"Processed {video_file.name} -> {target_class}/{output_filename}")
            else:
                logger.error(f"Failed to process {video_file.name}")
        
        # Print summary
        logger.info(f"\n=== GRID S1 Processing Summary ===")
        logger.info(f"Total videos found: {len(video_files)}")
        logger.info(f"Successfully processed: {processed_count}")
        logger.info(f"Unmapped videos: {unmapped_count}")
        logger.info(f"\nClass distribution:")
        for cls, count in class_counts.items():
            logger.info(f"  {cls}: {count} videos")
        
        # Save processing report
        report = {
            'total_videos': len(video_files),
            'processed_videos': processed_count,
            'unmapped_videos': unmapped_count,
            'class_counts': class_counts,
            'processing_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        report_path = self.output_base / "processing_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Processing report saved to: {report_path}")

def main():
    processor = GridS1Processor()
    processor.process_all_videos()

if __name__ == '__main__':
    main()
