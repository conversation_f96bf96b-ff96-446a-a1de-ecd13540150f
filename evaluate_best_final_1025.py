#!/usr/bin/env python3
"""
Comprehensive Evaluation Script for best_final_1.10.25.pth Model
================================================================

Performs systematic evaluation on selected test videos with clear ground truth labels.
"""

import cv2
import numpy as np
import json
import subprocess
import os
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveModelEvaluator:
    def __init__(self):
        self.test_directory = Path("/Users/<USER>/iCloud Drive (Archive)/Desktop/LRP classifier 11.9.25/test clips/1.10.25")
        self.processed_videos_dir = Path("evaluation_processed_videos")
        self.processed_videos_dir.mkdir(exist_ok=True)
        
        # Model classes in exact order
        self.model_classes = [
            'doctor', 'glasses', 'i_need_to_move', 'my_back_hurts', 
            'my_mouth_is_dry', 'phone', 'pillow'
        ]
        
        # Test videos with clear ground truth
        self.test_videos = [
            {"filename": "doctor 20.mp4", "ground_truth": "doctor"},
            {"filename": "doctor__useruser01__40to64__female__caucasian__20250903T020751.mp4", "ground_truth": "doctor"},
            {"filename": "glasses 12.mp4", "ground_truth": "glasses"},
            {"filename": "i_need_to_move__useruser01__65plus__female__caucasian__20250716T055222.mp4", "ground_truth": "i_need_to_move"},
            {"filename": "i_need_to_move__useruser01__65plus__female__caucasian__20250827T054813.mp4", "ground_truth": "i_need_to_move"},
            {"filename": "my_mouth_is_dry__useruser01__65plus__female__caucasian__20250723T073831.mp4", "ground_truth": "my_mouth_is_dry"},
            {"filename": "phone__useruser01__18to39__female__caucasian__20250822T084158.mp4", "ground_truth": "phone"},
            {"filename": "pillow 11.mp4", "ground_truth": "pillow"}
        ]
        
        self.results = []
    
    def get_video_properties(self, video_path):
        """Get video properties using ffprobe."""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                video_stream = next((s for s in data['streams'] if s['codec_type'] == 'video'), None)
                
                if video_stream:
                    return {
                        'width': int(video_stream['width']),
                        'height': int(video_stream['height']),
                        'nb_frames': int(video_stream.get('nb_frames', 0)),
                        'duration': float(data['format']['duration']),
                        'fps': eval(video_stream['r_frame_rate']),
                        'size_bytes': int(data['format']['size'])
                    }
        except Exception as e:
            logger.error(f"Error getting video properties for {video_path}: {e}")
        
        return None
    
    def preprocess_video(self, input_path, output_path):
        """Preprocess video using robust pipeline."""
        try:
            logger.info(f"🎬 Preprocessing: {input_path.name}")
            
            # Load video
            cap = cv2.VideoCapture(str(input_path))
            if not cap.isOpened():
                return {"success": False, "error": "Could not open video file"}
            
            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Read all frames
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if not frames:
                return {"success": False, "error": "No frames found in video"}
            
            # Determine ROI based on video dimensions (geometric fallback)
            if width > height:  # Landscape
                roi_x = int(width * 0.2)
                roi_y = int(height * 0.3)
                roi_w = int(width * 0.6)
                roi_h = int(height * 0.5)
                format_type = "landscape"
            else:  # Portrait
                roi_x = int(width * 0.15)
                roi_y = int(height * 0.35)
                roi_w = int(width * 0.7)
                roi_h = int(height * 0.25)
                format_type = "portrait"
            
            # Process frames
            processed_frames = []
            for frame in frames:
                # Extract ROI
                roi = frame[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
                
                # Resize to 96×64
                resized = cv2.resize(roi, (96, 64), interpolation=cv2.INTER_AREA)
                
                # Convert to grayscale
                gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
                processed_frames.append(gray)
            
            # Sample to exactly 32 frames
            if len(processed_frames) >= 32:
                indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
                sampled_frames = [processed_frames[i] for i in indices]
            else:
                sampled_frames = processed_frames[:]
                while len(sampled_frames) < 32:
                    sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
                sampled_frames = sampled_frames[:32]
            
            # Save as MP4 (grayscale)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
            
            for frame in sampled_frames:
                out.write(frame)
            out.release()
            
            # Verify output
            verify_cap = cv2.VideoCapture(str(output_path))
            if verify_cap.isOpened():
                out_frames = int(verify_cap.get(cv2.CAP_PROP_FRAME_COUNT))
                out_width = int(verify_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                out_height = int(verify_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                verify_cap.release()
                
                format_compliant = (out_width == 96 and out_height == 64 and out_frames == 32)
                
                return {
                    "success": True,
                    "method_used": f"geometric_fallback_{format_type}",
                    "input_dimensions": f"{width}×{height}",
                    "output_dimensions": f"{out_width}×{out_height}",
                    "input_frames": len(frames),
                    "output_frames": out_frames,
                    "format_compliant": format_compliant,
                    "roi_coordinates": f"{roi_x},{roi_y} {roi_w}×{roi_h}"
                }
            else:
                return {"success": False, "error": "Could not verify output video"}
                
        except Exception as e:
            logger.error(f"❌ Preprocessing failed for {input_path.name}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def run_inference(self, processed_video_path):
        """Run model inference on processed video."""
        try:
            cmd = ['python', 'lip_reading_inference_final_1025.py', '--video', str(processed_video_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                # Parse the output to extract prediction results
                output_lines = result.stdout.strip().split('\n')

                predicted_word = None
                confidence = None
                probabilities = {}

                for line in output_lines:
                    if "Predicted Word:" in line:
                        predicted_word = line.split("Predicted Word:")[1].strip()
                    elif "Confidence:" in line:
                        confidence = float(line.split("Confidence:")[1].strip())
                    elif line.strip().startswith("  ") and ":" in line:
                        # Parse probability lines (they start with two spaces)
                        parts = line.strip().split(":")
                        if len(parts) == 2:
                            class_name = parts[0].strip()
                            prob_value = float(parts[1].strip())
                            probabilities[class_name] = prob_value

                return {
                    "success": True,
                    "predicted_word": predicted_word,
                    "confidence": confidence,
                    "probabilities": probabilities
                }
            else:
                return {"success": False, "error": f"Inference failed: {result.stderr}"}

        except Exception as e:
            logger.error(f"❌ Inference failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def evaluate_single_video(self, video_info):
        """Evaluate a single video."""
        filename = video_info["filename"]
        ground_truth = video_info["ground_truth"]
        
        logger.info(f"\n🎯 Evaluating: {filename}")
        logger.info(f"📋 Ground Truth: {ground_truth}")
        
        # Get full path
        video_path = self.test_directory / filename
        if not video_path.exists():
            return {
                "filename": filename,
                "ground_truth": ground_truth,
                "status": "failed",
                "error": "Video file not found"
            }
        
        # Get video properties
        properties = self.get_video_properties(video_path)
        if not properties:
            return {
                "filename": filename,
                "ground_truth": ground_truth,
                "status": "failed",
                "error": "Could not get video properties"
            }
        
        # Preprocess video
        processed_filename = f"eval_{Path(filename).stem}.mp4"
        processed_path = self.processed_videos_dir / processed_filename
        
        preprocessing_result = self.preprocess_video(video_path, processed_path)
        if not preprocessing_result["success"]:
            return {
                "filename": filename,
                "ground_truth": ground_truth,
                "status": "failed",
                "error": f"Preprocessing failed: {preprocessing_result['error']}",
                "properties": properties
            }
        
        # Run inference
        inference_result = self.run_inference(processed_path)
        if not inference_result["success"]:
            return {
                "filename": filename,
                "ground_truth": ground_truth,
                "status": "failed",
                "error": f"Inference failed: {inference_result['error']}",
                "properties": properties,
                "preprocessing": preprocessing_result
            }
        
        # Determine correctness
        predicted_word = inference_result["predicted_word"]
        is_correct = predicted_word == ground_truth
        
        # Combine results
        return {
            "filename": filename,
            "ground_truth": ground_truth,
            "predicted_word": predicted_word,
            "confidence": inference_result["confidence"],
            "is_correct": is_correct,
            "status": "success",
            "properties": properties,
            "preprocessing": preprocessing_result,
            "probabilities": inference_result["probabilities"]
        }
    
    def run_evaluation(self):
        """Run comprehensive evaluation on all test videos."""
        logger.info("🎯 COMPREHENSIVE MODEL EVALUATION - best_final_1.10.25.pth")
        logger.info("="*80)
        logger.info(f"📁 Test Directory: {self.test_directory}")
        logger.info(f"🧠 Model Classes: {', '.join(self.model_classes)}")
        logger.info(f"📊 Test Videos: {len(self.test_videos)}")
        logger.info("="*80)
        
        for i, video_info in enumerate(self.test_videos, 1):
            logger.info(f"📹 Evaluating video {i}/{len(self.test_videos)}: {video_info['filename']}")
            result = self.evaluate_single_video(video_info)
            self.results.append(result)
            
            if result["status"] == "success":
                status_icon = "✅" if result["is_correct"] else "❌"
                logger.info(f"{status_icon} {result['predicted_word']} ({result['confidence']:.1%}) - {'CORRECT' if result['is_correct'] else 'WRONG'}")
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
        
        return self.results

def main():
    """Main evaluation function."""
    evaluator = ComprehensiveModelEvaluator()
    results = evaluator.run_evaluation()
    
    # Save results to JSON file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"comprehensive_evaluation_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Comprehensive evaluation complete!")
    
    return results

if __name__ == "__main__":
    main()
