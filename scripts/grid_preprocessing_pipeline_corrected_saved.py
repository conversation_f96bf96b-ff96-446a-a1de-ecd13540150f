#!/usr/bin/env python3
"""
GRID Preprocessing Pipeline - CORRECTED VERSION
===============================================

Production-ready preprocessing pipeline for GRID corpus videos with corrected
mouth region extraction for lip-reading applications.
"""

import os
import sys
import cv2
import numpy as np
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import time
from datetime import datetime
import argparse


class GRIDPreprocessingPipelineCorrected:
    """
    Production-ready GRID preprocessing pipeline with corrected mouth region extraction.
    """

    def __init__(self, target_resolution: Tuple[int, int] = (96, 64), target_frames: int = 32):
        self.TARGET_RESOLUTION = target_resolution  # (width, height)
        self.TARGET_FRAMES = target_frames
        self.TARGET_CHANNELS = 1  # grayscale

        # Face detection setup
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # ROI stabilization
        self.roi_history = []
        self.roi_history_size = 5

        # Quality validation thresholds
        self.MIN_MOUTH_CONTRAST = 8.0
        self.MIN_FACE_DETECTION_RATE = 0.6

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def extract_mouth_roi_corrected(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        if len(faces) == 0:
            return None
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face
        mouth_y_start = fy + int(fh * 0.5)
        mouth_y_end = fy + fh
        mouth_height = mouth_y_end - mouth_y_start
        mouth_width_margin = int(fw * 0.15)
        mouth_x_start = fx + mouth_width_margin
        mouth_x_end = fx + fw - mouth_width_margin
        mouth_width = mouth_x_end - mouth_x_start
        return (mouth_x_start, mouth_y_start, mouth_width, mouth_height)

    def stabilize_roi_sequence(self, roi_sequence: List[Optional[Tuple[int, int, int, int]]]) -> List[Tuple[int, int, int, int]]:
        valid_rois = [roi for roi in roi_sequence if roi is not None]
        if not valid_rois:
            return [(0, 0, 100, 100)] * len(roi_sequence)
        avg_width = int(np.mean([roi[2] for roi in valid_rois]))
        avg_height = int(np.mean([roi[3] for roi in valid_rois]))
        stabilized_rois = []
        smoothed_center_x, smoothed_center_y = None, None
        alpha = 0.3
        for roi in roi_sequence:
            if roi is not None:
                x, y, w, h = roi
                center_x = x + w // 2
                center_y = y + h // 2
                if smoothed_center_x is None:
                    smoothed_center_x, smoothed_center_y = center_x, center_y
                else:
                    smoothed_center_x = alpha * center_x + (1 - alpha) * smoothed_center_x
                    smoothed_center_y = alpha * center_y + (1 - alpha) * smoothed_center_y
            if smoothed_center_x is not None:
                stabilized_x = int(smoothed_center_x - avg_width // 2)
                stabilized_y = int(smoothed_center_y - avg_height // 2)
                stabilized_rois.append((stabilized_x, stabilized_y, avg_width, avg_height))
            else:
                stabilized_rois.append((0, 0, avg_width, avg_height))
        return stabilized_rois

    def apply_geometric_cropping_corrected(self, frame: np.ndarray, roi: Tuple[int, int, int, int]) -> np.ndarray:
        x, y, w, h = roi
        frame_h, frame_w = frame.shape[:2]
        x = max(0, min(x, frame_w - 1))
        y = max(0, min(y, frame_h - 1))
        w = min(w, frame_w - x)
        h = min(h, frame_h - y)
        roi_frame = frame[y:y+h, x:x+w]
        if roi_frame.size == 0:
            return frame
        roi_h, roi_w = roi_frame.shape[:2]
        crop_height = int(roi_h * 0.6)
        crop_y_start = roi_h - crop_height
        crop_width = int(roi_w * 0.5)
        crop_x_start = (roi_w - crop_width) // 2
        cropped = roi_frame[crop_y_start:crop_y_start+crop_height, crop_x_start:crop_x_start+crop_width]
        return cropped if cropped.size > 0 else roi_frame

    def process_video(self, input_path: Path, output_path: Path) -> Dict[str, Any]:
        result = {
            'input_path': str(input_path),
            'output_path': str(output_path),
            'success': False,
            'error': None
        }
        try:
            cap = cv2.VideoCapture(str(input_path))
            if not cap.isOpened():
                result['error'] = f"Cannot open video: {input_path}"
                return result
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            roi_sequence = [self.extract_mouth_roi_corrected(f) for f in frames]
            stabilized_rois = self.stabilize_roi_sequence(roi_sequence)
            processed_frames = []
            for frame, roi in zip(frames, stabilized_rois):
                cropped = self.apply_geometric_cropping_corrected(frame, roi)
                gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                resized = cv2.resize(gray, self.TARGET_RESOLUTION, interpolation=cv2.INTER_LINEAR)
                processed_frames.append(resized)
            if not processed_frames:
                result['error'] = "No frames processed"
                return result
            # Sample / pad to target length
            if len(processed_frames) >= self.TARGET_FRAMES:
                idx = np.linspace(0, len(processed_frames)-1, self.TARGET_FRAMES, dtype=int)
                sampled_frames = [processed_frames[i] for i in idx]
            else:
                sampled_frames = processed_frames * (self.TARGET_FRAMES // len(processed_frames) + 1)
                sampled_frames = sampled_frames[:self.TARGET_FRAMES]
            output_path.parent.mkdir(parents=True, exist_ok=True)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, 30.0, self.TARGET_RESOLUTION, isColor=False)
            for frame in sampled_frames:
                out.write(frame)
            out.release()
            result['success'] = True
        except Exception as e:
            result['error'] = str(e)
        return result

    def batch_process_videos(self, input_dir: Path, output_dir: Path, max_videos: Optional[int] = None) -> Dict[str, Any]:
        video_files = list(input_dir.glob("*.mpg"))
        if max_videos:
            video_files = video_files[:max_videos]
        results = {'total_videos': len(video_files), 'successful': 0, 'failed': 0}
        for vf in video_files:
            output_file = output_dir / f"{vf.stem}.mp4"
            res = self.process_video(vf, output_file)
            if res['success']:
                results['successful'] += 1
            else:
                results['failed'] += 1
        return results


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_dir", type=str, required=True, help="Folder with raw GRID videos (.mpg)")
    parser.add_argument("--output_dir", type=str, required=True, help="Folder to save processed videos")
    parser.add_argument("--max_videos", type=int, default=None, help="Limit number of videos (for testing)")
    args = parser.parse_args()

    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)

    if not input_dir.exists():
        print(f"❌ Input directory not found: {input_dir}")
        sys.exit(1)

    pipeline = GRIDPreprocessingPipelineCorrected()
    results = pipeline.batch_process_videos(input_dir, output_dir, max_videos=args.max_videos)

    print("\n🎯 Batch Results:")
    print(f"   Processed: {results['total_videos']} videos")
    print(f"   Successful: {results['successful']}")
    print(f"   Failed: {results['failed']}")


if __name__ == "__main__":
    main()
