#!/usr/bin/env python3
"""
LOSO Training v6 — Balanced + Train/Val Acc + Per-class reporting
"""

import os, random, time
from pathlib import Path
import numpy as np
import cv2
import torch, torch.nn as nn, torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.utils.data.sampler import WeightedRandomSampler

# ========= CONFIG =========
ROOT_MAIN = "/home/<USER>/SageMaker/classifier_training_29.9.25/speaker_sets"
ROOT_PARTIAL = "/home/<USER>/SageMaker/classifier_training_29.9.25/speaker_sets/partial_speaker_videos"
ENCODER_CHECKPOINT = "/home/<USER>/SageMaker/classifier_training_29.9.25/encoder_grid_5sp_encoder_final.pth"
OUT_DIR = "/home/<USER>/SageMaker/classifier_training_29.9.25/loso_checkpoints_v6"

DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
BATCH_SIZE = 12
N_FRAMES = 32
H, W = 64, 96
EPOCHS_STAGE1 = 5
EPOCHS_STAGE2 = 150
PATIENCE = 10
LR_HEAD = 1e-3
LR_ENCODER = 1e-4
NUM_WORKERS = 4
LABEL_SMOOTH = 0.1
USE_TTA = True

os.makedirs(OUT_DIR, exist_ok=True)

# ========= Helpers =========
def list_speakers():
    sp_main = [d.name for d in Path(ROOT_MAIN).iterdir() if d.is_dir() and d.name.startswith("speaker_")]
    sp_partial = [d.name for d in Path(ROOT_PARTIAL).iterdir() if d.is_dir() and d.name.startswith("speaker_p")]
    return sorted(sp_main + sp_partial)

def list_classes():
    sp_dirs = []
    for root in [ROOT_MAIN, ROOT_PARTIAL]:
        if Path(root).exists():
            sp_dirs += [d for d in Path(root).iterdir() if d.is_dir()]
    classes = sorted({c.name for sp in sp_dirs for c in sp.iterdir() if c.is_dir()})
    return classes

CLASSES = list_classes()
CLASS_TO_IDX = {c: i for i, c in enumerate(CLASSES)}
NUM_CLASSES = len(CLASSES)
print("Classes:", CLASSES)

# ========= Augmentation =========
def augment_frames(frames: np.ndarray):
    if random.random() < 0.5:
        frames = np.flip(frames, axis=2).copy()
    if random.random() < 0.5:
        factor = 0.75 + 0.5 * random.random()
        frames = np.clip(frames * factor, 0, 1)
    return frames

# ========= Video I/O =========
def sample_frames_from_video(path, n_frames=N_FRAMES):
    cap = cv2.VideoCapture(str(path))
    frames = []
    total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total <= 0:
        cap.release(); raise RuntimeError(f"Bad video: {path}")
    if total >= n_frames:
        start = max(0, (total - n_frames) // 2)
        idxs = list(range(start, start + n_frames))
    else:
        idxs = list(range(total)) + [total - 1] * (n_frames - total)
    cur = 0; got = []
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    while True:
        ret, fr = cap.read()
        if not ret: break
        if cur in idxs:
            fr = cv2.cvtColor(fr, cv2.COLOR_BGR2GRAY)
            fr = cv2.resize(fr, (W, H))
            got.append(fr)
        cur += 1
    cap.release()
    while len(got) < n_frames:
        got.append(got[-1].copy())
    return np.stack(got[:n_frames], axis=0).astype(np.float32) / 255.0

# ========= Dataset =========
class VideoFolderDataset(Dataset):
    def __init__(self, roots, speakers, classes, augment=False):
        self.samples = []
        self.augment = augment
        for root in roots:
            for sp in speakers:
                spath = Path(root) / sp
                if not spath.exists(): continue
                for cls in classes:
                    cpath = spath / cls
                    if not cpath.exists(): continue
                    for vid in cpath.glob("*"):
                        if vid.suffix.lower() in [".mp4",".mov",".avi",".webm",".mkv"]:
                            self.samples.append((str(vid), CLASS_TO_IDX[cls]))
        random.shuffle(self.samples)
    def __len__(self): return len(self.samples)
    def __getitem__(self, idx):
        path,label=self.samples[idx]
        frames=sample_frames_from_video(path)
        if self.augment: frames=augment_frames(frames)
        frames=np.expand_dims(frames,1)
        return torch.from_numpy(frames), label

def collate_fn(batch):
    vids=torch.stack([b[0] for b in batch],0).float()
    labels=torch.tensor([b[1] for b in batch],dtype=torch.long)
    return vids,labels

# ========= Model =========
class Encoder(nn.Module):
    def __init__(self, hidden_dim=256):
        super().__init__()
        self.conv=nn.Sequential(
            nn.Conv2d(1,32,3,stride=2,padding=1),nn.ReLU(),
            nn.Conv2d(32,64,3,stride=2,padding=1),nn.ReLU(),
            nn.Conv2d(64,128,3,stride=2,padding=1),nn.ReLU()
        )
        self.flatten_dim=128*8*12
        self.gru=nn.GRU(self.flatten_dim,hidden_dim,batch_first=True,bidirectional=True)
        self.out_dim=hidden_dim*2
    def forward(self,x):
        B,T,C,H,W=x.shape
        x=x.view(B*T,C,H,W)
        x=self.conv(x)
        x=x.view(B,T,-1)
        out,_=self.gru(x)
        return out

class AttentivePooling(nn.Module):
    def __init__(self,d_in,d_att=128):
        super().__init__()
        self.mlp=nn.Sequential(nn.Linear(d_in,d_att),nn.Tanh(),nn.Linear(d_att,1))
    def forward(self,x):
        e=self.mlp(x).squeeze(-1)
        w=torch.softmax(e,dim=1)
        pooled=(x*w.unsqueeze(-1)).sum(dim=1)
        return pooled,w

class ClassifierHead(nn.Module):
    def __init__(self,encoder_out_dim=512,gru_hidden=256,num_layers=2,dropout=0.3,num_classes=NUM_CLASSES):
        super().__init__()
        self.gru=nn.GRU(encoder_out_dim,gru_hidden,batch_first=True,
                        bidirectional=True,num_layers=num_layers,dropout=dropout)
        self.pool=AttentivePooling(gru_hidden*2)
        self.dropout=nn.Dropout(dropout)
        self.fc=nn.Linear(gru_hidden*2,num_classes)
    def forward(self,enc_feats):
        out,_=self.gru(enc_feats)
        pooled,_=self.pool(out)
        return self.fc(self.dropout(pooled))

class FullModel(nn.Module):
    def __init__(self,encoder,head):
        super().__init__()
        self.encoder=encoder
        self.head=head
    def forward(self,x):
        return self.head(self.encoder(x))

# ========= Train / Eval =========
def train_one_epoch(model, loader, criterion, opt, device):
    model.train(); loss_sum=0; tot=0; correct=0
    for vids,labels in loader:
        vids,labels=vids.to(device),labels.to(device)
        opt.zero_grad()
        logits=model(vids)
        loss=criterion(logits,labels)
        loss.backward(); opt.step()
        loss_sum+=loss.item()*vids.size(0); tot+=vids.size(0)
        correct+=(logits.argmax(1)==labels).sum().item()
    return loss_sum/max(1,tot), correct/max(1,tot)

@torch.no_grad()
def eval_model(model, loader, criterion, device):
    model.eval(); loss_sum=0; tot=0; correct=0
    class_correct=np.zeros(NUM_CLASSES); class_tot=np.zeros(NUM_CLASSES)
    for vids,labels in loader:
        vids,labels=vids.to(device),labels.to(device)
        logits=model(vids)
        loss=criterion(logits,labels)
        loss_sum+=loss.item()*vids.size(0); tot+=vids.size(0)
        preds=logits.argmax(1)
        correct+=(preds==labels).sum().item()
        for l,p in zip(labels.cpu(),preds.cpu()):
            class_tot[l]+=1
            if l==p: class_correct[l]+=1
    class_acc={CLASSES[i]:(class_correct[i]/class_tot[i] if class_tot[i]>0 else 0.0) for i in range(NUM_CLASSES)}
    return loss_sum/max(1,tot), correct/max(1,tot), class_acc

# ========= Main =========
def main():
    speakers=list_speakers()
    print("Speakers:",speakers)
    best_overall=0.0; results=[]
    roots=[ROOT_MAIN,ROOT_PARTIAL]

    for fold,val_sp in enumerate(speakers,1):
        train_speakers=[s for s in speakers if s!=val_sp]
        print(f"\n=== Fold {fold}/{len(speakers)} — val speaker: {val_sp} ===")

        train_ds=VideoFolderDataset(roots,train_speakers,CLASSES,augment=True)
        val_ds=VideoFolderDataset(roots,[val_sp],CLASSES,augment=False)

        train_loader=DataLoader(train_ds,batch_size=BATCH_SIZE,shuffle=True,
                                num_workers=NUM_WORKERS,collate_fn=collate_fn)
        val_loader=DataLoader(val_ds,batch_size=BATCH_SIZE,shuffle=False,
                              num_workers=NUM_WORKERS,collate_fn=collate_fn)

        encoder=Encoder()
        ck=torch.load(ENCODER_CHECKPOINT,map_location="cpu")
        encoder.load_state_dict(ck,strict=False)
        head=ClassifierHead(encoder_out_dim=encoder.out_dim)
        model=FullModel(encoder,head).to(DEVICE)
        print("✅ Loaded encoder")

        criterion=nn.CrossEntropyLoss(label_smoothing=LABEL_SMOOTH)

        # Stage 1
        for p in model.encoder.parameters(): p.requires_grad=False
        opt=optim.Adam(model.head.parameters(),lr=LR_HEAD)
        for ep in range(1,EPOCHS_STAGE1+1):
            tr_loss,tr_acc=train_one_epoch(model,train_loader,criterion,opt,DEVICE)
            vl_loss,val_acc,val_class_acc=eval_model(model,val_loader,criterion,DEVICE)
            print(f"[Fold{fold} S1 {ep}/{EPOCHS_STAGE1}] train_loss:{tr_loss:.4f} train_acc:{tr_acc:.4f} "
                  f"val_loss:{vl_loss:.4f} val_acc:{val_acc:.4f}")
            print("   Per-class val acc:",val_class_acc)

        # Stage 2
        for p in model.encoder.parameters(): p.requires_grad=True
        opt=optim.Adam([
            {"params":model.encoder.parameters(),"lr":LR_ENCODER},
            {"params":model.head.parameters(),"lr":LR_HEAD}
        ])
        best_val=0.0; patience=PATIENCE; best_state=None
        for ep in range(1,EPOCHS_STAGE2+1):
            tr_loss,tr_acc=train_one_epoch(model,train_loader,criterion,opt,DEVICE)
            vl_loss,val_acc,val_class_acc=eval_model(model,val_loader,criterion,DEVICE)
            print(f"[Fold{fold} S2 {ep}/{EPOCHS_STAGE2}] train_loss:{tr_loss:.4f} train_acc:{tr_acc:.4f} "
                  f"val_loss:{vl_loss:.4f} val_acc:{val_acc:.4f}")
            print("   Per-class val acc:",val_class_acc)

            if val_acc>best_val:
                best_val=val_acc; patience=PATIENCE
                best_state=model.state_dict()
                torch.save(best_state,f"{OUT_DIR}/best_fold{fold}.pth")
                print(f"  ✅ New best fold {fold}: {val_acc:.4f}")
                if val_acc>best_overall:
                    best_overall=val_acc
                    torch.save(best_state,f"{OUT_DIR}/best_overall.pth")
                    print(f"  🌟 Updated best overall: {val_acc:.4f}")
            else:
                patience-=1
                if patience==0:
                    print("  ⏹ Early stopping"); break

        results.append(best_val)
        print(f"Fold {fold} best acc: {best_val:.4f}")

    print("\n=== LOSO Summary ===")
    for i,acc in enumerate(results,1): print(f"Fold {i}: {acc:.4f}")
    print("Mean acc:",float(np.mean(results)))
    print("🌟 Best overall acc:",best_overall)

if __name__=="__main__":
    main()
