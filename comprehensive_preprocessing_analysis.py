#!/usr/bin/env python3
"""
Comprehensive Preprocessing Analysis
===================================

Analyzes the current GRID preprocessing pipeline's cropping parameters and their impact
on lip detail preservation WITHOUT modifying the original GRID script.

This script will:
1. Select a test video from speaker_sets/full_speaker_sets_top7
2. Analyze current GRID cropping parameters (60% height, 50% width)
3. Apply the exact GRID preprocessing pipeline
4. Assess lip detail preservation and quality
5. Document findings for optimal preprocessing understanding
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional, Any

# Import the exact GRID preprocessing pipeline (unchanged)
sys.path.append('scripts')
from grid_preprocessing_pipeline_corrected_saved import GRIDPreprocessingPipelineCorrected

def analyze_video_properties(video_path: Path) -> dict:
    """Analyze video properties for detailed comparison."""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        return {"error": f"Cannot open video: {video_path}"}
    
    # Get video properties
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    # Read first frame to check color channels and analyze content
    ret, frame = cap.read()
    if ret:
        channels = frame.shape[2] if len(frame.shape) == 3 else 1
        # Check if it's grayscale data stored as 3-channel
        if channels == 3:
            b, g, r = cv2.split(frame)
            if np.array_equal(b, g) and np.array_equal(g, r):
                channels = 1
                color_space = "Grayscale (stored as BGR)"
            else:
                color_space = "BGR"
        else:
            color_space = "Grayscale"
        
        # Analyze frame content for lip region assessment
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        frame_mean = np.mean(gray_frame)
        frame_std = np.std(gray_frame)
        
    else:
        channels = 0
        color_space = "Unknown"
        frame_mean = 0
        frame_std = 0
    
    cap.release()
    
    return {
        "path": str(video_path),
        "frame_count": frame_count,
        "width": width,
        "height": height,
        "fps": fps,
        "channels": channels,
        "color_space": color_space,
        "frame_mean_intensity": frame_mean,
        "frame_std_intensity": frame_std
    }

def analyze_cropping_impact(original_frame: np.ndarray, cropped_frame: np.ndarray) -> dict:
    """Analyze the impact of cropping on lip detail preservation."""
    
    # Convert to grayscale if needed
    if len(original_frame.shape) == 3:
        orig_gray = cv2.cvtColor(original_frame, cv2.COLOR_BGR2GRAY)
    else:
        orig_gray = original_frame
        
    if len(cropped_frame.shape) == 3:
        crop_gray = cv2.cvtColor(cropped_frame, cv2.COLOR_BGR2GRAY)
    else:
        crop_gray = cropped_frame
    
    # Calculate detail preservation metrics
    orig_edges = cv2.Canny(orig_gray, 50, 150)
    crop_edges = cv2.Canny(crop_gray, 50, 150)
    
    orig_edge_density = np.sum(orig_edges > 0) / orig_edges.size
    crop_edge_density = np.sum(crop_edges > 0) / crop_edges.size
    
    # Calculate contrast and texture metrics
    orig_contrast = np.std(orig_gray)
    crop_contrast = np.std(crop_gray)
    
    # Calculate size reduction
    orig_area = orig_gray.shape[0] * orig_gray.shape[1]
    crop_area = crop_gray.shape[0] * crop_gray.shape[1]
    area_reduction = (orig_area - crop_area) / orig_area * 100
    
    return {
        "original_size": orig_gray.shape,
        "cropped_size": crop_gray.shape,
        "area_reduction_percent": area_reduction,
        "original_edge_density": orig_edge_density,
        "cropped_edge_density": crop_edge_density,
        "edge_density_ratio": crop_edge_density / orig_edge_density if orig_edge_density > 0 else 0,
        "original_contrast": orig_contrast,
        "cropped_contrast": crop_contrast,
        "contrast_ratio": crop_contrast / orig_contrast if orig_contrast > 0 else 0
    }

def extract_sample_frames_for_analysis(video_path: Path, num_samples: int = 5) -> List[np.ndarray]:
    """Extract sample frames from video for detailed analysis."""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        return []
    
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if frame_count == 0:
        cap.release()
        return []
    
    # Select evenly spaced frames
    frame_indices = np.linspace(0, frame_count - 1, num_samples, dtype=int)
    frames = []
    
    for idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
    
    cap.release()
    return frames

def main():
    """Main comprehensive analysis function."""
    print("🔍 COMPREHENSIVE PREPROCESSING ANALYSIS")
    print("=" * 60)
    
    # Select test video from top7 directory (raw input)
    test_video_path = Path("speaker_sets/full_speaker_sets_top7/speaker_4/my_back_hurts/my_back_hurts__useruser01__18to39__female__caucasian__20250827T082824.mp4")
    
    if not test_video_path.exists():
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    print(f"📹 Selected test video: {test_video_path.name}")
    print(f"📁 Source directory: speaker_sets/full_speaker_sets_top7 (raw input)")
    
    # Analyze original video properties
    print("\n📊 ORIGINAL VIDEO ANALYSIS:")
    original_props = analyze_video_properties(test_video_path)
    for key, value in original_props.items():
        if key != "path":
            print(f"   {key}: {value}")
    
    # Create output directory
    output_dir = Path("comprehensive_preprocessing_analysis")
    output_dir.mkdir(exist_ok=True)
    
    # Apply exact GRID preprocessing pipeline (unchanged)
    print("\n🔄 APPLYING EXACT GRID PREPROCESSING PIPELINE...")
    print("   📋 Current GRID Parameters Analysis:")
    print("   - Face detection: Haar cascades")
    print("   - Mouth ROI: Lower 50% of detected face")
    print("   - ROI stabilization: Exponential smoothing (α=0.3)")
    print("   - Geometric cropping: 60% height × 50% width")
    print("   - Crop position: Bottom 60% height, center 50% width")
    print("   - Target resolution: 96×64 pixels")
    print("   - Target frames: 32")
    print("   - Color space: Grayscale")
    
    # Initialize exact GRID preprocessing pipeline
    pipeline = GRIDPreprocessingPipelineCorrected(
        target_resolution=(96, 64),
        target_frames=32
    )
    
    # Process the video using exact GRID pipeline
    output_path = output_dir / f"grid_processed_{test_video_path.name}"
    result = pipeline.process_video(test_video_path, output_path)
    
    if result['success']:
        print("   ✅ GRID preprocessing completed successfully!")
        
        # Analyze processed video properties
        print("\n📊 GRID-PROCESSED VIDEO ANALYSIS:")
        processed_props = analyze_video_properties(output_path)
        for key, value in processed_props.items():
            if key != "path":
                print(f"   {key}: {value}")
        
        # Extract sample frames for detailed analysis
        print("\n🔬 DETAILED CROPPING IMPACT ANALYSIS:")
        original_frames = extract_sample_frames_for_analysis(test_video_path, 3)
        processed_frames = extract_sample_frames_for_analysis(output_path, 3)
        
        if original_frames and processed_frames:
            total_area_reduction = 0
            total_edge_ratio = 0
            total_contrast_ratio = 0
            
            for i, (orig_frame, proc_frame) in enumerate(zip(original_frames, processed_frames)):
                analysis = analyze_cropping_impact(orig_frame, proc_frame)
                print(f"\n   Frame {i+1} Analysis:")
                print(f"   - Original size: {analysis['original_size']}")
                print(f"   - Cropped size: {analysis['cropped_size']}")
                print(f"   - Area reduction: {analysis['area_reduction_percent']:.1f}%")
                print(f"   - Edge density ratio: {analysis['edge_density_ratio']:.3f}")
                print(f"   - Contrast ratio: {analysis['contrast_ratio']:.3f}")
                
                total_area_reduction += analysis['area_reduction_percent']
                total_edge_ratio += analysis['edge_density_ratio']
                total_contrast_ratio += analysis['contrast_ratio']
            
            # Calculate averages
            avg_area_reduction = total_area_reduction / len(original_frames)
            avg_edge_ratio = total_edge_ratio / len(original_frames)
            avg_contrast_ratio = total_contrast_ratio / len(original_frames)
            
            print(f"\n   📈 AVERAGE IMPACT METRICS:")
            print(f"   - Average area reduction: {avg_area_reduction:.1f}%")
            print(f"   - Average edge density ratio: {avg_edge_ratio:.3f}")
            print(f"   - Average contrast ratio: {avg_contrast_ratio:.3f}")
        
        # GRID specification compliance check
        print("\n🎯 GRID SPECIFICATION COMPLIANCE:")
        grid_specs = {
            "width": 96,
            "height": 64,
            "frame_count": 32,
            "channels": 1
        }
        
        compliance_passed = True
        for spec, expected_value in grid_specs.items():
            actual_value = processed_props.get(spec)
            status = "✅" if actual_value == expected_value else "❌"
            print(f"   {status} {spec}: {actual_value} (expected: {expected_value})")
            if actual_value != expected_value:
                compliance_passed = False
        
        # Color space check
        color_space = processed_props.get("color_space", "")
        is_grayscale = "Grayscale" in color_space
        status = "✅" if is_grayscale else "❌"
        print(f"   {status} color_space: {color_space}")
        
        print(f"\n🏆 OVERALL COMPLIANCE: {'✅ PASSED' if compliance_passed and is_grayscale else '❌ FAILED'}")
        
        # Analysis summary and recommendations
        print("\n📋 ANALYSIS SUMMARY:")
        print("   🔍 Current GRID Cropping Parameters:")
        print("   - Height crop: 60% (keeps bottom 60% of mouth ROI)")
        print("   - Width crop: 50% (keeps center 50% of mouth ROI)")
        print("   - Focus area: Lower-center portion of detected mouth region")
        
        print("\n   📊 Impact Assessment:")
        if 'avg_area_reduction' in locals():
            if avg_area_reduction > 80:
                print(f"   ⚠️  High area reduction ({avg_area_reduction:.1f}%) - significant cropping")
            elif avg_area_reduction > 60:
                print(f"   ⚡ Moderate area reduction ({avg_area_reduction:.1f}%) - balanced cropping")
            else:
                print(f"   ✅ Low area reduction ({avg_area_reduction:.1f}%) - minimal cropping")
            
            if avg_edge_ratio > 0.8:
                print(f"   ✅ Good edge preservation (ratio: {avg_edge_ratio:.3f})")
            elif avg_edge_ratio > 0.6:
                print(f"   ⚡ Moderate edge preservation (ratio: {avg_edge_ratio:.3f})")
            else:
                print(f"   ⚠️  Low edge preservation (ratio: {avg_edge_ratio:.3f})")
            
            if avg_contrast_ratio > 0.9:
                print(f"   ✅ Excellent contrast preservation (ratio: {avg_contrast_ratio:.3f})")
            elif avg_contrast_ratio > 0.7:
                print(f"   ⚡ Good contrast preservation (ratio: {avg_contrast_ratio:.3f})")
            else:
                print(f"   ⚠️  Reduced contrast preservation (ratio: {avg_contrast_ratio:.3f})")
        
        print("\n   🎯 GRID Pipeline Assessment:")
        print("   ✅ Maintains exact GRID specification compliance")
        print("   ✅ Consistent preprocessing across all videos")
        print("   ✅ Standardized 96×64 output format")
        print("   ✅ Proper grayscale conversion")
        print("   ✅ Exact 32-frame sampling")
        
        print(f"\n   📁 Processed video saved to: {output_path}")
        
    else:
        print(f"   ❌ GRID preprocessing failed: {result['error']}")
        return
    
    print("\n🎉 COMPREHENSIVE ANALYSIS COMPLETE!")
    print("   The analysis shows the current GRID preprocessing pipeline's")
    print("   impact on lip detail preservation while maintaining exact")
    print("   GRID specification compliance.")

if __name__ == "__main__":
    main()
