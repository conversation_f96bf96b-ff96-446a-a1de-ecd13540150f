#!/usr/bin/env python3
"""
Comprehensive Model Evaluation Script
====================================

Evaluates the best_lipreader.pth model on test videos from the data directory.
Includes preprocessing, inference, and detailed analysis.
"""

import json
import torch
import cv2
import numpy as np
from pathlib import Path
import logging
import sys
import os
from typing import List, Dict, Any
import matplotlib.pyplot as plt
import seaborn as sns

# Add current directory to path for imports
sys.path.append('.')

# Import our preprocessing and inference modules
from robust_lip_preprocessing_pipeline import RobustLipPreprocessor
from lip_reading_inference import LipReadingInference

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model classes
MODEL_CLASSES = [
    'doctor', 'my_back_hurts', 'my_mouth_is_dry', 
    'phone', 'pillow', 'water', 'where_does_it_hurt'
]

class ModelEvaluator:
    """Comprehensive model evaluation pipeline."""
    
    def __init__(self, model_path: str = 'models/best_lipreader.pth'):
        self.model_path = model_path
        self.preprocessor = RobustLipPreprocessor()
        self.inference = LipReadingInference(model_path)
        self.model_classes = MODEL_CLASSES
        
        # Create output directories
        self.processed_dir = Path('data/model_evaluation_processed')
        self.results_dir = Path('model_evaluation_results')
        self.processed_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info("✅ Model evaluator initialized")
    
    def preprocess_test_videos(self, test_videos: List[Dict]) -> List[Dict]:
        """Preprocess test videos using our robust pipeline."""
        logger.info("🔄 Preprocessing test videos...")
        
        processed_videos = []
        
        for video_info in test_videos:
            test_path = Path(video_info['test_path'])
            
            # Create processed filename
            processed_filename = f"processed_{test_path.stem}.mp4"
            processed_path = self.processed_dir / processed_filename
            
            logger.info(f"Processing: {test_path.name}")
            
            # Process video
            result = self.preprocessor.process_video(test_path, processed_path)
            
            # Update video info
            video_info_copy = video_info.copy()
            video_info_copy['processed_path'] = str(processed_path)
            video_info_copy['preprocessing_success'] = result['success']
            video_info_copy['preprocessing_method'] = result.get('method_used', 'unknown')
            
            if result['success']:
                logger.info(f"✅ Processed successfully: {processed_path.name}")
            else:
                logger.error(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            
            processed_videos.append(video_info_copy)
        
        return processed_videos
    
    def run_inference(self, processed_videos: List[Dict]) -> List[Dict]:
        """Run inference on processed videos."""
        logger.info("🧠 Running model inference...")
        
        inference_results = []
        
        for video_info in processed_videos:
            if not video_info['preprocessing_success']:
                # Skip failed preprocessing
                video_info['inference_success'] = False
                video_info['inference_error'] = 'Preprocessing failed'
                inference_results.append(video_info)
                continue
            
            processed_path = Path(video_info['processed_path'])
            logger.info(f"Inference: {processed_path.name}")
            
            # Run inference with attention weights
            result = self.inference.predict(processed_path, return_attention=True)
            
            # Merge results
            video_info_merged = {**video_info, **result}
            inference_results.append(video_info_merged)
            
            if result['success']:
                predicted = result['predicted_class']
                confidence = result['confidence']
                expected = video_info['expected_class']
                
                # Check if prediction matches expected (accounting for class mismatch)
                is_correct = self.evaluate_prediction(expected, predicted)
                video_info_merged['is_correct'] = is_correct
                video_info_merged['is_in_model_vocab'] = expected in self.model_classes
                
                status = "✅ CORRECT" if is_correct else "❌ INCORRECT"
                logger.info(f"  Expected: {expected:15} | Predicted: {predicted:15} | Confidence: {confidence:.3f} | {status}")
            else:
                logger.error(f"  Inference failed: {result.get('error', 'Unknown error')}")
                video_info_merged['is_correct'] = False
                video_info_merged['is_in_model_vocab'] = False
        
        return inference_results
    
    def evaluate_prediction(self, expected: str, predicted: str) -> bool:
        """Evaluate if prediction is correct, handling vocabulary mismatches."""
        # Direct match
        if expected == predicted:
            return True
        
        # Handle known mappings for out-of-vocabulary classes
        mappings = {
            'glasses': ['doctor', 'phone'],  # Might be confused with these
            'help': ['doctor', 'phone'],     # Might be confused with these
            'i_need_to_move': ['my_back_hurts', 'where_does_it_hurt']  # Might map to medical terms
        }
        
        # For out-of-vocab classes, we can't really say it's "correct"
        # but we can note reasonable confusions
        return False  # Conservative: only exact matches are correct
    
    def analyze_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Analyze inference results and generate statistics."""
        logger.info("📊 Analyzing results...")
        
        # Basic statistics
        total_videos = len(results)
        successful_preprocessing = sum(1 for r in results if r['preprocessing_success'])
        successful_inference = sum(1 for r in results if r.get('success', False))
        
        # Accuracy analysis
        in_vocab_results = [r for r in results if r.get('is_in_model_vocab', False) and r.get('success', False)]
        out_vocab_results = [r for r in results if not r.get('is_in_model_vocab', False) and r.get('success', False)]
        
        in_vocab_correct = sum(1 for r in in_vocab_results if r.get('is_correct', False))
        in_vocab_accuracy = in_vocab_correct / len(in_vocab_results) if in_vocab_results else 0
        
        # Confidence analysis
        successful_results = [r for r in results if r.get('success', False)]
        confidences = [r['confidence'] for r in successful_results]
        
        # Prediction distribution
        predictions = [r['predicted_class'] for r in successful_results]
        prediction_counts = {cls: predictions.count(cls) for cls in self.model_classes}
        
        # Expected vs predicted analysis
        confusion_data = []
        for r in successful_results:
            confusion_data.append({
                'expected': r['expected_class'],
                'predicted': r['predicted_class'],
                'confidence': r['confidence'],
                'is_in_vocab': r.get('is_in_model_vocab', False),
                'is_correct': r.get('is_correct', False)
            })
        
        analysis = {
            'summary': {
                'total_videos': total_videos,
                'successful_preprocessing': successful_preprocessing,
                'successful_inference': successful_inference,
                'preprocessing_success_rate': successful_preprocessing / total_videos,
                'inference_success_rate': successful_inference / total_videos if successful_preprocessing > 0 else 0
            },
            'accuracy': {
                'in_vocab_videos': len(in_vocab_results),
                'in_vocab_correct': in_vocab_correct,
                'in_vocab_accuracy': in_vocab_accuracy,
                'out_vocab_videos': len(out_vocab_results)
            },
            'confidence': {
                'mean_confidence': np.mean(confidences) if confidences else 0,
                'std_confidence': np.std(confidences) if confidences else 0,
                'min_confidence': np.min(confidences) if confidences else 0,
                'max_confidence': np.max(confidences) if confidences else 0,
                'confidence_distribution': confidences
            },
            'predictions': {
                'prediction_counts': prediction_counts,
                'most_predicted_class': max(prediction_counts.items(), key=lambda x: x[1])[0] if prediction_counts else None
            },
            'confusion_data': confusion_data,
            'detailed_results': results
        }
        
        return analysis
    
    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a comprehensive evaluation report."""
        report = []
        
        report.append("# 🎯 LIP READING MODEL EVALUATION REPORT")
        report.append("=" * 60)
        report.append(f"**Model**: {self.model_path}")
        from datetime import datetime
        report.append(f"**Evaluation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary
        summary = analysis['summary']
        report.append("## 📊 SUMMARY STATISTICS")
        report.append(f"- **Total Test Videos**: {summary['total_videos']}")
        report.append(f"- **Preprocessing Success**: {summary['successful_preprocessing']}/{summary['total_videos']} ({summary['preprocessing_success_rate']:.1%})")
        report.append(f"- **Inference Success**: {summary['successful_inference']}/{summary['total_videos']} ({summary['inference_success_rate']:.1%})")
        report.append("")
        
        # Accuracy
        accuracy = analysis['accuracy']
        report.append("## 🎯 ACCURACY ANALYSIS")
        report.append(f"- **In-Vocabulary Videos**: {accuracy['in_vocab_videos']}")
        report.append(f"- **In-Vocabulary Correct**: {accuracy['in_vocab_correct']}/{accuracy['in_vocab_videos']}")
        report.append(f"- **In-Vocabulary Accuracy**: {accuracy['in_vocab_accuracy']:.1%}")
        report.append(f"- **Out-of-Vocabulary Videos**: {accuracy['out_vocab_videos']}")
        report.append("")
        
        # Confidence
        confidence = analysis['confidence']
        report.append("## 📈 CONFIDENCE ANALYSIS")
        report.append(f"- **Mean Confidence**: {confidence['mean_confidence']:.3f}")
        report.append(f"- **Std Confidence**: {confidence['std_confidence']:.3f}")
        report.append(f"- **Confidence Range**: {confidence['min_confidence']:.3f} - {confidence['max_confidence']:.3f}")
        report.append("")
        
        # Predictions
        predictions = analysis['predictions']
        report.append("## 🔮 PREDICTION DISTRIBUTION")
        for cls, count in sorted(predictions['prediction_counts'].items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                report.append(f"- **{cls}**: {count} predictions")
        report.append("")
        
        # Detailed results
        report.append("## 📋 DETAILED RESULTS")
        report.append("| Test ID | Expected Class | Predicted Class | Confidence | In Vocab | Correct | Category |")
        report.append("|---------|----------------|-----------------|------------|----------|---------|----------|")
        
        for r in analysis['detailed_results']:
            if r.get('success', False):
                test_id = r.get('test_id', 'N/A')
                expected = r['expected_class']
                predicted = r['predicted_class']
                confidence = r['confidence']
                in_vocab = "✅" if r.get('is_in_model_vocab', False) else "❌"
                correct = "✅" if r.get('is_correct', False) else "❌"
                category = r['category']
                
                report.append(f"| {test_id} | {expected} | {predicted} | {confidence:.3f} | {in_vocab} | {correct} | {category} |")
        
        return "\n".join(report)
    
    def save_results(self, analysis: Dict[str, Any], report: str):
        """Save evaluation results and report."""
        # Save detailed results as JSON
        results_file = self.results_dir / 'evaluation_results.json'
        with open(results_file, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        # Save report as markdown
        report_file = self.results_dir / 'evaluation_report.md'
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"💾 Results saved to: {results_file}")
        logger.info(f"📄 Report saved to: {report_file}")

def main():
    """Main evaluation pipeline."""
    
    # Load test video selection
    selection_file = Path('test_video_selection.json')
    if not selection_file.exists():
        logger.error(f"Test video selection file not found: {selection_file}")
        logger.info("Please run test_video_selection.py first")
        return
    
    with open(selection_file, 'r') as f:
        test_videos = json.load(f)
    
    logger.info(f"🎬 Loaded {len(test_videos)} test videos for evaluation")
    
    # Initialize evaluator
    evaluator = ModelEvaluator()
    
    # Run evaluation pipeline
    try:
        # Step 1: Preprocess videos
        processed_videos = evaluator.preprocess_test_videos(test_videos)
        
        # Step 2: Run inference
        inference_results = evaluator.run_inference(processed_videos)
        
        # Step 3: Analyze results
        analysis = evaluator.analyze_results(inference_results)
        
        # Step 4: Generate report
        report = evaluator.generate_report(analysis)
        
        # Step 5: Save results
        evaluator.save_results(analysis, report)
        
        # Print summary
        print("\n" + "=" * 60)
        print("🎉 EVALUATION COMPLETE!")
        print("=" * 60)
        print(f"📊 Total Videos: {analysis['summary']['total_videos']}")
        print(f"✅ Successful: {analysis['summary']['successful_inference']}")
        print(f"🎯 In-Vocab Accuracy: {analysis['accuracy']['in_vocab_accuracy']:.1%}")
        print(f"📈 Mean Confidence: {analysis['confidence']['mean_confidence']:.3f}")
        print(f"📁 Results: {evaluator.results_dir}")
        
    except Exception as e:
        logger.error(f"❌ Evaluation failed: {e}")
        raise

if __name__ == "__main__":
    main()
