# Comprehensive Evaluation Report: best_final_1.10.25.pth Model

**Date:** October 1, 2025  
**Model:** best_final_1.10.25.pth  
**Architecture:** CNN + Bidirectional GRU + Attention (32.9M parameters)  
**Test Videos:** 8 videos with clear ground truth labels  

---

## Executive Summary

The `best_final_1.10.25.pth` model achieved **37.5% accuracy** (3/8 correct predictions) on the comprehensive test set, representing a **significant improvement** over previous models:

- **Original model (best_lipreader.pth):** 0% accuracy (0/9 videos)
- **Second model (best_final.pth):** 16.7% accuracy (1/6 videos) 
- **Current model (best_final_1.10.25.pth):** 37.5% accuracy (3/8 videos)

However, the model exhibits a **severe "doctor" bias**, predicting "doctor" for 75% of all test videos (6/8), including cases where the ground truth was completely different classes.

---

## Test Summary

- **Total Videos Tested:** 8
- **Preprocessing Success Rate:** 100% (8/8 videos)
- **Inference Success Rate:** 100% (8/8 videos)
- **Format Compliance:** 100% (all videos processed to 96×64, 32 frames, grayscale, MP4)
- **Overall Accuracy:** 37.5% (3/8 correct predictions)

---

## Detailed Results Table

| Video | Ground Truth | Predicted | Confidence | Correct | Notes |
|-------|-------------|-----------|------------|---------|-------|
| doctor 20.mp4 | doctor | **doctor** | 80.1% | ✅ | High confidence, correct |
| doctor__useruser01__40to64__female__caucasian__20250903T020751.mp4 | doctor | **doctor** | 78.8% | ✅ | High confidence, correct |
| glasses 12.mp4 | glasses | **doctor** | 81.3% | ❌ | High confidence, wrong class |
| i_need_to_move__useruser01__65plus__female__caucasian__20250716T055222.mp4 | i_need_to_move | **doctor** | 56.7% | ❌ | Moderate confidence, wrong class |
| i_need_to_move__useruser01__65plus__female__caucasian__20250827T054813.mp4 | i_need_to_move | **doctor** | 87.7% | ❌ | Very high confidence, wrong class |
| my_mouth_is_dry__useruser01__65plus__female__caucasian__20250723T073831.mp4 | my_mouth_is_dry | **my_mouth_is_dry** | 32.7% | ✅ | Low confidence, correct |
| phone__useruser01__18to39__female__caucasian__20250822T084158.mp4 | phone | **doctor** | 74.0% | ❌ | High confidence, wrong class |
| pillow 11.mp4 | pillow | **doctor** | 66.9% | ❌ | High confidence, wrong class |

---

## Performance Metrics

### Overall Performance
- **Accuracy:** 37.5% (3/8)
- **Correct Predictions:** 3
- **Incorrect Predictions:** 5

### Per-Class Performance
- **doctor:** 2/2 correct (100% recall, but 2/6 precision = 33.3%)
- **glasses:** 0/1 correct (0% recall)
- **i_need_to_move:** 0/2 correct (0% recall)
- **my_mouth_is_dry:** 1/1 correct (100% recall)
- **phone:** 0/1 correct (0% recall)
- **pillow:** 0/1 correct (0% recall)
- **my_back_hurts:** Not tested (no test videos available)

### Confidence Analysis
- **Average Confidence:** 68.8%
- **Confidence Range:** 32.7% - 87.7%
- **High Confidence Errors:** 4/5 incorrect predictions had >65% confidence
- **Dangerous Overconfidence:** Model shows 87.7% confidence in wrong "doctor" prediction

---

## Critical Issues Identified

### 1. Severe "Doctor" Bias
- **75% of predictions are "doctor"** (6/8 videos)
- Model predicts "doctor" even for completely unrelated classes (glasses, phone, pillow)
- This represents a **catastrophic overfitting** to the "doctor" class

### 2. Dangerous Overconfidence
- **87.7% confidence** in wrong "doctor" prediction for "i_need_to_move"
- **81.3% confidence** in wrong "doctor" prediction for "glasses"
- High confidence in wrong predictions makes the model unreliable for real-world use

### 3. Poor Generalization
- Only correctly identifies 3 out of 7 classes tested
- Fails completely on: glasses, i_need_to_move, phone, pillow
- Shows inability to distinguish between visually distinct lip movements

---

## Preprocessing Analysis

### Success Metrics
- **100% preprocessing success rate** (8/8 videos)
- **100% format compliance** (all outputs: 96×64, 32 frames, grayscale)
- **Robust geometric fallback** handled all video formats successfully

### Video Format Distribution
- **Landscape videos:** 5 (1280×720 and 400×200)
- **Portrait videos:** 0
- **ROI extraction:** Consistent geometric fallback method used

---

## Comparison with Previous Models

| Model | Accuracy | Key Issues | Improvement |
|-------|----------|------------|-------------|
| best_lipreader.pth | 0% (0/9) | Complete failure, wrong predictions | Baseline |
| best_final.pth | 16.7% (1/6) | Severe "glasses" bias (78% predictions) | +16.7% |
| **best_final_1.10.25.pth** | **37.5% (3/8)** | **Severe "doctor" bias (75% predictions)** | **+20.8%** |

### Progress Analysis
- **Positive:** Steady improvement in accuracy across model versions
- **Negative:** Each model develops severe bias toward different classes
- **Pattern:** Models appear to overfit to dominant classes in training data

---

## Technical Observations

### Model Architecture
- **Parameters:** 32.9M (same as best_final.pth)
- **Architecture:** CNN + Bidirectional GRU + Attention
- **Classes:** 7 medical/healthcare terms
- **Input Format:** [1, 32, 1, 64, 96] tensor

### Inference Performance
- **Processing Speed:** ~2-3 seconds per video
- **Memory Usage:** Efficient on MPS (Apple Silicon)
- **Stability:** No crashes or errors during evaluation

---

## Recommendations

### Immediate Actions Required
1. **Address "Doctor" Bias:** Investigate training data distribution and class balancing
2. **Confidence Calibration:** Implement confidence calibration to reduce overconfident wrong predictions
3. **Data Augmentation:** Add more diverse examples for underperforming classes

### Model Improvements
1. **Class Balancing:** Ensure equal representation of all 7 classes in training data
2. **Regularization:** Add stronger regularization to prevent overfitting to dominant classes
3. **Cross-Validation:** Implement proper cross-validation to detect bias during training

### Evaluation Improvements
1. **Larger Test Set:** Expand test set to include more examples per class
2. **Confusion Matrix:** Generate detailed confusion matrix for better error analysis
3. **Probability Distribution Analysis:** Examine full probability distributions for insights

---

## Conclusion

The `best_final_1.10.25.pth` model shows **meaningful progress** with 37.5% accuracy, representing the best performance achieved so far. However, the **severe "doctor" bias** and **dangerous overconfidence** in wrong predictions make it unsuitable for production use.

**Key Takeaway:** While accuracy has improved, the fundamental issue of class bias persists across all model versions, suggesting a systematic problem in the training methodology that needs to be addressed before further model development.

**Next Steps:** Focus on training data analysis and class balancing before developing the next model iteration.
