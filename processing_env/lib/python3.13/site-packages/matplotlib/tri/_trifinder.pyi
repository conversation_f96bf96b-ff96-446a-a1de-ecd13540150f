from matplotlib.tri import Triangulation
from numpy.typing import ArrayLike

class TriFinder:
    def __init__(self, triangulation: Triangulation) -> None: ...
    def __call__(self, x: ArrayLike, y: ArrayLike) -> ArrayLike: ...

class TrapezoidMapTriFinder(TriFinder):
    def __init__(self, triangulation: Triangulation) -> None: ...
    def __call__(self, x: Array<PERSON>ike, y: Array<PERSON>ike) -> ArrayLike: ...
