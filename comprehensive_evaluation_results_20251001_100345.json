[{"filename": "doctor 20.mp4", "ground_truth": "doctor", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_doctor 20.mp4'", "properties": {"width": 1280, "height": 720, "nb_frames": 46, "duration": 1.534867, "fps": 29.97002997002997, "size_bytes": 2656537}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "1280×720", "output_dimensions": "96×64", "input_frames": 46, "output_frames": 32, "format_compliant": true, "roi_coordinates": "256,216 768×360"}}, {"filename": "doctor__useruser01__40to64__female__caucasian__20250903T020751.mp4", "ground_truth": "doctor", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_doctor__useruser01__40to64__female__caucasian__20250903T020751.mp4'", "properties": {"width": 400, "height": 200, "nb_frames": 109, "duration": 3.599, "fps": 2000.0, "size_bytes": 31951}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "400×200", "output_dimensions": "96×64", "input_frames": 109, "output_frames": 32, "format_compliant": true, "roi_coordinates": "80,60 240×100"}}, {"filename": "glasses 12.mp4", "ground_truth": "glasses", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_glasses 12.mp4'", "properties": {"width": 1280, "height": 720, "nb_frames": 77, "duration": 2.569233, "fps": 29.97002997002997, "size_bytes": 3846439}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "1280×720", "output_dimensions": "96×64", "input_frames": 77, "output_frames": 32, "format_compliant": true, "roi_coordinates": "256,216 768×360"}}, {"filename": "i_need_to_move__useruser01__65plus__female__caucasian__20250716T055222.mp4", "ground_truth": "i_need_to_move", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_i_need_to_move__useruser01__65plus__female__caucasian__20250716T055222.mp4'", "properties": {"width": 400, "height": 200, "nb_frames": 44, "duration": 1.466667, "fps": 30.0, "size_bytes": 30862}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "400×200", "output_dimensions": "96×64", "input_frames": 44, "output_frames": 32, "format_compliant": true, "roi_coordinates": "80,60 240×100"}}, {"filename": "i_need_to_move__useruser01__65plus__female__caucasian__20250827T054813.mp4", "ground_truth": "i_need_to_move", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_i_need_to_move__useruser01__65plus__female__caucasian__20250827T054813.mp4'", "properties": {"width": 400, "height": 200, "nb_frames": 147, "duration": 4.916016, "fps": 60.0, "size_bytes": 84501}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "400×200", "output_dimensions": "96×64", "input_frames": 147, "output_frames": 32, "format_compliant": true, "roi_coordinates": "80,60 240×100"}}, {"filename": "my_mouth_is_dry__useruser01__65plus__female__caucasian__20250723T073831.mp4", "ground_truth": "my_mouth_is_dry", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_my_mouth_is_dry__useruser01__65plus__female__caucasian__20250723T073831.mp4'", "properties": {"width": 400, "height": 200, "nb_frames": 90, "duration": 2.948, "fps": 2000.0, "size_bytes": 77978}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "400×200", "output_dimensions": "96×64", "input_frames": 90, "output_frames": 32, "format_compliant": true, "roi_coordinates": "80,60 240×100"}}, {"filename": "phone__useruser01__18to39__female__caucasian__20250822T084158.mp4", "ground_truth": "phone", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_phone__useruser01__18to39__female__caucasian__20250822T084158.mp4'", "properties": {"width": 400, "height": 200, "nb_frames": 62, "duration": 2.099, "fps": 2000.0, "size_bytes": 28541}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "400×200", "output_dimensions": "96×64", "input_frames": 62, "output_frames": 32, "format_compliant": true, "roi_coordinates": "80,60 240×100"}}, {"filename": "pillow 11.mp4", "ground_truth": "pillow", "status": "failed", "error": "Inference failed: could not convert string to float: 'evaluation_processed_videos/eval_pillow 11.mp4'", "properties": {"width": 1280, "height": 720, "nb_frames": 53, "duration": 1.768433, "fps": 29.97002997002997, "size_bytes": 600758}, "preprocessing": {"success": true, "method_used": "geometric_fallback_landscape", "input_dimensions": "1280×720", "output_dimensions": "96×64", "input_frames": 53, "output_frames": 32, "format_compliant": true, "roi_coordinates": "256,216 768×360"}}]