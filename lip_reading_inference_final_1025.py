#!/usr/bin/env python3
"""
Lip Reading Inference Script for best_final_1.10.25.pth Model
=============================================================

Comprehensive evaluation script for the latest lip reading model.

Model Specifications:
- Architecture: CNN + Bidirectional GRU + Attention
- Parameters: 32.9M
- Classes: ["doctor", "glasses", "i_need_to_move", "my_back_hurts", "my_mouth_is_dry", "phone", "pillow"]
- Input format: [1, 32, 1, 64, 96] tensor format

Usage:
    python lip_reading_inference_final_1025.py --video path/to/video.mp4
"""

import torch
import torch.nn as nn
import cv2
import numpy as np
import argparse
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AttentionPooling(nn.Module):
    """Attention pooling layer for temporal aggregation."""
    def __init__(self, input_dim):
        super(AttentionPooling, self).__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
    
    def forward(self, x):
        # x: [batch, seq_len, features]
        attention_weights = torch.softmax(self.mlp(x), dim=1)  # [batch, seq_len, 1]
        attended = torch.sum(attention_weights * x, dim=1)  # [batch, features]
        return attended, attention_weights

class LipReadingModelFinal1025(nn.Module):
    """
    Lip Reading Model for best_final_1.10.25.pth
    - 3-layer CNN encoder (1→48→96→160)
    - Bidirectional GRU encoder (320 hidden)
    - 2-layer Bidirectional GRU head (256 hidden)
    - Attention pooling
    - 7-class output
    """
    def __init__(self, num_classes=7):
        super(LipReadingModelFinal1025, self).__init__()
        
        # CNN Encoder with exact structure matching checkpoint indices
        conv_layers = nn.ModuleList()
        conv_layers.append(nn.Conv2d(1, 48, kernel_size=3, padding=1))  # 0
        conv_layers.append(nn.BatchNorm2d(48))  # 1
        conv_layers.append(nn.ReLU())  # 2 (no params)
        conv_layers.append(nn.Conv2d(48, 96, kernel_size=3, padding=1))  # 3
        conv_layers.append(nn.BatchNorm2d(96))  # 4
        conv_layers.append(nn.ReLU())  # 5 (no params)
        conv_layers.append(nn.Conv2d(96, 160, kernel_size=3, padding=1))  # 6
        conv_layers.append(nn.BatchNorm2d(160))  # 7
        conv_layers.append(nn.ReLU())  # 8 (no params)
        
        self.encoder = nn.ModuleDict({
            'conv': conv_layers,
            'gru': nn.GRU(
                input_size=15360,  # 160 * 12 * 8
                hidden_size=320,
                num_layers=1,
                batch_first=True,
                bidirectional=True
            )
        })
        
        # Head with 2-layer Bidirectional GRU
        self.head = nn.ModuleDict({
            'gru': nn.GRU(
                input_size=640,  # 320 * 2 (bidirectional)
                hidden_size=256,
                num_layers=2,
                batch_first=True,
                bidirectional=True
            ),
            'pool': AttentionPooling(512),  # 256 * 2 (bidirectional)
            'fc': nn.Linear(512, num_classes)
        })
        
    def forward(self, x):
        # x: [batch, seq_len, channels, height, width]
        batch_size, seq_len = x.size(0), x.size(1)
        
        # Reshape for CNN: [batch*seq_len, channels, height, width]
        x = x.view(batch_size * seq_len, x.size(2), x.size(3), x.size(4))
        
        # CNN encoding with manual pooling
        x = self.encoder['conv'][0](x)  # Conv2d
        x = self.encoder['conv'][1](x)  # BatchNorm2d
        x = self.encoder['conv'][2](x)  # ReLU
        x = nn.functional.max_pool2d(x, 2, 2)  # MaxPool2d
        
        x = self.encoder['conv'][3](x)  # Conv2d
        x = self.encoder['conv'][4](x)  # BatchNorm2d
        x = self.encoder['conv'][5](x)  # ReLU
        x = nn.functional.max_pool2d(x, 2, 2)  # MaxPool2d
        
        x = self.encoder['conv'][6](x)  # Conv2d
        x = self.encoder['conv'][7](x)  # BatchNorm2d
        x = self.encoder['conv'][8](x)  # ReLU
        x = nn.functional.max_pool2d(x, 2, 2)  # MaxPool2d  # [batch*seq_len, 160, 12, 8]
        
        # Flatten CNN output
        x = x.view(batch_size * seq_len, -1)  # [batch*seq_len, 15360]
        
        # Reshape back to sequence: [batch, seq_len, features]
        x = x.view(batch_size, seq_len, -1)
        
        # Encoder GRU
        x, _ = self.encoder['gru'](x)  # [batch, seq_len, 640]
        
        # Head GRU
        x, _ = self.head['gru'](x)  # [batch, seq_len, 512]
        
        # Attention pooling
        x, attention_weights = self.head['pool'](x)  # [batch, 512]
        
        # Final classification
        x = self.head['fc'](x)  # [batch, num_classes]
        
        return x, attention_weights

class LipReadingInferenceFinal1025:
    """Inference class for the best_final_1.10.25.pth model."""
    
    def __init__(self, model_path, device='auto'):
        self.device = self._get_device(device)
        self.model = self._load_model(model_path)
        
        # Exact class names for the new model (7 classes)
        self.class_names = [
            'doctor', 'glasses', 'i_need_to_move', 'my_back_hurts', 
            'my_mouth_is_dry', 'phone', 'pillow'
        ]
        
        logger.info(f"✅ Lip reading model loaded on {self.device}")
        logger.info(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        logger.info(f"🎯 Model classes: {', '.join(self.class_names)}")
    
    def _get_device(self, device):
        """Determine the best available device."""
        if device == 'auto':
            if torch.cuda.is_available():
                return torch.device('cuda')
            elif torch.backends.mps.is_available():
                return torch.device('mps')
            else:
                return torch.device('cpu')
        return torch.device(device)
    
    def _load_model(self, model_path):
        """Load the pre-trained model."""
        model = LipReadingModelFinal1025(num_classes=7)
        
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint)
            model.eval()
            model.to(self.device)
            return model
        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            raise
    
    def preprocess_video(self, video_path):
        """Load and preprocess video for model input."""
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to grayscale if needed
            if len(frame.shape) == 3:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            frames.append(frame)
        
        cap.release()
        
        if len(frames) == 0:
            raise ValueError("No frames found in video")
        
        # Verify frame dimensions
        if frames[0].shape != (64, 96):
            raise ValueError(f"Expected frame size (64, 96), got {frames[0].shape}")
        
        # Ensure exactly 32 frames
        if len(frames) != 32:
            raise ValueError(f"Expected 32 frames, got {len(frames)}")
        
        # Convert to tensor: [1, 32, 1, 64, 96]
        video_tensor = torch.FloatTensor(np.array(frames)).unsqueeze(0).unsqueeze(2)
        video_tensor = video_tensor.to(self.device)
        
        return video_tensor
    
    def predict_single(self, video_path):
        """Run inference on a single video."""
        logger.info(f"🎬 Processing single video: {video_path}")
        
        # Preprocess video
        video_tensor = self.preprocess_video(video_path)
        
        # Run inference
        with torch.no_grad():
            logits, attention_weights = self.model(video_tensor)
            probabilities = torch.softmax(logits, dim=1)
        
        # Get predictions
        predicted_idx = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0, predicted_idx].item()
        predicted_word = self.class_names[predicted_idx]
        
        # Get all probabilities
        all_probs = {}
        for i, class_name in enumerate(self.class_names):
            all_probs[class_name] = probabilities[0, i].item()
        
        return {
            'predicted_word': predicted_word,
            'confidence': confidence,
            'probabilities': all_probs,
            'attention_weights': attention_weights.cpu().numpy()
        }

def main():
    parser = argparse.ArgumentParser(description='Lip Reading Inference - Final 1.10.25 Model')
    parser.add_argument('--model', default='models/best_final_1.10.25.pth', 
                       help='Path to model file')
    parser.add_argument('--video', help='Path to single video file')
    parser.add_argument('--device', default='auto', 
                       help='Device to use (auto, cpu, cuda, mps)')
    
    args = parser.parse_args()
    
    if not args.video:
        parser.error("Please provide --video argument")
    
    # Initialize inference
    inference = LipReadingInferenceFinal1025(args.model, args.device)
    
    # Run prediction
    try:
        result = inference.predict_single(args.video)
        
        print(f"\n🎯 PREDICTION RESULTS:")
        print(f"Video: {args.video}")
        print(f"Predicted Word: {result['predicted_word']}")
        print(f"Confidence: {result['confidence']:.3f}")
        print(f"\nAll Probabilities:")
        for class_name, prob in result['probabilities'].items():
            print(f"  {class_name}: {prob:.3f}")
        
    except Exception as e:
        logger.error(f"❌ Prediction failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
