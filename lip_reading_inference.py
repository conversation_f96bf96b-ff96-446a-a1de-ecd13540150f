#!/usr/bin/env python3
"""
Lip Reading Inference Script
===========================

Demonstrates how to use the best_final.pth model
with videos processed by our robust lip preprocessing pipeline.

Usage:
    python lip_reading_inference.py --video path/to/video.mp4
    python lip_reading_inference.py --batch --input_dir processed_videos/
"""

import torch
import torch.nn as nn
import cv2
import numpy as np
import argparse
from pathlib import Path
import logging
from typing import List, Tuple, Dict, Optional
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Class labels for the 7-class medical vocabulary
CLASSES = [
    'doctor',
    'my_back_hurts', 
    'my_mouth_is_dry',
    'phone',
    'pillow', 
    'water',
    'where_does_it_hurt'
]

class Encoder(nn.Module):
    """Video encoder with CNN + GRU architecture."""
    
    def __init__(self, hidden_dim=256):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(1, 32, 3, stride=2, padding=1), nn.ReLU(),
            nn.Conv2d(32, 64, 3, stride=2, padding=1), nn.ReLU(),
            nn.Conv2d(64, 128, 3, stride=2, padding=1), nn.ReLU()
        )
        self.flatten_dim = 128 * 8 * 12  # 12288
        self.gru = nn.GRU(self.flatten_dim, hidden_dim, batch_first=True, bidirectional=True)
        self.out_dim = hidden_dim * 2  # 512
    
    def forward(self, x):
        B, T, C, H, W = x.shape
        x = x.view(B * T, C, H, W)
        x = self.conv(x)
        x = x.view(B, T, -1)
        out, _ = self.gru(x)
        return out

class AttentivePooling(nn.Module):
    """Attention-based pooling layer."""
    
    def __init__(self, d_in, d_att=128):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(d_in, d_att),
            nn.Tanh(),
            nn.Linear(d_att, 1)
        )
    
    def forward(self, x):
        e = self.mlp(x).squeeze(-1)
        w = torch.softmax(e, dim=1)
        pooled = (x * w.unsqueeze(-1)).sum(dim=1)
        return pooled, w

class ClassifierHead(nn.Module):
    """Classification head with GRU + attention pooling."""
    
    def __init__(self, encoder_out_dim=512, gru_hidden=256, num_layers=2, 
                 dropout=0.3, num_classes=7):
        super().__init__()
        self.gru = nn.GRU(encoder_out_dim, gru_hidden, batch_first=True,
                         bidirectional=True, num_layers=num_layers, dropout=dropout)
        self.pool = AttentivePooling(gru_hidden * 2)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(gru_hidden * 2, num_classes)
    
    def forward(self, enc_feats):
        out, _ = self.gru(enc_feats)
        pooled, attention_weights = self.pool(out)
        return self.fc(self.dropout(pooled)), attention_weights

class LipReadingModel(nn.Module):
    """Complete lip reading model."""
    
    def __init__(self, num_classes=7):
        super().__init__()
        self.encoder = Encoder(hidden_dim=256)
        self.head = ClassifierHead(encoder_out_dim=512, num_classes=num_classes)
    
    def forward(self, x, return_attention=False):
        enc_feats = self.encoder(x)
        logits, attention_weights = self.head(enc_feats)
        
        if return_attention:
            return logits, attention_weights
        return logits

class LipReadingInference:
    """Lip reading inference pipeline."""
    
    def __init__(self, model_path: str, device: str = 'auto'):
        self.device = self._get_device(device)
        self.model = self._load_model(model_path)
        self.classes = CLASSES
        
        logger.info(f"✅ Lip reading model loaded on {self.device}")
        logger.info(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def _get_device(self, device: str) -> torch.device:
        """Determine the best device to use."""
        if device == 'auto':
            if torch.cuda.is_available():
                return torch.device('cuda')
            elif torch.backends.mps.is_available():
                return torch.device('mps')
            else:
                return torch.device('cpu')
        return torch.device(device)
    
    def _load_model(self, model_path: str) -> LipReadingModel:
        """Load the trained model."""
        model = LipReadingModel(num_classes=len(CLASSES))
        
        # Load weights
        checkpoint = torch.load(model_path, map_location=self.device)
        model.load_state_dict(checkpoint)
        
        model.to(self.device)
        model.eval()
        
        return model
    
    def load_video_tensor(self, video_path: Path) -> torch.Tensor:
        """
        Load video and convert to tensor format expected by model.
        Expected format: [1, 32, 1, 64, 96] (Batch, Time, Channels, Height, Width)
        """
        cap = cv2.VideoCapture(str(video_path))
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to grayscale if needed
            if len(frame.shape) == 3:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            frames.append(frame)
        
        cap.release()
        
        if len(frames) == 0:
            raise ValueError(f"No frames found in video: {video_path}")
        
        # Convert to numpy array and normalize
        video_array = np.array(frames, dtype=np.float32) / 255.0
        
        # Ensure exactly 32 frames
        if len(video_array) != 32:
            logger.warning(f"Video has {len(video_array)} frames, expected 32. Resampling...")
            indices = np.linspace(0, len(video_array) - 1, 32, dtype=int)
            video_array = video_array[indices]
        
        # Convert to tensor: [T, H, W] -> [1, T, 1, H, W]
        video_tensor = torch.from_numpy(video_array).unsqueeze(0).unsqueeze(2)
        
        # Verify dimensions
        expected_shape = (1, 32, 1, 64, 96)
        if video_tensor.shape != expected_shape:
            logger.warning(f"Video shape {video_tensor.shape} != expected {expected_shape}")
            # Resize if needed
            if video_tensor.shape[-2:] != (64, 96):
                video_tensor = torch.nn.functional.interpolate(
                    video_tensor.view(-1, 1, *video_tensor.shape[-2:]),
                    size=(64, 96), mode='bilinear', align_corners=False
                ).view(1, 32, 1, 64, 96)
        
        return video_tensor.to(self.device)
    
    def predict(self, video_path: Path, return_attention: bool = False) -> Dict:
        """
        Predict the spoken word from a video.
        
        Args:
            video_path: Path to the video file
            return_attention: Whether to return attention weights
            
        Returns:
            Dictionary with prediction results
        """
        try:
            # Load video
            video_tensor = self.load_video_tensor(video_path)
            
            # Inference
            with torch.no_grad():
                if return_attention:
                    logits, attention_weights = self.model(video_tensor, return_attention=True)
                else:
                    logits = self.model(video_tensor)
                    attention_weights = None
                
                # Get predictions
                probabilities = torch.softmax(logits, dim=1)
                predicted_class = torch.argmax(logits, dim=1)
                confidence = probabilities[0][predicted_class].item()
                
                # Prepare results
                result = {
                    'video_path': str(video_path),
                    'predicted_class': self.classes[predicted_class.item()],
                    'confidence': confidence,
                    'all_probabilities': {
                        self.classes[i]: prob.item() 
                        for i, prob in enumerate(probabilities[0])
                    },
                    'success': True,
                    'error': None
                }
                
                if return_attention and attention_weights is not None:
                    result['attention_weights'] = attention_weights.cpu().numpy().tolist()
                
                return result
                
        except Exception as e:
            logger.error(f"Error processing {video_path}: {e}")
            return {
                'video_path': str(video_path),
                'predicted_class': None,
                'confidence': 0.0,
                'all_probabilities': {},
                'success': False,
                'error': str(e)
            }
    
    def predict_batch(self, video_paths: List[Path], return_attention: bool = False) -> List[Dict]:
        """Predict on multiple videos."""
        results = []
        
        for i, video_path in enumerate(video_paths, 1):
            logger.info(f"Processing video {i}/{len(video_paths)}: {video_path.name}")
            result = self.predict(video_path, return_attention)
            results.append(result)
            
            if result['success']:
                logger.info(f"✅ Predicted: {result['predicted_class']} (confidence: {result['confidence']:.3f})")
            else:
                logger.error(f"❌ Failed: {result['error']}")
        
        return results

def main():
    parser = argparse.ArgumentParser(description='Lip Reading Inference')
    parser.add_argument('--model', default='models/best_final.pth',
                       help='Path to model file')
    parser.add_argument('--video', help='Path to single video file')
    parser.add_argument('--batch', action='store_true', 
                       help='Process multiple videos')
    parser.add_argument('--input_dir', default='processed_videos/robust_lip_pipeline',
                       help='Directory containing videos for batch processing')
    parser.add_argument('--output', help='Output JSON file for results')
    parser.add_argument('--device', default='auto', 
                       choices=['auto', 'cpu', 'cuda', 'mps'],
                       help='Device to use for inference')
    parser.add_argument('--attention', action='store_true',
                       help='Return attention weights')
    parser.add_argument('--limit', type=int, help='Limit number of videos to process')
    
    args = parser.parse_args()
    
    # Initialize inference pipeline
    inference = LipReadingInference(args.model, args.device)
    
    if args.video:
        # Single video inference
        video_path = Path(args.video)
        if not video_path.exists():
            logger.error(f"Video file not found: {video_path}")
            return
        
        logger.info(f"🎬 Processing single video: {video_path}")
        result = inference.predict(video_path, args.attention)
        
        if result['success']:
            print(f"\n🎯 PREDICTION RESULTS:")
            print(f"Video: {result['video_path']}")
            print(f"Predicted Word: {result['predicted_class']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print(f"\nAll Probabilities:")
            for word, prob in result['all_probabilities'].items():
                print(f"  {word}: {prob:.3f}")
        else:
            print(f"❌ Prediction failed: {result['error']}")
    
    elif args.batch:
        # Batch processing
        input_dir = Path(args.input_dir)
        if not input_dir.exists():
            logger.error(f"Input directory not found: {input_dir}")
            return
        
        # Find all video files
        video_files = []
        for ext in ['.mp4', '.avi', '.mov']:
            video_files.extend(input_dir.rglob(f'*{ext}'))
        
        if args.limit:
            video_files = video_files[:args.limit]
        
        logger.info(f"🎬 Found {len(video_files)} videos for batch processing")
        
        if len(video_files) == 0:
            logger.error("No video files found")
            return
        
        # Process all videos
        results = inference.predict_batch(video_files, args.attention)
        
        # Summary statistics
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"\n📊 BATCH PROCESSING SUMMARY:")
        print(f"Total videos: {len(results)}")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
        
        if successful:
            avg_confidence = np.mean([r['confidence'] for r in successful])
            print(f"Average confidence: {avg_confidence:.3f}")
            
            # Class distribution
            predictions = [r['predicted_class'] for r in successful]
            class_counts = {cls: predictions.count(cls) for cls in CLASSES}
            print(f"\nPredicted class distribution:")
            for cls, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
                if count > 0:
                    print(f"  {cls}: {count} videos")
        
        # Save results if requested
        if args.output:
            output_path = Path(args.output)
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"💾 Results saved to: {output_path}")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
