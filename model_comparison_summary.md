# R(2+1)D-18 Model Performance Comparison Summary

## Quick Comparison Table

| Metric | 7-Class Model | 6-Class Model | 5-Class Model |
|--------|---------------|---------------|---------------|
| **Classes Available** | 7 | 6 | 5 |
| **Excluded Classes** | None | 'doctor' | 'doctor', 'glasses' |
| **Test Videos** | 10 | 7 | 5 |
| **Excluded Videos** | 0 | 3 | 5 |
| **Ground Truth Classes** | 5 | 5 | 4 |
| **Dominant Prediction** | 'doctor' (90%) | 'glasses' (100%) | 'my_back_hurts' (100%) |
| **Accuracy** | 20% (2/10) | 28.6% (2/7) | 0% (0/5) |
| **Classes Actually Predicted** | 2/7 | 1/6 | 1/5 |
| **Confidence Range** | 51.4% - 93.0% | 87.1% - 97.4% | 82.1% - 96.6% |
| **Processing Time/Video** | 0.53s | 0.48s | 0.47s |

## Bias Pattern Evolution

### 7-Class → 6-Class → 5-Class
```
'doctor' dominance (90%) → 'glasses' dominance (100%) → 'my_back_hurts' dominance (100%)
```

### Performance Degradation
```
20% accuracy → 28.6% accuracy → 0% accuracy
2 classes predicted → 1 class predicted → 1 class predicted
```

## Key Insights

1. **Bias Transfer**: Removing dominant classes doesn't improve discrimination - bias simply transfers to the next class
2. **Progressive Degradation**: Performance gets worse as we remove classes, not better
3. **Complete Collapse**: Model always collapses to single-class prediction with high confidence
4. **Systematic Failure**: Consistent pattern indicates fundamental model deficiencies

## Conclusion

The R(2+1)D-18 model shows **systematic and worsening bias patterns** that make it completely unsuitable for lip reading applications. The model has not learned meaningful lip movement features and instead relies on superficial visual patterns that lead to consistent misclassification.

**Recommendation**: Abandon current approach and redesign from scratch with proper data collection, architecture selection, and training methodology.
