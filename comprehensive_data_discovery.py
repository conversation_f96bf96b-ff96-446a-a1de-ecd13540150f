#!/usr/bin/env python3
"""
Comprehensive Data Discovery Script
==================================

Systematically searches ALL subdirectories in data/ for videos matching 
the model's exact 7-class medical/healthcare vocabulary.
"""

import os
import re
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model's exact training vocabulary (7 classes)
MODEL_CLASSES = [
    'doctor',
    'my_back_hurts', 
    'my_mouth_is_dry',
    'phone',
    'pillow',
    'water',
    'where_does_it_hurt'
]

class DataDiscovery:
    """Comprehensive data discovery for model vocabulary matching."""
    
    def __init__(self, data_root: str = 'data'):
        self.data_root = Path(data_root)
        self.model_classes = MODEL_CLASSES
        self.discovered_videos = []
        self.class_variations = self._build_class_variations()
        
    def _build_class_variations(self) -> Dict[str, List[str]]:
        """Build variations of class names for flexible matching."""
        variations = {
            'doctor': ['doctor', 'dr'],
            'my_back_hurts': [
                'my_back_hurts', 'my back hurts', 'mybackhurts',
                'back_hurts', 'back hurts', 'backhurts'
            ],
            'my_mouth_is_dry': [
                'my_mouth_is_dry', 'my mouth is dry', 'mymouthisdry',
                'mouth_is_dry', 'mouth is dry', 'mouthisdry',
                'dry_mouth', 'dry mouth', 'drymouth'
            ],
            'phone': ['phone', 'telephone', 'call'],
            'pillow': ['pillow', 'cushion'],
            'water': ['water', 'h2o', 'drink'],
            'where_does_it_hurt': [
                'where_does_it_hurt', 'where does it hurt', 'wheredoesithurt',
                'where_hurt', 'where hurt', 'wherehurt',
                'does_it_hurt', 'does it hurt', 'doesithurt'
            ]
        }
        return variations
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        # Convert to lowercase, replace special chars with underscores
        normalized = re.sub(r'[^a-zA-Z0-9]', '_', text.lower())
        # Remove multiple underscores
        normalized = re.sub(r'_+', '_', normalized)
        # Remove leading/trailing underscores
        normalized = normalized.strip('_')
        return normalized
    
    def _match_class(self, filename: str, directory_path: str) -> Optional[str]:
        """
        Match filename/directory to model class using flexible matching.
        Prioritizes longer, more specific matches to avoid false positives.

        Args:
            filename: Video filename
            directory_path: Full directory path

        Returns:
            Matched model class or None
        """
        # Combine filename and directory for context
        full_context = f"{directory_path} {filename}".lower()
        normalized_context = self._normalize_text(full_context)

        # Sort classes by specificity (longer phrases first) to avoid false matches
        class_specificity = []
        for model_class in self.model_classes:
            for variation in self.class_variations[model_class]:
                normalized_variation = self._normalize_text(variation)
                if normalized_variation in normalized_context:
                    class_specificity.append((model_class, len(normalized_variation), variation))

        # Return the most specific match (longest variation)
        if class_specificity:
            # Sort by length (descending) to get most specific match
            class_specificity.sort(key=lambda x: x[1], reverse=True)
            return class_specificity[0][0]

        return None
    
    def _get_file_info(self, video_path: Path) -> Dict:
        """Get detailed file information."""
        try:
            stat = video_path.stat()
            return {
                'size_bytes': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'exists': True
            }
        except Exception as e:
            return {
                'size_bytes': 0,
                'size_mb': 0.0,
                'exists': False,
                'error': str(e)
            }
    
    def discover_videos(self) -> List[Dict]:
        """
        Systematically discover all videos matching model vocabulary.
        
        Returns:
            List of discovered video information
        """
        logger.info(f"🔍 Starting comprehensive data discovery in: {self.data_root}")
        logger.info(f"🎯 Target classes: {', '.join(self.model_classes)}")
        
        discovered = []
        total_files_scanned = 0
        
        # Walk through all subdirectories
        for root, dirs, files in os.walk(self.data_root):
            root_path = Path(root)
            
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                total_files_scanned += 1
                
                # Only process video files
                if not file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    continue
                
                video_path = root_path / file
                relative_path = video_path.relative_to(self.data_root)
                
                # Try to match to model class
                matched_class = self._match_class(file, str(root_path))
                
                if matched_class:
                    file_info = self._get_file_info(video_path)
                    
                    video_info = {
                        'video_path': str(video_path),
                        'relative_path': str(relative_path),
                        'filename': file,
                        'directory': str(root_path),
                        'source_directory': root_path.name,
                        'matched_class': matched_class,
                        'file_info': file_info
                    }
                    
                    discovered.append(video_info)
                    logger.info(f"✅ Found: {matched_class:15} | {relative_path}")
        
        logger.info(f"📊 Discovery complete:")
        logger.info(f"   Total files scanned: {total_files_scanned}")
        logger.info(f"   Videos found: {len(discovered)}")
        
        # Sort by class and then by path
        discovered.sort(key=lambda x: (x['matched_class'], x['relative_path']))
        
        return discovered
    
    def generate_inventory_report(self, discovered_videos: List[Dict]) -> str:
        """Generate comprehensive inventory report."""
        
        # Class distribution
        class_counts = {}
        for video in discovered_videos:
            cls = video['matched_class']
            class_counts[cls] = class_counts.get(cls, 0) + 1
        
        # Source directory distribution
        source_counts = {}
        for video in discovered_videos:
            source = video['source_directory']
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # Generate report
        report = []
        report.append("# 📋 COMPREHENSIVE VIDEO INVENTORY REPORT")
        report.append("=" * 60)
        from datetime import datetime
        report.append(f"**Discovery Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"**Total Videos Found**: {len(discovered_videos)}")
        report.append("")
        
        # Class distribution
        report.append("## 🎯 CLASS DISTRIBUTION")
        for cls in self.model_classes:
            count = class_counts.get(cls, 0)
            status = "✅" if count > 0 else "❌"
            report.append(f"- **{cls}**: {count} videos {status}")
        report.append("")
        
        # Source distribution
        report.append("## 📁 SOURCE DIRECTORY DISTRIBUTION")
        for source, count in sorted(source_counts.items(), key=lambda x: x[1], reverse=True):
            report.append(f"- **{source}**: {count} videos")
        report.append("")
        
        # Detailed inventory
        report.append("## 📋 DETAILED INVENTORY")
        report.append("| Class | Filename | Source Directory | Size (MB) | Relative Path |")
        report.append("|-------|----------|------------------|-----------|---------------|")
        
        for video in discovered_videos:
            cls = video['matched_class']
            filename = video['filename']
            source = video['source_directory']
            size_mb = video['file_info']['size_mb']
            rel_path = video['relative_path']
            
            # Truncate long paths for readability
            if len(rel_path) > 50:
                rel_path = "..." + rel_path[-47:]
            
            report.append(f"| {cls} | {filename} | {source} | {size_mb} | {rel_path} |")
        
        return "\n".join(report)
    
    def save_results(self, discovered_videos: List[Dict], report: str):
        """Save discovery results."""
        
        # Save detailed JSON
        results_file = Path('data_discovery_results.json')
        with open(results_file, 'w') as f:
            json.dump({
                'discovery_summary': {
                    'total_videos': len(discovered_videos),
                    'classes_found': len(set(v['matched_class'] for v in discovered_videos)),
                    'model_classes': self.model_classes
                },
                'discovered_videos': discovered_videos
            }, f, indent=2)
        
        # Save inventory report
        report_file = Path('data_inventory_report.md')
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"💾 Results saved:")
        logger.info(f"   JSON: {results_file}")
        logger.info(f"   Report: {report_file}")

def main():
    """Main discovery pipeline."""
    
    # Initialize discovery
    discovery = DataDiscovery()
    
    # Discover videos
    discovered_videos = discovery.discover_videos()
    
    if not discovered_videos:
        logger.error("❌ No videos found matching model vocabulary!")
        return
    
    # Generate report
    report = discovery.generate_inventory_report(discovered_videos)
    
    # Save results
    discovery.save_results(discovered_videos, report)
    
    # Print summary
    class_counts = {}
    for video in discovered_videos:
        cls = video['matched_class']
        class_counts[cls] = class_counts.get(cls, 0) + 1
    
    print("\n" + "=" * 60)
    print("🎉 DATA DISCOVERY COMPLETE!")
    print("=" * 60)
    print(f"📊 Total Videos Found: {len(discovered_videos)}")
    print(f"🎯 Classes Covered: {len(class_counts)}/7")
    print("\n📋 Class Distribution:")
    for cls in discovery.model_classes:
        count = class_counts.get(cls, 0)
        status = "✅" if count > 0 else "❌"
        print(f"   {cls:20}: {count:2d} videos {status}")
    
    print(f"\n📁 Results saved to data_discovery_results.json")
    print(f"📄 Report saved to data_inventory_report.md")

if __name__ == "__main__":
    main()
