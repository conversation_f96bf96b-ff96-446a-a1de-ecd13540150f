# R(2+1)D-18 Model Analysis: 6-Class Performance (Excluding 'Doctor')

## Executive Summary

After excluding the dominant 'doctor' class from both the model architecture and test dataset, the R(2+1)D-18 model reveals an even more severe bias pattern, now predicting 'glasses' for **100% of all test videos** across 5 different ground truth classes.

## Methodology

### Model Adaptation
- **Original Model**: 7-class R(2+1)D-18 trained on ['doctor', 'glasses', 'help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']
- **Adapted Model**: 6-class version excluding 'doctor' class
- **Final Layer Adaptation**: Removed first row/element from FC layer weights and bias (corresponding to 'doctor' class)
- **New Classes**: ['glasses', 'help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']

### Test Dataset
- **Total Videos Found**: 10 (original)
- **Excluded Videos**: 3 (all 'doctor' class videos)
- **Test Videos**: 7 videos from 5 different classes
  - glasses: 2 videos (glasses 10.mp4, glasses 8.mp4)
  - help: 3 videos (help 10.mp4, help 15.mp4, help 8.mp4)
  - phone: 1 video (phone 9.mp4)
  - pillow: 1 video (pillow 11.mp4)
  - my_mouth_is_dry: 0 videos
  - my_back_hurts: 0 videos
  - i_need_to_move: 0 videos

## Results

### Performance Metrics
- **Total Videos Processed**: 7
- **Success Rate**: 100% (technical processing)
- **Accuracy**: 28.6% (2/7 correct predictions)
- **Average Processing Time**: 0.48 seconds per video

### Prediction Distribution
- **glasses**: 7 videos (100.0%)
- **help**: 0 videos (0.0%)
- **phone**: 0 videos (0.0%)
- **my_mouth_is_dry**: 0 videos (0.0%)
- **my_back_hurts**: 0 videos (0.0%)
- **i_need_to_move**: 0 videos (0.0%)

### Detailed Results by Ground Truth Class

#### Glasses Class (2/2 videos)
- **glasses 10.mp4** → glasses (91.3% confidence) ✅ **CORRECT**
- **glasses 8.mp4** → glasses (93.7% confidence) ✅ **CORRECT**

#### Help Class (0/3 videos correct)
- **help 10.mp4** → glasses (96.2% confidence) ❌ **INCORRECT**
- **help 15.mp4** → glasses (87.1% confidence) ❌ **INCORRECT**
- **help 8.mp4** → glasses (93.0% confidence) ❌ **INCORRECT**

#### Phone Class (0/1 videos correct)
- **phone 9.mp4** → glasses (97.4% confidence) ❌ **INCORRECT**

#### Pillow Class (0/1 videos correct)
- **pillow 11.mp4** → glasses (96.1% confidence) ❌ **INCORRECT**

## Key Findings

### 1. Severe Bias Transfer
- Removing the dominant 'doctor' class didn't improve discrimination
- The bias simply transferred to the next most confident class: 'glasses'
- Model shows **100% prediction rate** for 'glasses' across all test videos

### 2. High Confidence in Wrong Predictions
- All incorrect predictions have very high confidence (87.1% - 97.4%)
- This suggests the model is not just uncertain but systematically biased
- High confidence in wrong predictions indicates poor calibration

### 3. Complete Class Collapse
- 4 out of 6 classes never predicted: ['help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']
- Only 'glasses' class is ever predicted
- This represents a complete failure of multi-class discrimination

### 4. Probability Distribution Analysis
Looking at the probability distributions for misclassified videos:
- **help 10.mp4**: glasses (96.2%), my_back_hurts (3.3%), help (0.4%)
- **help 15.mp4**: glasses (87.1%), my_back_hurts (12.5%), help (0.4%)
- **phone 9.mp4**: glasses (97.4%), other classes < 3%
- **pillow 11.mp4**: glasses (96.1%), other classes < 4%

## Comparison with 7-Class Model

### 7-Class Model Results (Original)
- **Doctor bias**: 90% of predictions were 'doctor'
- **Accuracy**: 20% (2/10 correct, both were 'doctor' class)
- **Classes predicted**: 2 ('doctor', 'glasses')

### 6-Class Model Results (Current)
- **Glasses bias**: 100% of predictions are 'glasses'
- **Accuracy**: 28.6% (2/7 correct, both were 'glasses' class)
- **Classes predicted**: 1 ('glasses' only)

## Implications

### 1. Fundamental Model Issues
- The model appears to have learned superficial features rather than meaningful class distinctions
- Removing the dominant class reveals that the underlying discrimination capability is severely compromised
- The model may be overfitting to dataset artifacts or visual patterns unrelated to lip movements

### 2. Training Data Quality Concerns
- The consistent bias patterns suggest potential issues with training data quality or balance
- The model may have learned to associate certain visual characteristics (lighting, face position, etc.) with specific classes rather than lip movements

### 3. Architecture Limitations
- The R(2+1)D-18 architecture may not be suitable for this specific lip reading task
- The model may require different preprocessing, augmentation, or architectural modifications

## Recommendations

### Immediate Actions
1. **Investigate Training Data**: Examine the original training dataset for class imbalance, visual artifacts, or systematic biases
2. **Feature Analysis**: Analyze what visual features the model is actually learning (using techniques like Grad-CAM)
3. **Preprocessing Review**: Verify that the preprocessing pipeline matches the training procedure exactly

### Model Improvements
1. **Class Balancing**: Ensure equal representation of all classes in training data
2. **Data Augmentation**: Implement more aggressive augmentation to reduce overfitting to visual artifacts
3. **Architecture Exploration**: Consider alternative architectures specifically designed for lip reading
4. **Loss Function**: Experiment with focal loss or other techniques to handle class imbalance

### Evaluation Strategy
1. **Cross-Validation**: Implement proper cross-validation to assess model generalization
2. **Confusion Matrix Analysis**: Generate detailed confusion matrices for better understanding of misclassification patterns
3. **Feature Visualization**: Use visualization techniques to understand what the model is learning

## Conclusion

The 6-class evaluation reveals that the R(2+1)D-18 model's issues are more fundamental than simple class imbalance. The complete collapse to a single predicted class ('glasses') with high confidence suggests the model has not learned meaningful lip reading features but rather superficial visual patterns. This indicates a need for comprehensive review of the training methodology, data quality, and potentially the model architecture itself.

The consistent bias patterns across both 7-class and 6-class evaluations suggest systematic issues that require addressing at the foundational level rather than through post-processing or class exclusion approaches.
