#!/usr/bin/env python3
"""
Comprehensive Inference Script for ICU Test Videos using R(2+1)D-18 Model (6-Class Version)
============================================================================================

This script performs inference on test videos using a trained R(2+1)D-18 model
modified for grayscale input with 6-class output for ICU communication (excluding 'doctor' class).

Model Details:
- Architecture: R(2+1)D-18 modified for grayscale input
- Input format: [Batch, 1, 24, 96, 96] (grayscale, 24 frames, 96x96 resolution)
- Output classes: ['glasses', 'help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']
- Model checkpoint: models/model_ep013.pth (adapted from 7-class to 6-class)
- Excluded class: 'doctor' (to evaluate performance without dominant class bias)

Author: Augment Agent
Date: October 2025
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models.video.resnet import VideoResNet, BasicBlock, Conv2Plus1D, R2Plus1dStem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_infer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Constants
MODEL_PATH = "models/model_ep013.pth"
TEST_DIR = "data/TEST SET"
OUTPUT_JSON = "inference_results_6classes.json"

# Target classes in exact order (excluding 'doctor' class)
CLASS_NAMES = [
    'glasses', 'help', 'phone',
    'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move'
]

# Video file extensions to process
VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}


class GrayscaleR2Plus1D18(nn.Module):
    """
    R(2+1)D-18 model modified for grayscale input and 6-class output.

    This model adapts the standard R(2+1)D-18 architecture to work with:
    - Single channel (grayscale) input instead of RGB
    - 6 output classes for ICU communication (excluding 'doctor')
    - Input shape: [Batch, 1, 24, 96, 96]
    """

    def __init__(self, num_classes: int = 6):
        super().__init__()

        # Create base R(2+1)D-18 architecture with 'base' prefix to match checkpoint
        self.base = VideoResNet(
            block=BasicBlock,
            conv_makers=[Conv2Plus1D] * 4,
            layers=[2, 2, 2, 2],
            stem=self._create_grayscale_stem,
            num_classes=num_classes
        )

        logger.info(f"✅ Created GrayscaleR2Plus1D18 with {num_classes} classes")

    def _create_grayscale_stem(self) -> nn.Module:
        """Create R(2+1)D stem modified for grayscale input (1 channel instead of 3)."""
        return nn.Sequential(
            nn.Conv3d(1, 45, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3), bias=False),
            nn.BatchNorm3d(45),
            nn.ReLU(inplace=True),
            nn.Conv3d(45, 64, kernel_size=(3, 1, 1), stride=(1, 1, 1), padding=(1, 0, 0), bias=False),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the model."""
        return self.base(x)


class VideoPreprocessor:
    """
    Video preprocessing pipeline that matches training requirements exactly.
    
    Pipeline:
    1. Load video using OpenCV
    2. Extract exactly 24 evenly spaced frames
    3. Convert each frame to grayscale
    4. Resize each frame to 96x96 pixels
    5. Normalize pixel values to [0,1] range
    6. Apply standardization: (x - 0.5) / 0.5 (transforms to [-1,1] range)
    7. Format as tensor with shape [1, 1, 24, 96, 96]
    """
    
    def __init__(self):
        self.target_frames = 24
        self.target_size = (96, 96)
        logger.info("✅ VideoPreprocessor initialized")
    
    def process_video(self, video_path: Path) -> Tuple[Optional[torch.Tensor], Dict[str, Any]]:
        """
        Process a single video file.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Tuple of (processed_tensor, metadata_dict)
            processed_tensor is None if processing failed
        """
        metadata = {
            'filename': video_path.name,
            'success': False,
            'error': None,
            'original_frames': 0,
            'original_size': None,
            'processed_frames': 0,
            'processed_size': self.target_size
        }
        
        try:
            # Load video
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                metadata['error'] = "Could not open video file"
                return None, metadata
            
            # Get video properties
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            metadata['original_frames'] = total_frames
            metadata['original_size'] = (width, height)
            
            if total_frames == 0:
                metadata['error'] = "Video has no frames"
                cap.release()
                return None, metadata
            
            # Read all frames
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if len(frames) == 0:
                metadata['error'] = "Could not read any frames"
                return None, metadata
            
            # Extract exactly 24 evenly spaced frames
            if len(frames) >= self.target_frames:
                indices = np.linspace(0, len(frames) - 1, self.target_frames, dtype=int)
                selected_frames = [frames[i] for i in indices]
            else:
                # If video has fewer than 24 frames, repeat frames to reach 24
                selected_frames = frames[:]
                while len(selected_frames) < self.target_frames:
                    selected_frames.extend(frames[:self.target_frames - len(selected_frames)])
                selected_frames = selected_frames[:self.target_frames]
            
            # Process each frame
            processed_frames = []
            for frame in selected_frames:
                # Convert to grayscale
                if len(frame.shape) == 3:
                    gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                else:
                    gray_frame = frame
                
                # Resize to 96x96
                resized_frame = cv2.resize(gray_frame, self.target_size, interpolation=cv2.INTER_AREA)
                
                # Normalize to [0, 1]
                normalized_frame = resized_frame.astype(np.float32) / 255.0
                
                # Apply standardization: (x - 0.5) / 0.5 -> [-1, 1]
                standardized_frame = (normalized_frame - 0.5) / 0.5
                
                processed_frames.append(standardized_frame)
            
            # Convert to tensor: [1, 1, 24, 96, 96]
            video_tensor = torch.tensor(processed_frames, dtype=torch.float32)  # [24, 96, 96]
            video_tensor = video_tensor.unsqueeze(0).unsqueeze(0)  # [1, 1, 24, 96, 96]
            
            metadata['processed_frames'] = len(processed_frames)
            metadata['success'] = True
            
            return video_tensor, metadata
            
        except Exception as e:
            metadata['error'] = f"Processing error: {str(e)}"
            logger.error(f"❌ Error processing {video_path.name}: {str(e)}")
            return None, metadata


class ICUInferenceEngine:
    """
    Main inference engine for ICU test videos.
    
    Handles model loading, video processing, inference execution,
    and results generation.
    """
    
    def __init__(self, model_path: str, test_dir: str):
        self.model_path = Path(model_path)
        self.test_dir = Path(test_dir)
        self.device = self._get_device()
        self.model = None
        self.preprocessor = VideoPreprocessor()
        self.results = []
        
        logger.info(f"🚀 ICUInferenceEngine initialized")
        logger.info(f"📱 Device: {self.device}")
        logger.info(f"🎯 Model path: {self.model_path}")
        logger.info(f"📁 Test directory: {self.test_dir}")
    
    def _get_device(self) -> torch.device:
        """Determine the best available device."""
        if torch.cuda.is_available():
            return torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return torch.device('mps')
        else:
            return torch.device('cpu')
    
    def load_model(self) -> bool:
        """Load the trained model from checkpoint, adapting from 7-class to 6-class model."""
        try:
            if not self.model_path.exists():
                logger.error(f"❌ Model file not found: {self.model_path}")
                return False

            # Create model with 6 classes (excluding 'doctor')
            self.model = GrayscaleR2Plus1D18(num_classes=len(CLASS_NAMES))

            # Load state dict from 7-class model
            checkpoint = torch.load(self.model_path, map_location=self.device)

            # Handle different checkpoint formats
            if isinstance(checkpoint, dict):
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint

            # Adapt the final layer weights from 7 classes to 6 classes
            # Original classes: ['doctor', 'glasses', 'help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']
            # New classes: ['glasses', 'help', 'phone', 'my_mouth_is_dry', 'my_back_hurts', 'i_need_to_move']
            # We need to exclude index 0 ('doctor') and keep indices 1-6

            if 'base.fc.weight' in state_dict and 'base.fc.bias' in state_dict:
                original_fc_weight = state_dict['base.fc.weight']  # Shape: [7, 512]
                original_fc_bias = state_dict['base.fc.bias']      # Shape: [7]

                # Extract weights for classes 1-6 (excluding class 0 which is 'doctor')
                new_fc_weight = original_fc_weight[1:, :]  # Shape: [6, 512]
                new_fc_bias = original_fc_bias[1:]         # Shape: [6]

                state_dict['base.fc.weight'] = new_fc_weight
                state_dict['base.fc.bias'] = new_fc_bias

                logger.info(f"✅ Adapted final layer from 7 classes to 6 classes (excluded 'doctor')")
                logger.info(f"📊 FC layer: {original_fc_weight.shape} -> {new_fc_weight.shape}")

            self.model.load_state_dict(state_dict)
            self.model.to(self.device)
            self.model.eval()

            # Count parameters
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

            logger.info(f"✅ Model loaded successfully")
            logger.info(f"📊 Total parameters: {total_params:,}")
            logger.info(f"🎯 Trainable parameters: {trainable_params:,}")

            return True

        except Exception as e:
            logger.error(f"❌ Error loading model: {str(e)}")
            return False
    
    def find_test_videos(self) -> List[Path]:
        """Find all video files in the test directory, excluding 'doctor' class videos."""
        if not self.test_dir.exists():
            logger.error(f"❌ Test directory not found: {self.test_dir}")
            return []

        video_files = []
        excluded_count = 0

        for file_path in self.test_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in VIDEO_EXTENSIONS:
                # Exclude videos with 'doctor' in filename
                if 'doctor' in file_path.name.lower():
                    excluded_count += 1
                    logger.info(f"🚫 Excluding doctor class video: {file_path.name}")
                    continue
                video_files.append(file_path)

        video_files.sort()  # Sort for consistent ordering
        logger.info(f"📹 Found {len(video_files)} video files (excluded {excluded_count} doctor videos)")

        return video_files
    
    def run_inference(self, video_tensor: torch.Tensor) -> Tuple[str, float, Dict[str, float]]:
        """
        Run model inference on a preprocessed video tensor.
        
        Args:
            video_tensor: Preprocessed video tensor [1, 1, 24, 96, 96]
            
        Returns:
            Tuple of (predicted_class, confidence, all_probabilities)
        """
        with torch.no_grad():
            video_tensor = video_tensor.to(self.device)
            
            # Forward pass
            logits = self.model(video_tensor)
            
            # Apply softmax to get probabilities
            probabilities = F.softmax(logits, dim=1)
            
            # Get prediction
            predicted_idx = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0, predicted_idx].item()
            predicted_class = CLASS_NAMES[predicted_idx]
            
            # Get all class probabilities
            all_probs = {
                CLASS_NAMES[i]: probabilities[0, i].item() 
                for i in range(len(CLASS_NAMES))
            }
            
            return predicted_class, confidence, all_probs
    
    def process_single_video(self, video_path: Path) -> Dict[str, Any]:
        """Process a single video file and return results."""
        logger.info(f"🎬 Processing: {video_path.name}")
        
        start_time = time.time()
        
        # Preprocess video
        video_tensor, preprocessing_metadata = self.preprocessor.process_video(video_path)
        
        result = {
            'filename': video_path.name,
            'preprocessing': preprocessing_metadata,
            'inference': {
                'success': False,
                'predicted_class': None,
                'confidence': None,
                'all_probabilities': {},
                'processing_time_seconds': None
            }
        }
        
        if video_tensor is None:
            logger.error(f"❌ Preprocessing failed for {video_path.name}")
            result['inference']['processing_time_seconds'] = time.time() - start_time
            return result
        
        try:
            # Run inference
            predicted_class, confidence, all_probs = self.run_inference(video_tensor)
            
            result['inference'].update({
                'success': True,
                'predicted_class': predicted_class,
                'confidence': confidence,
                'all_probabilities': all_probs,
                'processing_time_seconds': time.time() - start_time
            })
            
            # Print result in required format
            print(f"{video_path.name} -> {predicted_class} (confidence: {confidence:.1%})")
            
            logger.info(f"✅ {video_path.name} -> {predicted_class} ({confidence:.1%})")
            
        except Exception as e:
            result['inference']['error'] = str(e)
            result['inference']['processing_time_seconds'] = time.time() - start_time
            logger.error(f"❌ Inference failed for {video_path.name}: {str(e)}")
        
        return result
    
    def run_comprehensive_inference(self) -> Dict[str, Any]:
        """Run inference on all test videos and generate comprehensive results."""
        logger.info("🎯 Starting comprehensive inference...")
        
        # Load model
        if not self.load_model():
            return {'error': 'Failed to load model'}
        
        # Find test videos
        video_files = self.find_test_videos()
        if not video_files:
            return {'error': 'No test videos found'}
        
        # Process all videos
        start_time = time.time()
        results = []
        
        for i, video_path in enumerate(video_files, 1):
            logger.info(f"📹 Processing video {i}/{len(video_files)}: {video_path.name}")
            result = self.process_single_video(video_path)
            results.append(result)
        
        total_time = time.time() - start_time
        
        # Generate summary statistics
        successful_inferences = [r for r in results if r['inference']['success']]
        failed_inferences = [r for r in results if not r['inference']['success']]
        
        # Count predictions by class
        prediction_counts = defaultdict(int)
        for result in successful_inferences:
            prediction_counts[result['inference']['predicted_class']] += 1
        
        # Calculate average processing time
        processing_times = [r['inference']['processing_time_seconds'] for r in results 
                          if r['inference']['processing_time_seconds'] is not None]
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        
        summary = {
            'total_videos': len(video_files),
            'successful_inferences': len(successful_inferences),
            'failed_inferences': len(failed_inferences),
            'success_rate': len(successful_inferences) / len(video_files) if video_files else 0,
            'total_processing_time_seconds': total_time,
            'average_processing_time_per_video_seconds': avg_processing_time,
            'prediction_distribution': dict(prediction_counts),
            'model_info': {
                'model_path': str(self.model_path),
                'device': str(self.device),
                'classes': CLASS_NAMES
            }
        }
        
        comprehensive_results = {
            'summary': summary,
            'individual_results': results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save results to JSON
        with open(OUTPUT_JSON, 'w') as f:
            json.dump(comprehensive_results, f, indent=2)
        
        # Print summary
        self._print_summary(summary)
        
        logger.info(f"💾 Results saved to: {OUTPUT_JSON}")
        logger.info("🎉 Comprehensive inference complete!")
        
        return comprehensive_results
    
    def _print_summary(self, summary: Dict[str, Any]) -> None:
        """Print a formatted summary of results."""
        print("\n" + "="*80)
        print("🎯 INFERENCE SUMMARY")
        print("="*80)
        print(f"📹 Total videos processed: {summary['total_videos']}")
        print(f"✅ Successful inferences: {summary['successful_inferences']}")
        print(f"❌ Failed inferences: {summary['failed_inferences']}")
        print(f"📊 Success rate: {summary['success_rate']:.1%}")
        print(f"⏱️  Total processing time: {summary['total_processing_time_seconds']:.2f}s")
        print(f"⚡ Average time per video: {summary['average_processing_time_per_video_seconds']:.2f}s")
        
        print("\n📈 PREDICTION DISTRIBUTION:")
        for class_name, count in summary['prediction_distribution'].items():
            percentage = (count / summary['successful_inferences']) * 100 if summary['successful_inferences'] > 0 else 0
            print(f"  {class_name}: {count} videos ({percentage:.1f}%)")
        
        print("="*80)


def main():
    """Main function to run the inference script."""
    print("🚀 ICU Video Inference with R(2+1)D-18 Model (6-Class Version)")
    print("="*80)
    
    # Create inference engine
    engine = ICUInferenceEngine(MODEL_PATH, TEST_DIR)
    
    # Run comprehensive inference
    results = engine.run_comprehensive_inference()
    
    if 'error' in results:
        logger.error(f"❌ Inference failed: {results['error']}")
        sys.exit(1)
    
    print(f"\n✅ Inference completed successfully!")
    print(f"📄 Detailed results saved to: {OUTPUT_JSON}")


if __name__ == "__main__":
    main()
