#!/usr/bin/env python3
"""
Systematic Video Testing Script for Lip Reading Model
====================================================

Tests all video files in the specified directory with our lip reading model.
Provides comprehensive analysis of model performance across multiple videos.
"""

import cv2
import numpy as np
import json
import subprocess
import os
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystematicVideoTester:
    def __init__(self, test_directory, model_classes):
        self.test_directory = Path(test_directory)
        self.model_classes = model_classes
        self.results = []
        self.processed_videos_dir = Path("systematic_test_processed")
        self.processed_videos_dir.mkdir(exist_ok=True)
        
    def find_video_files(self):
        """Find all video files in the test directory."""
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
        video_files = []
        
        for ext in video_extensions:
            video_files.extend(self.test_directory.glob(f"*{ext}"))
        
        return sorted(video_files)
    
    def get_video_properties(self, video_path):
        """Get video properties using ffprobe."""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                video_stream = next((s for s in data['streams'] if s['codec_type'] == 'video'), None)
                
                if video_stream:
                    return {
                        'width': int(video_stream['width']),
                        'height': int(video_stream['height']),
                        'nb_frames': int(video_stream.get('nb_frames', 0)),
                        'duration': float(data['format']['duration']),
                        'fps': eval(video_stream['r_frame_rate']),
                        'size_bytes': int(data['format']['size'])
                    }
        except Exception as e:
            logger.error(f"Error getting video properties for {video_path}: {e}")
        
        return None
    
    def preprocess_video(self, input_path, output_path):
        """Preprocess video to match model requirements."""
        try:
            logger.info(f"🎬 Preprocessing: {input_path.name}")
            
            # Load video
            cap = cv2.VideoCapture(str(input_path))
            if not cap.isOpened():
                return {"success": False, "error": "Could not open video file"}
            
            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Read all frames
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if not frames:
                return {"success": False, "error": "No frames found in video"}
            
            # Determine ROI based on video dimensions
            if width > height:  # Landscape
                roi_x = int(width * 0.2)
                roi_y = int(height * 0.3)
                roi_w = int(width * 0.6)
                roi_h = int(height * 0.5)
                format_type = "landscape"
            else:  # Portrait
                roi_x = int(width * 0.15)
                roi_y = int(height * 0.35)
                roi_w = int(width * 0.7)
                roi_h = int(height * 0.25)
                format_type = "portrait"
            
            # Process frames
            processed_frames = []
            for frame in frames:
                # Extract ROI
                roi = frame[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
                
                # Resize to 96×64
                resized = cv2.resize(roi, (96, 64), interpolation=cv2.INTER_AREA)
                
                # Convert to grayscale
                gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
                processed_frames.append(gray)
            
            # Sample to exactly 32 frames
            if len(processed_frames) >= 32:
                indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
                sampled_frames = [processed_frames[i] for i in indices]
            else:
                sampled_frames = processed_frames[:]
                while len(sampled_frames) < 32:
                    sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
                sampled_frames = sampled_frames[:32]
            
            # Save as MP4 (grayscale)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
            
            for frame in sampled_frames:
                out.write(frame)
            out.release()
            
            # Verify output
            verify_cap = cv2.VideoCapture(str(output_path))
            if verify_cap.isOpened():
                out_frames = int(verify_cap.get(cv2.CAP_PROP_FRAME_COUNT))
                out_width = int(verify_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                out_height = int(verify_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                verify_cap.release()
                
                format_compliant = (out_width == 96 and out_height == 64 and out_frames == 32)
                
                return {
                    "success": True,
                    "method_used": f"geometric_fallback_{format_type}",
                    "input_dimensions": f"{width}×{height}",
                    "output_dimensions": f"{out_width}×{out_height}",
                    "input_frames": len(frames),
                    "output_frames": out_frames,
                    "format_compliant": format_compliant,
                    "roi_coordinates": f"{roi_x},{roi_y} {roi_w}×{roi_h}"
                }
            else:
                return {"success": False, "error": "Could not verify output video"}
                
        except Exception as e:
            logger.error(f"❌ Preprocessing failed for {input_path.name}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def run_inference(self, processed_video_path):
        """Run model inference on processed video."""
        try:
            cmd = ['python', 'lip_reading_inference.py', '--video', str(processed_video_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd='.')
            
            if result.returncode == 0:
                # Parse the output to extract prediction results
                output_lines = result.stdout.strip().split('\n')
                
                predicted_word = None
                confidence = None
                probabilities = {}
                
                for line in output_lines:
                    if "Predicted Word:" in line:
                        predicted_word = line.split("Predicted Word:")[1].strip()
                    elif "Confidence:" in line:
                        confidence = float(line.split("Confidence:")[1].strip())
                    elif ":" in line and any(cls in line for cls in self.model_classes):
                        # Parse probability lines
                        parts = line.strip().split(":")
                        if len(parts) == 2:
                            class_name = parts[0].strip()
                            prob_value = float(parts[1].strip())
                            probabilities[class_name] = prob_value
                
                return {
                    "success": True,
                    "predicted_word": predicted_word,
                    "confidence": confidence,
                    "probabilities": probabilities
                }
            else:
                return {"success": False, "error": f"Inference failed: {result.stderr}"}
                
        except Exception as e:
            logger.error(f"❌ Inference failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_single_video(self, video_path):
        """Test a single video file through the complete pipeline."""
        logger.info(f"\n🎯 Testing video: {video_path.name}")
        
        # Get video properties
        properties = self.get_video_properties(video_path)
        if not properties:
            return {
                "filename": video_path.name,
                "status": "failed",
                "error": "Could not get video properties"
            }
        
        # Preprocess video
        processed_filename = f"processed_{video_path.stem}.mp4"
        processed_path = self.processed_videos_dir / processed_filename
        
        preprocessing_result = self.preprocess_video(video_path, processed_path)
        if not preprocessing_result["success"]:
            return {
                "filename": video_path.name,
                "status": "failed",
                "error": f"Preprocessing failed: {preprocessing_result['error']}",
                "properties": properties
            }
        
        # Run inference
        inference_result = self.run_inference(processed_path)
        if not inference_result["success"]:
            return {
                "filename": video_path.name,
                "status": "failed",
                "error": f"Inference failed: {inference_result['error']}",
                "properties": properties,
                "preprocessing": preprocessing_result
            }
        
        # Combine results
        return {
            "filename": video_path.name,
            "status": "success",
            "properties": properties,
            "preprocessing": preprocessing_result,
            "inference": inference_result
        }
    
    def test_all_videos(self):
        """Test all videos in the directory."""
        video_files = self.find_video_files()
        
        logger.info(f"🎬 Found {len(video_files)} video files to test")
        logger.info("="*80)
        
        for i, video_path in enumerate(video_files, 1):
            logger.info(f"📹 Testing video {i}/{len(video_files)}: {video_path.name}")
            result = self.test_single_video(video_path)
            self.results.append(result)
            
            if result["status"] == "success":
                logger.info(f"✅ Success: {result['inference']['predicted_word']} ({result['inference']['confidence']:.1%})")
            else:
                logger.error(f"❌ Failed: {result.get('error', 'Unknown error')}")
        
        return self.results

def main():
    """Main function to run systematic testing."""
    test_directory = "/Users/<USER>/iCloud Drive (Archive)/Desktop/LRP classifier 11.9.25/test clips/1.10.25"
    model_classes = ['doctor', 'my_back_hurts', 'my_mouth_is_dry', 'phone', 'pillow', 'water', 'where_does_it_hurt']
    
    print("🎯 SYSTEMATIC LIP READING MODEL TESTING")
    print("="*80)
    print(f"📁 Test Directory: {test_directory}")
    print(f"🧠 Model Classes: {', '.join(model_classes)}")
    print("="*80)
    
    tester = SystematicVideoTester(test_directory, model_classes)
    results = tester.test_all_videos()
    
    # Save results to JSON file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"systematic_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Systematic testing complete!")
    
    return results

if __name__ == "__main__":
    main()
