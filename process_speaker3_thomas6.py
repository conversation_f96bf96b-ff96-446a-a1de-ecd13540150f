#!/usr/bin/env python3
"""
Process Speaker 3 Thomas 6 Videos - Lip Cropping
================================================

Process all videos in the 'speaker 3 thomas 6 done' directory with direct lip cropping.
Uses adaptive geometric cropping based on video dimensions.

Output specifications:
- Crop lip region using dimension-appropriate coordinates
- Convert to grayscale
- Resize to 96×64 pixels
- Sample to exactly 32 frames (uniform)
- Save as MP4 at 15 FPS
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import sys
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_lip_crop_video(input_path: Path, output_path: Path) -> bool:
    """
    Directly crop the lip region without any detection - adaptive geometric targeting.
    
    Adapts cropping coordinates based on video dimensions:
    - For 400x200 videos: 25%-75% width, 5%-45% height
    - For 1620x1080 videos: 35%-65% width, 40%-70% height (face region)
    """
    
    logger.info(f"🎬 Processing: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    logger.info(f"📹 Loaded {len(frames)} frames, {frames[0].shape[1]}×{frames[0].shape[0]} pixels")
    
    # ADAPTIVE GEOMETRIC CROP BASED ON DIMENSIONS
    h, w = frames[0].shape[:2]
    
    if w == 400 and h == 200:
        # Small videos - direct lip region
        crop_x1 = int(w * 0.25)  # 25% from left
        crop_x2 = int(w * 0.75)  # 75% from left
        crop_y1 = int(h * 0.05)  # 5% from top
        crop_y2 = int(h * 0.45)  # 45% from top
        logger.info("📐 Using 400x200 crop coordinates")
    elif w >= 1600 and h >= 1000:
        # Large videos - face region then lip area
        crop_x1 = int(w * 0.35)  # 35% from left
        crop_x2 = int(w * 0.65)  # 65% from left
        crop_y1 = int(h * 0.40)  # 40% from top (lower face)
        crop_y2 = int(h * 0.70)  # 70% from top
        logger.info("📐 Using large video crop coordinates")
    else:
        # Default proportional cropping
        crop_x1 = int(w * 0.30)
        crop_x2 = int(w * 0.70)
        crop_y1 = int(h * 0.30)
        crop_y2 = int(h * 0.70)
        logger.info("📐 Using default proportional crop coordinates")
    
    logger.info(f"🎯 Crop region: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
    logger.info(f"📏 Crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1} pixels")
    
    # Process frames with direct cropping
    processed_frames = []
    
    for frame in frames:
        # DIRECT CROP - no detection, just extract the region
        cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Convert to grayscale
        gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
        
        # Resize to 96×64
        resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        processed_frames.append(resized)
    
    # Sample to exactly 32 frames uniformly
    if len(processed_frames) > 32:
        # Uniform sampling
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    elif len(processed_frames) < 32:
        # Repeat frames to reach 32
        sampled_frames = []
        for i in range(32):
            frame_idx = i % len(processed_frames)
            sampled_frames.append(processed_frames[frame_idx])
    else:
        sampled_frames = processed_frames
    
    logger.info(f"📊 Sampled to {len(sampled_frames)} frames")
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    logger.info(f"✅ Saved: {output_path}")
    return True

def find_all_videos(base_dir: Path) -> List[Path]:
    """Find all video files in the directory structure."""
    video_files = []
    for ext in ['.mp4', '.avi', '.mov']:
        video_files.extend(base_dir.rglob(f'*{ext}'))
    return sorted(video_files)

def main():
    # Define paths
    input_dir = Path("speaker_sets/partial_speaker_sets_top7/speaker 3 thomas 6 done")
    output_dir = Path("processed_videos/speaker3_thomas6_lips")
    
    if not input_dir.exists():
        logger.error(f"Input directory not found: {input_dir}")
        sys.exit(1)
    
    # Find all videos
    video_files = find_all_videos(input_dir)
    
    if not video_files:
        logger.error(f"No videos found in {input_dir}")
        sys.exit(1)
    
    logger.info(f"🎬 Found {len(video_files)} videos to process")
    
    print("🎯 SPEAKER 3 THOMAS 6 - LIP CROPPING PROCESSOR")
    print("="*60)
    print("Strategy:")
    print("• Adaptive geometric cropping (dimension-aware)")
    print("• 400x200: 25%-75% width, 5%-45% height")
    print("• 1620x1080: 35%-65% width, 40%-70% height")
    print("• Convert to grayscale")
    print("• Resize to 96×64 pixels")
    print("• Sample to exactly 32 frames")
    print("• Save as MP4 at 15 FPS")
    print("="*60)
    
    # Process all videos
    success_count = 0
    
    for i, video_path in enumerate(video_files, 1):
        print(f"\n🎬 Processing video {i}/{len(video_files)}")
        
        # Create output path maintaining directory structure
        relative_path = video_path.relative_to(input_dir)
        output_path = output_dir / relative_path.parent / f"lips_{relative_path.stem}.mp4"
        
        # Process the video
        success = direct_lip_crop_video(video_path, output_path)
        
        if success:
            success_count += 1
            print(f"✅ Completed: {output_path.name}")
        else:
            print(f"❌ Failed: {video_path.name}")
    
    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{len(video_files)} videos")
    print(f"📁 Output directory: {output_dir}")
    
    if success_count < len(video_files):
        print(f"⚠️  {len(video_files) - success_count} videos failed")

if __name__ == '__main__':
    main()
