#!/usr/bin/env python3
"""
GRID Speaker 1 Classifier Training
=================================

Train a lip reading classifier using only GRID Speaker 1 data for the 7 target word classes.
Uses the same architecture as the LOSO training but adapted for single-speaker training.

Target Classes:
1. doctor
2. i_need_to_move  
3. my_back_hurts
4. my_mouth_is_dry
5. phone
6. pillow
7. glasses
"""

import os, random, time
from pathlib import Path
import numpy as np
import cv2
import torch, torch.nn as nn, torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

# ========= CONFIG =========
GRID_S1_DATA = "grid_s1_processed"
ENCODER_CHECKPOINT = "sagemaker_training_files/checkpoints/grid_pretrain_s1s4/encoder.pt"
OUT_DIR = "grid_s1_training_results"

DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
BATCH_SIZE = 8
N_FRAMES = 32
H, W = 64, 96
EPOCHS_STAGE1 = 10
EPOCHS_STAGE2 = 100
PATIENCE = 15
LR_HEAD = 1e-3
LR_ENCODER = 5e-5
NUM_WORKERS = 2
LABEL_SMOOTH = 0.1
TRAIN_SPLIT = 0.8
VAL_SPLIT = 0.2

os.makedirs(OUT_DIR, exist_ok=True)

# Target classes
CLASSES = [
    "doctor", "i_need_to_move", "my_back_hurts", 
    "my_mouth_is_dry", "phone", "pillow", "glasses"
]
CLASS_TO_IDX = {c: i for i, c in enumerate(CLASSES)}
NUM_CLASSES = len(CLASSES)

print("Target Classes:", CLASSES)
print("Device:", DEVICE)

# ========= Augmentation =========
def augment_frames(frames: np.ndarray):
    """Apply data augmentation to video frames."""
    if random.random() < 0.5:
        frames = np.flip(frames, axis=2).copy()  # Horizontal flip
    if random.random() < 0.5:
        factor = 0.8 + 0.4 * random.random()  # Brightness adjustment
        frames = np.clip(frames * factor, 0, 1)
    if random.random() < 0.3:
        # Add slight noise
        noise = np.random.normal(0, 0.02, frames.shape)
        frames = np.clip(frames + noise, 0, 1)
    return frames

# ========= Video I/O =========
def sample_frames_from_video(path, n_frames=N_FRAMES):
    """Load and sample frames from video file."""
    cap = cv2.VideoCapture(str(path))
    frames = []
    total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total <= 0:
        cap.release()
        raise RuntimeError(f"Bad video: {path}")
    
    if total >= n_frames:
        start = max(0, (total - n_frames) // 2)
        idxs = list(range(start, start + n_frames))
    else:
        idxs = list(range(total)) + [total - 1] * (n_frames - total)
    
    cur = 0
    got = []
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
    while True:
        ret, fr = cap.read()
        if not ret:
            break
        if cur in idxs:
            if len(fr.shape) == 3:
                fr = cv2.cvtColor(fr, cv2.COLOR_BGR2GRAY)
            fr = cv2.resize(fr, (W, H))
            got.append(fr)
        cur += 1
    cap.release()
    
    while len(got) < n_frames:
        got.append(got[-1].copy())
    
    return np.stack(got[:n_frames], axis=0).astype(np.float32) / 255.0

# ========= Dataset =========
class GridS1Dataset(Dataset):
    """Dataset for GRID Speaker 1 videos."""
    
    def __init__(self, video_paths, labels, augment=False):
        self.video_paths = video_paths
        self.labels = labels
        self.augment = augment
        
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        path = self.video_paths[idx]
        label = self.labels[idx]
        
        frames = sample_frames_from_video(path)
        if self.augment:
            frames = augment_frames(frames)
        
        frames = np.expand_dims(frames, 1)  # Add channel dimension
        return torch.from_numpy(frames), label

def collate_fn(batch):
    """Collate function for DataLoader."""
    vids = torch.stack([b[0] for b in batch], 0).float()
    labels = torch.tensor([b[1] for b in batch], dtype=torch.long)
    return vids, labels

# ========= Model Architecture =========
class Encoder(nn.Module):
    """Video encoder with CNN + GRU architecture."""
    
    def __init__(self, hidden_dim=256):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(1, 32, 3, stride=2, padding=1), nn.ReLU(),
            nn.Conv2d(32, 64, 3, stride=2, padding=1), nn.ReLU(),
            nn.Conv2d(64, 128, 3, stride=2, padding=1), nn.ReLU()
        )
        self.flatten_dim = 128 * 8 * 12
        self.gru = nn.GRU(self.flatten_dim, hidden_dim, batch_first=True, bidirectional=True)
        self.out_dim = hidden_dim * 2
    
    def forward(self, x):
        B, T, C, H, W = x.shape
        x = x.view(B * T, C, H, W)
        x = self.conv(x)
        x = x.view(B, T, -1)
        out, _ = self.gru(x)
        return out

class AttentivePooling(nn.Module):
    """Attention-based pooling layer."""
    
    def __init__(self, d_in, d_att=128):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(d_in, d_att),
            nn.Tanh(),
            nn.Linear(d_att, 1)
        )
    
    def forward(self, x):
        e = self.mlp(x).squeeze(-1)
        w = torch.softmax(e, dim=1)
        pooled = (x * w.unsqueeze(-1)).sum(dim=1)
        return pooled, w

class ClassifierHead(nn.Module):
    """Classification head with GRU + attention pooling."""
    
    def __init__(self, encoder_out_dim=512, gru_hidden=256, num_layers=2, 
                 dropout=0.3, num_classes=NUM_CLASSES):
        super().__init__()
        self.gru = nn.GRU(encoder_out_dim, gru_hidden, batch_first=True,
                         bidirectional=True, num_layers=num_layers, dropout=dropout)
        self.pool = AttentivePooling(gru_hidden * 2)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(gru_hidden * 2, num_classes)
    
    def forward(self, enc_feats):
        out, _ = self.gru(enc_feats)
        pooled, _ = self.pool(out)
        return self.fc(self.dropout(pooled))

class FullModel(nn.Module):
    """Complete model combining encoder and classifier head."""
    
    def __init__(self, encoder, head):
        super().__init__()
        self.encoder = encoder
        self.head = head
    
    def forward(self, x):
        return self.head(self.encoder(x))

# ========= Training Functions =========
def train_one_epoch(model, loader, criterion, opt, device):
    """Train for one epoch."""
    model.train()
    loss_sum = 0
    tot = 0
    correct = 0
    
    for vids, labels in loader:
        vids, labels = vids.to(device), labels.to(device)
        opt.zero_grad()
        logits = model(vids)
        loss = criterion(logits, labels)
        loss.backward()
        opt.step()
        
        loss_sum += loss.item() * vids.size(0)
        tot += vids.size(0)
        correct += (logits.argmax(1) == labels).sum().item()
    
    return loss_sum / max(1, tot), correct / max(1, tot)

@torch.no_grad()
def eval_model(model, loader, criterion, device):
    """Evaluate model."""
    model.eval()
    loss_sum = 0
    tot = 0
    correct = 0
    class_correct = np.zeros(NUM_CLASSES)
    class_tot = np.zeros(NUM_CLASSES)
    
    for vids, labels in loader:
        vids, labels = vids.to(device), labels.to(device)
        logits = model(vids)
        loss = criterion(logits, labels)
        
        loss_sum += loss.item() * vids.size(0)
        tot += vids.size(0)
        preds = logits.argmax(1)
        correct += (preds == labels).sum().item()
        
        for l, p in zip(labels.cpu(), preds.cpu()):
            class_tot[l] += 1
            if l == p:
                class_correct[l] += 1
    
    class_acc = {CLASSES[i]: (class_correct[i] / class_tot[i] if class_tot[i] > 0 else 0.0) 
                 for i in range(NUM_CLASSES)}
    
    return loss_sum / max(1, tot), correct / max(1, tot), class_acc

def load_grid_s1_data():
    """Load GRID Speaker 1 processed data."""
    data_dir = Path(GRID_S1_DATA)
    if not data_dir.exists():
        raise FileNotFoundError(f"GRID S1 data directory not found: {data_dir}")
    
    video_paths = []
    labels = []
    
    for class_name in CLASSES:
        class_dir = data_dir / class_name
        if not class_dir.exists():
            print(f"Warning: Class directory not found: {class_dir}")
            continue
        
        class_videos = list(class_dir.glob("*.mp4"))
        print(f"Found {len(class_videos)} videos for class '{class_name}'")
        
        for video_path in class_videos:
            video_paths.append(str(video_path))
            labels.append(CLASS_TO_IDX[class_name])
    
    return video_paths, labels

def main():
    """Main training function."""
    print("🎬 Loading GRID Speaker 1 data...")
    video_paths, labels = load_grid_s1_data()
    
    if not video_paths:
        print("❌ No videos found! Please run process_grid_s1_for_training.py first.")
        return
    
    print(f"📊 Total videos: {len(video_paths)}")
    
    # Split data
    train_paths, val_paths, train_labels, val_labels = train_test_split(
        video_paths, labels, test_size=VAL_SPLIT, random_state=42, stratify=labels
    )
    
    print(f"📈 Training videos: {len(train_paths)}")
    print(f"📉 Validation videos: {len(val_paths)}")
    
    # Create datasets
    train_ds = GridS1Dataset(train_paths, train_labels, augment=True)
    val_ds = GridS1Dataset(val_paths, val_labels, augment=False)
    
    train_loader = DataLoader(train_ds, batch_size=BATCH_SIZE, shuffle=True,
                             num_workers=NUM_WORKERS, collate_fn=collate_fn)
    val_loader = DataLoader(val_ds, batch_size=BATCH_SIZE, shuffle=False,
                           num_workers=NUM_WORKERS, collate_fn=collate_fn)
    
    # Create model
    encoder = Encoder()
    
    # Load pretrained encoder if available
    if Path(ENCODER_CHECKPOINT).exists():
        print(f"🔄 Loading pretrained encoder from {ENCODER_CHECKPOINT}")
        ck = torch.load(ENCODER_CHECKPOINT, map_location="cpu")
        encoder.load_state_dict(ck, strict=False)
        print("✅ Loaded pretrained encoder")
    else:
        print("⚠️  No pretrained encoder found, training from scratch")
    
    head = ClassifierHead(encoder_out_dim=encoder.out_dim)
    model = FullModel(encoder, head).to(DEVICE)
    
    criterion = nn.CrossEntropyLoss(label_smoothing=LABEL_SMOOTH)
    
    # Training history
    train_losses, train_accs = [], []
    val_losses, val_accs = [], []
    
    print("\n🚀 Starting training...")
    print("="*60)
    
    # Stage 1: Train head only
    print("📚 Stage 1: Training classifier head only")
    for p in model.encoder.parameters():
        p.requires_grad = False
    
    opt = optim.Adam(model.head.parameters(), lr=LR_HEAD)
    
    for ep in range(1, EPOCHS_STAGE1 + 1):
        tr_loss, tr_acc = train_one_epoch(model, train_loader, criterion, opt, DEVICE)
        vl_loss, val_acc, val_class_acc = eval_model(model, val_loader, criterion, DEVICE)
        
        train_losses.append(tr_loss)
        train_accs.append(tr_acc)
        val_losses.append(vl_loss)
        val_accs.append(val_acc)
        
        print(f"[S1 {ep:2d}/{EPOCHS_STAGE1}] train_loss:{tr_loss:.4f} train_acc:{tr_acc:.4f} "
              f"val_loss:{vl_loss:.4f} val_acc:{val_acc:.4f}")
        print("   Per-class val acc:", {k: f"{v:.3f}" for k, v in val_class_acc.items()})
    
    # Stage 2: Fine-tune entire model
    print("\n🔧 Stage 2: Fine-tuning entire model")
    for p in model.encoder.parameters():
        p.requires_grad = True
    
    opt = optim.Adam([
        {"params": model.encoder.parameters(), "lr": LR_ENCODER},
        {"params": model.head.parameters(), "lr": LR_HEAD}
    ])
    
    best_val = 0.0
    patience = PATIENCE
    best_state = None
    
    for ep in range(1, EPOCHS_STAGE2 + 1):
        tr_loss, tr_acc = train_one_epoch(model, train_loader, criterion, opt, DEVICE)
        vl_loss, val_acc, val_class_acc = eval_model(model, val_loader, criterion, DEVICE)
        
        train_losses.append(tr_loss)
        train_accs.append(tr_acc)
        val_losses.append(vl_loss)
        val_accs.append(val_acc)
        
        print(f"[S2 {ep:2d}/{EPOCHS_STAGE2}] train_loss:{tr_loss:.4f} train_acc:{tr_acc:.4f} "
              f"val_loss:{vl_loss:.4f} val_acc:{val_acc:.4f}")
        print("   Per-class val acc:", {k: f"{v:.3f}" for k, v in val_class_acc.items()})
        
        if val_acc > best_val:
            best_val = val_acc
            patience = PATIENCE
            best_state = model.state_dict()
            torch.save(best_state, f"{OUT_DIR}/best_grid_s1_model.pth")
            print(f"  ✅ New best validation accuracy: {val_acc:.4f}")
        else:
            patience -= 1
            if patience == 0:
                print("  ⏹ Early stopping")
                break
    
    print(f"\n🎉 Training complete! Best validation accuracy: {best_val:.4f}")
    
    # Save training curves
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.axvline(x=EPOCHS_STAGE1, color='red', linestyle='--', alpha=0.7, label='Stage 2 Start')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training Loss')
    
    plt.subplot(1, 2, 2)
    plt.plot(train_accs, label='Train Acc')
    plt.plot(val_accs, label='Val Acc')
    plt.axvline(x=EPOCHS_STAGE1, color='red', linestyle='--', alpha=0.7, label='Stage 2 Start')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.title('Training Accuracy')
    
    plt.tight_layout()
    plt.savefig(f"{OUT_DIR}/grid_s1_training_curves.png", dpi=150, bbox_inches='tight')
    print(f"📈 Training curves saved to {OUT_DIR}/grid_s1_training_curves.png")

if __name__ == "__main__":
    main()
