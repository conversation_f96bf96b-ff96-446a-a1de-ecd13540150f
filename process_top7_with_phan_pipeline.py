#!/usr/bin/env python3
"""
Process Top7 Videos with Speaker 2 Phan Pipeline
===============================================

Replicates the exact preprocessing approach that was successfully used for 
the "speaker 2 phan 4 done" dataset (80 videos in 1280×720 resolution).

Uses the proven adaptive geometric cropping parameters:
- For 400x200 videos: 25%-75% width, 5%-45% height
- For 1280x720 videos: 30%-70% width, 45%-75% height (face region)
- For 1620x1080+ videos: 35%-65% width, 40%-70% height (face region)
- For speaker 2 phan specific: 35%-65% width, 50%-70% height (tighter crop)

Output specifications:
- Crop lip region using dimension-appropriate coordinates
- Convert to grayscale
- Resize to 96×64 pixels
- Sample to exactly 32 frames (uniform)
- Save as MP4 at 15 FPS
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import sys
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def adaptive_lip_crop_video(input_path: Path, output_path: Path) -> bool:
    """
    Apply the exact same adaptive geometric cropping that was used for speaker 2 phan 4 done.
    
    Adapts cropping coordinates based on video dimensions using proven parameters:
    - For 400x200 videos: 25%-75% width, 5%-45% height
    - For 1280x720 videos: 30%-70% width, 45%-75% height (face region)
    - For 1620x1080+ videos: 35%-65% width, 40%-70% height (face region)
    """
    
    logger.info(f"🎬 Processing: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error(f"No frames found in video: {input_path}")
        return False
    
    # Get video dimensions
    h, w = frames[0].shape[:2]
    logger.info(f"📏 Video dimensions: {w}×{h}")
    
    # ADAPTIVE GEOMETRIC CROPPING - EXACT SAME LOGIC AS SPEAKER 2 PHAN 4 DONE
    if w == 400 and h == 200:
        # Small videos - direct lip region (same as original)
        crop_x1 = int(w * 0.25)  # 25% from left
        crop_x2 = int(w * 0.75)  # 75% from left
        crop_y1 = int(h * 0.05)  # 5% from top
        crop_y2 = int(h * 0.45)  # 45% from top
        logger.info("📐 Using 400x200 crop coordinates (25%-75% width, 5%-45% height)")
    elif w == 1280 and h == 720:
        # 720p videos - face region focusing on lower face (same as original)
        crop_x1 = int(w * 0.30)  # 30% from left
        crop_x2 = int(w * 0.70)  # 70% from left
        crop_y1 = int(h * 0.45)  # 45% from top (lower face)
        crop_y2 = int(h * 0.75)  # 75% from top
        logger.info("📐 Using 1280x720 crop coordinates (30%-70% width, 45%-75% height)")
    elif w >= 1600 and h >= 1000:
        # Large videos - face region then lip area (same as original)
        crop_x1 = int(w * 0.35)  # 35% from left
        crop_x2 = int(w * 0.65)  # 65% from left
        crop_y1 = int(h * 0.40)  # 40% from top (lower face)
        crop_y2 = int(h * 0.70)  # 70% from top
        logger.info("📐 Using large video crop coordinates (35%-65% width, 40%-70% height)")
    else:
        # Default proportional cropping (same as original)
        crop_x1 = int(w * 0.30)
        crop_x2 = int(w * 0.70)
        crop_y1 = int(h * 0.40)
        crop_y2 = int(h * 0.70)
        logger.info("📐 Using default proportional crop coordinates (30%-70% width, 40%-70% height)")
    
    logger.info(f"🎯 Crop region: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
    logger.info(f"📏 Crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1} pixels")
    
    # Process frames with direct cropping (EXACT SAME AS ORIGINAL)
    processed_frames = []
    
    for frame in frames:
        # DIRECT CROP - no detection, just extract the region
        cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Convert to grayscale
        gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
        
        # Resize to 96×64
        resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        processed_frames.append(resized)
    
    logger.info(f"📊 Processed {len(processed_frames)} frames")
    
    # Sample to exactly 32 frames (EXACT SAME AS ORIGINAL)
    if len(processed_frames) >= 32:
        # Sample uniformly
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad by repeating frames
        sampled_frames = processed_frames * (32 // len(processed_frames) + 1)
        sampled_frames = sampled_frames[:32]
    
    logger.info(f"📊 Sampled to {len(sampled_frames)} frames")
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS (EXACT SAME AS ORIGINAL)
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    logger.info(f"✅ Saved: {output_path}")
    return True

def find_all_videos(base_dir: Path) -> List[Path]:
    """Find all video files in the directory structure."""
    video_files = []
    for ext in ['.mp4', '.avi', '.mov']:
        video_files.extend(base_dir.rglob(f'*{ext}'))
    
    # Filter out already processed files
    video_files = [f for f in video_files if not f.name.startswith("processed_") and not f.name.startswith("lips_")]
    
    return sorted(video_files)

def main():
    # Define paths
    input_dir = Path("speaker_sets/full_speaker_sets_top7")
    output_dir = Path("processed_videos/top7_phan_pipeline")
    
    if not input_dir.exists():
        logger.error(f"Input directory not found: {input_dir}")
        sys.exit(1)
    
    # Find all videos
    video_files = find_all_videos(input_dir)
    
    if not video_files:
        logger.error(f"No videos found in {input_dir}")
        sys.exit(1)
    
    logger.info(f"🎬 Found {len(video_files)} videos to process")
    
    print("🎯 TOP7 VIDEOS - SPEAKER 2 PHAN PIPELINE REPLICATION")
    print("="*70)
    print("Replicating the exact preprocessing approach used for:")
    print("• 'speaker 2 phan 4 done' dataset (80 videos, 1280×720)")
    print("• Proven successful adaptive geometric cropping")
    print("")
    print("Strategy:")
    print("• Adaptive geometric cropping (dimension-aware)")
    print("• 400x200: 25%-75% width, 5%-45% height")
    print("• 1280x720: 30%-70% width, 45%-75% height")
    print("• 1620x1080+: 35%-65% width, 40%-70% height")
    print("• Convert to grayscale")
    print("• Resize to 96×64 pixels")
    print("• Sample to exactly 32 frames")
    print("• Save as MP4 at 15 FPS")
    print("="*70)
    
    # Process all videos
    success_count = 0
    
    for i, video_path in enumerate(video_files, 1):
        print(f"\n🎬 Processing video {i}/{len(video_files)}")
        
        # Create output path maintaining directory structure
        relative_path = video_path.relative_to(input_dir)
        output_path = output_dir / relative_path.parent / f"processed_{relative_path.stem}.mp4"
        
        # Process the video
        success = adaptive_lip_crop_video(video_path, output_path)
        
        if success:
            success_count += 1
            print(f"✅ Completed: {output_path.name}")
        else:
            print(f"❌ Failed: {video_path.name}")
    
    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{len(video_files)} videos")
    print(f"📁 Output directory: {output_dir}")
    
    if success_count < len(video_files):
        print(f"⚠️  {len(video_files) - success_count} videos failed")
    
    # Verify output format
    if success_count > 0:
        print(f"\n🔍 VERIFYING OUTPUT FORMAT:")
        sample_output = list(output_dir.rglob("*.mp4"))[0]
        cap = cv2.VideoCapture(str(sample_output))
        if cap.isOpened():
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            ret, frame = cap.read()
            channels = 1 if len(frame.shape) == 2 else frame.shape[2]
            
            cap.release()
            
            print(f"📊 Sample output verification:")
            print(f"   Dimensions: {width}×{height} ({'✅' if width == 96 and height == 64 else '❌'})")
            print(f"   Frame count: {frame_count} ({'✅' if frame_count == 32 else '❌'})")
            print(f"   FPS: {fps} ({'✅' if fps == 15.0 else '❌'})")
            print(f"   Channels: {channels} ({'✅' if channels == 1 else '❌'})")
            print(f"   Format: MP4 ✅")
            
            if width == 96 and height == 64 and frame_count == 32 and channels == 1:
                print(f"🎉 OUTPUT FORMAT MATCHES SPEAKER 2 PHAN 4 DONE SPECIFICATIONS!")
            else:
                print(f"⚠️  Output format differs from expected specifications")

if __name__ == "__main__":
    main()
