#!/usr/bin/env python3
"""
Process the specific 1.mp4 video for lip reading model testing.
"""

import cv2
import numpy as np
from pathlib import Path

def process_1_video():
    """Process the 1.mp4 video to model requirements."""
    
    input_path = "data/quick tests/second round/third round/fourth round/fifth round/1.mp4"
    output_path = "test_1_processed.mp4"
    
    print(f"🎬 Processing: {input_path}")
    print(f"📁 Output: {output_path}")
    
    # Load video
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"❌ Error: Cannot open video {input_path}")
        return False
    
    # Get video properties
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"📏 Input: {width}×{height}, {total_frames} frames, {fps:.1f} FPS")
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    
    cap.release()
    
    if not frames:
        print("❌ Error: No frames found in video")
        return False
    
    print(f"📊 Read {len(frames)} frames")
    
    # For portrait video (1080×1920), focus on center region for mouth
    # Use geometric approach since this is a portrait selfie-style video
    roi_x = int(width * 0.15)   # 15% from left
    roi_y = int(height * 0.35)  # 35% from top (mouth region)
    roi_w = int(width * 0.7)    # 70% of width
    roi_h = int(height * 0.25)  # 25% of height
    
    print(f"📐 Using mouth ROI: {roi_x},{roi_y} {roi_w}×{roi_h}")
    
    # Process frames
    processed_frames = []
    
    for frame in frames:
        # Extract ROI (mouth region)
        roi = frame[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
        
        # Resize to 96×64
        resized = cv2.resize(roi, (96, 64), interpolation=cv2.INTER_AREA)
        
        # Convert to grayscale
        gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
        
        processed_frames.append(gray)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    print(f"✅ Processed: {len(sampled_frames)} frames, 96×64 pixels, grayscale")
    
    # Save as MP4 (grayscale)
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    # Verify output
    verify_cap = cv2.VideoCapture(output_path)
    if verify_cap.isOpened():
        out_frames = int(verify_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        out_width = int(verify_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        out_height = int(verify_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        out_fps = verify_cap.get(cv2.CAP_PROP_FPS)
        verify_cap.release()
        
        print(f"📊 Output: {out_width}×{out_height}, {out_frames} frames, {out_fps:.1f} FPS")
        
        # Check format compliance
        format_ok = (out_width == 96 and out_height == 64 and out_frames == 32)
        print(f"🎯 Format compliant: {format_ok}")
        
        return True
    else:
        print("❌ Could not verify output video")
        return False

if __name__ == "__main__":
    print("🎯 PROCESSING 1.MP4 FOR LIP READING MODEL")
    print("="*50)
    print("Target format: 96×64, 32 frames, grayscale, MP4")
    print("="*50)
    
    success = process_1_video()
    
    if success:
        print("\n🎉 SUCCESS! Video processed and ready for model inference.")
    else:
        print("\n❌ FAILED! Could not process video.")
